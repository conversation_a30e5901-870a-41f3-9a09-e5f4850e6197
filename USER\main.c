#include "gd32f4xx.h"
#include "gd32f470v_start.h"
#include "systick.h"
#include "usart.h"
#include <stdio.h>
#include <string.h>
#include "time.h"
#include "rtc.h"
#include "adc.h"
#include "dma.h"
#include "gpio.h"
#include "dac.h"



#include "API_TNRG.h"
#include "API_W5500.h"
#include "API_LAN_DATA_Process.h"

/*                             */
#include "w5500.h"
#include "w5500_conf.h"
#include "w5500_conf.h"

#include "utility.h"
#include "dhcp.h"
#include "tcp_client.h"
#include "socket.h"




void key_funtion(void);
void ADC_RunTask(void);
// void print_system_status(void);  // ???????????
// void simple_uart_test(void);     // ???????????????
 
extern LAN_PARA Lan_Para;
uint8_t Soft_Ver[3]={1,0,3};
uint8_t HardWare_Ver[2]= {7,5};

uint8_t dhcp_ok = 0;

int main(void)
{
	/* set the NVIC vector table base address to APP code area */
   nvic_vector_table_set(NVIC_VECTTAB_FLASH, 0x0);
 /* enable global interrupt, the same as __set_PRIMASK(0) */
  __enable_irq();
    systick_config();
	  GPIO_Init();

	  USART0_Init();

	  printf("\r\n=== W5500 Network Test ===\r\n");
	  printf("Software Version: %d.%d.%d\r\n", Soft_Ver[0], Soft_Ver[1], Soft_Ver[2]);
	  printf("System Clock: %dMHz\r\n", rcu_clock_freq_get(CK_SYS)/1000000);
	  printf("Initializing W5500 network...\r\n");

	  // ??????W5500????H??????????
  	TIMER6_Init();  // W5500?????????
	  printf("TIMER6 initialized\r\n");

	  // ????????????
	  //TIMER3_Init();
	  //printf("TIMER3 initialized\r\n");
	  //RTC_Init();
	  //printf("RTC initialized\r\n");

	  TIMER1_Init();  // ??????????????????
	  printf("TIMER1 initialized\r\n");

  	// SPI1_Init();  // 注释掉错误的SPI1初始化，W5500使用SPI0
	  // printf("SPI1 initialized\r\n");
	  printf("W5500 SPI0 will be initialized in API_Init_LAN\r\n");

	  // ????RNG????????MAC???
	  //API_RNG_Init();
	  //printf("RNG initialized\r\n");

	  timer_enable(TIMER1);
		API_Init_LAN();
		printf("LAN parameters initialized\r\n");
		API_W5500_Reset();
		printf("W5500 reset completed\r\n");
		LED_G_OFF();LED_B_OFF();LED_R_OFF();
		printf("W5500 network test ready!\r\n");
		printf("=== Test Configuration ===\r\n");
		printf("- DHCP: Enabled (auto IP assignment)\r\n");
		printf("- Test Server: ***********0:8080\r\n");
		printf("- MAC: 02:08:DC:11:22:33\r\n");
		printf("- Gateway: ***********\r\n");
		printf("==========================\r\n");
		printf("Starting network loop...\r\n");
		printf("Note: Connect network cable to see DHCP process\r\n");
		printf("      Or wait 30s for static IP fallback\r\n\r\n");
    while(1)
		{
				// W5500???????????
				API_do_tcp_client();

				// 20ms??????? - ??????????????
				if(g_tTimeSign.bTic20msSign)
				{
						API_ExecuteSeverCMD();
						g_tTimeSign.bTic20msSign = FALSE;
				}

				// 1??????? - ?????????
				if(g_tTimeSign.bTic1secSign)
				{
				  	API_PHY_Check();  // ?????????????
				  	// ???????????????????????
				  	printf("[NET] DHCP: %s, PHY: %s\r\n",
				  	       dhcp_ok ? "OK" : "FAIL",
				  	       "Connected");  // ??PHY?????
						g_tTimeSign.bTic1secSign = FALSE;
				}
    }
}

void print_system_status(void)
{
	static uint32_t status_counter = 0;
	status_counter++;

	// ?10????????????
	if(status_counter % 10 == 0) {
		printf("\r\n=== System Status (Uptime: %d seconds) ===\r\n", status_counter);
		printf("DHCP Status: %s\r\n", dhcp_ok ? "OK" : "Failed");
		printf("Network IP: %d.%d.%d.%d\r\n",
		       Lan_Para.IP_B0[0], Lan_Para.IP_B0[1], Lan_Para.IP_B0[2], Lan_Para.IP_B0[3]);
		printf("Server IP: %d.%d.%d.%d:%d\r\n",
		       Lan_Para.IP_S[0], Lan_Para.IP_S[1], Lan_Para.IP_S[2], Lan_Para.IP_S[3], Lan_Para.Port_S);
		printf("Connection Status: %s\r\n", Lan_Para.f_Connect ? "Connected" : "Disconnected");
		printf("Free Heap: %d bytes\r\n", 0x20000 - (uint32_t)&status_counter); // ????????????
		printf("=======================================\r\n\r\n");
	} else {
		// ????????????
		printf("[HEARTBEAT] System running... (%d)\r\n", status_counter);
	}
}


