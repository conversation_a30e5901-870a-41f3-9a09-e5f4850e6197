#include "gd32f4xx.h"
#include "gd32f470v_start.h"
#include "systick.h"
#include "usart.h"
#include <stdio.h>
#include <string.h>
#include "time.h"
#include "rtc.h"
#include "adc.h"
#include "dma.h"
#include "gpio.h"
#include "dac.h"



#include "API_TNRG.h"
#include "API_W5500.h"
#include "API_LAN_DATA_Process.h"

/*                             */
#include "w5500.h"
#include "w5500_conf.h"
#include "w5500_conf.h"

#include "utility.h"
#include "dhcp.h"
#include "tcp_client.h"
#include "socket.h"




void key_funtion(void);
void ADC_RunTask(void);
 
extern LAN_PARA Lan_Para;
uint8_t Soft_Ver[3]={1,0,3};
uint8_t HardWare_Ver[2]= {7,5};

uint8_t dhcp_ok = 0;

int main(void)
{
	/* set the NVIC vector table base address to APP code area */
 nvic_vector_table_set(NVIC_VECTTAB_FLASH, 0x0);
 /* enable global interrupt, the same as __set_PRIMASK(0) */
 __enable_irq();
    systick_config();  
	  GPIO_Init(); 

	  USART0_Init();
  	TIMER6_Init();
	  TIMER3_Init();
	  RTC_Init();
	  TIMER1_Init();
  	SPI1_Init();
	  API_RNG_Init();//���������������������MAC��ַ
	  timer_enable(TIMER1);  
		API_Init_LAN();//���ڳ�ʼ��TWXj
		API_W5500_Reset();
		LED_G_OFF();LED_B_OFF();LED_R_OFF();
    while(1)
		{		                                   
				API_do_tcp_client();        //��ѯ���ڵĸ�������           
				if(g_tTimeSign.bTic20msSign)                       /* 20ms */
				{			
						API_ExecuteSeverCMD();//ִ�з���������
					//send(SOCK_TCPC,SendBuff,20); //���ڷ������ݵ��ú���
						g_tTimeSign.bTic20msSign = FALSE;
				}

				if(g_tTimeSign.bTic1secSign)                      /* 1sec */
				{
				  	API_PHY_Check();//�������
						g_tTimeSign.bTic1secSign = FALSE;
				}	
    }
}


