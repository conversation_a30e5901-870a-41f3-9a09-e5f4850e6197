.\objects\bayesfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BayesFunctions\BayesFunctions.c
.\objects\bayesfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BayesFunctions\arm_gaussian_naive_bayes_predict_f32.c
.\objects\bayesfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/bayes_functions.h
.\objects\bayesfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\arm_math_types.h
.\objects\bayesfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h
.\objects\bayesfunctions.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\bayesfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h
.\objects\bayesfunctions.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
.\objects\bayesfunctions.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\math.h
.\objects\bayesfunctions.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\float.h
.\objects\bayesfunctions.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\limits.h
.\objects\bayesfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\arm_math_memory.h
.\objects\bayesfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/none.h
.\objects\bayesfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/utils.h
.\objects\bayesfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/statistics_functions.h
.\objects\bayesfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/basic_math_functions.h
.\objects\bayesfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/fast_math_functions.h
