<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\Objects\API_LAN_V7_6.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\Objects\API_LAN_V7_6.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060960: Last Updated: Thu Aug 07 19:31:19 2025
<BR><P>
<H3>Maximum Stack Usage =       2112 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
USART2_IRQHandler &rArr; UART_IDLECallBack &rArr; FML_USART_RecvTask &rArr; ReadBytesToBuffer
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[1f]">ADC_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1f]">ADC_IRQHandler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[1f]">ADC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[7]">BusFault_Handler</a> from gd32f4xx_it.o(i.BusFault_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[23]">CAN0_EWMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[21]">CAN0_RX0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[22]">CAN0_RX1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[20]">CAN0_TX_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4f]">CAN1_EWMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4d]">CAN1_RX0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4e]">CAN1_RX1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4c]">CAN1_TX_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5b]">DCI_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[18]">DMA0_Channel0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[19]">DMA0_Channel1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1a]">DMA0_Channel2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1b]">DMA0_Channel3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1c]">DMA0_Channel4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1d]">DMA0_Channel5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[1e]">DMA0_Channel6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3c]">DMA0_Channel7_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[45]">DMA1_Channel0_IRQHandler</a> from gd32f4xx_it.o(i.DMA1_Channel0_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[46]">DMA1_Channel1_IRQHandler</a> from gd32f4xx_it.o(i.DMA1_Channel1_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[47]">DMA1_Channel2_IRQHandler</a> from gd32f4xx_it.o(i.DMA1_Channel2_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[48]">DMA1_Channel3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[49]">DMA1_Channel4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[51]">DMA1_Channel5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[52]">DMA1_Channel6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[53]">DMA1_Channel7_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[a]">DebugMon_Handler</a> from gd32f4xx_it.o(i.DebugMon_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4a]">ENET_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4b]">ENET_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3d]">EXMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[13]">EXTI0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[35]">EXTI10_15_IRQHandler</a> from gd32f4xx_it.o(i.EXTI10_15_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[14]">EXTI1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[15]">EXTI2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[16]">EXTI3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[17]">EXTI4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[24]">EXTI5_9_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[11]">FMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5d]">FPU_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5]">HardFault_Handler</a> from gd32f4xx_it.o(i.HardFault_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2d]">I2C0_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2c]">I2C0_EV_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2f]">I2C1_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2e]">I2C1_EV_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[56]">I2C2_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[55]">I2C2_EV_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[65]">IPA_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[e]">LVD_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[6]">MemManage_Handler</a> from gd32f4xx_it.o(i.MemManage_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4]">NMI_Handler</a> from gd32f4xx_it.o(i.NMI_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[b]">PendSV_Handler</a> from gd32f4xx_it.o(i.PendSV_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[12]">RCU_CTC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[36]">RTC_Alarm_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[10]">RTC_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3]">Reset_Handler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3e]">SDIO_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[30]">SPI0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[31]">SPI1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[40]">SPI2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[60]">SPI3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[61]">SPI4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[62]">SPI5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[9]">SVC_Handler</a> from gd32f4xx_it.o(i.SVC_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[c]">SysTick_Handler</a> from gd32f4xx_it.o(i.SysTick_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[67]">SystemInit</a> from system_gd32f4xx.o(i.SystemInit) referenced from startup_gd32f450_470.o(.text)
 <LI><a href="#[f]">TAMPER_STAMP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[25]">TIMER0_BRK_TIMER8_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[28]">TIMER0_Channel_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[27]">TIMER0_TRG_CMT_TIMER10_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[26]">TIMER0_UP_TIMER9_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[29]">TIMER1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2a]">TIMER2_IRQHandler</a> from gd32f4xx_it.o(i.TIMER2_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2b]">TIMER3_IRQHandler</a> from gd32f4xx_it.o(i.TIMER3_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3f]">TIMER4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[43]">TIMER5_DAC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[44]">TIMER6_IRQHandler</a> from gd32f4xx_it.o(i.TIMER6_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[38]">TIMER7_BRK_TIMER11_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3b]">TIMER7_Channel_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3a]">TIMER7_TRG_CMT_TIMER13_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[39]">TIMER7_UP_TIMER12_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[64]">TLI_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[63]">TLI_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5c]">TRNG_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[41]">UART3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[42]">UART4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5e]">UART6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5f]">UART7_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[32]">USART0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[33]">USART1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[34]">USART2_IRQHandler</a> from gd32f4xx_it.o(i.USART2_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[54]">USART5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[50]">USBFS_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[37]">USBFS_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[58]">USBHS_EP1_In_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[57]">USBHS_EP1_Out_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5a]">USBHS_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[59]">USBHS_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[8]">UsageFault_Handler</a> from gd32f4xx_it.o(i.UsageFault_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[d]">WWDGT_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[68]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_gd32f450_470.o(.text)
 <LI><a href="#[6a]">_sputc</a> from printfa.o(i._sputc) referenced from printfa.o(i.__0sprintf)
 <LI><a href="#[0]">default_ip_assign</a> from dhcp.o(i.default_ip_assign) referenced 2 times from dhcp.o(.data)
 <LI><a href="#[2]">default_ip_conflict</a> from dhcp.o(i.default_ip_conflict) referenced 2 times from dhcp.o(.data)
 <LI><a href="#[1]">default_ip_update</a> from dhcp.o(i.default_ip_update) referenced 2 times from dhcp.o(.data)
 <LI><a href="#[69]">fputc</a> from usart.o(i.fputc) referenced from printfa.o(i.__0printf)
 <LI><a href="#[66]">main</a> from main.o(i.main) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[68]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(.text)
</UL>
<P><STRONG><a name="[126]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[6b]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[7c]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[127]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[128]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[129]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[12a]"></a>__rt_lib_shutdown_fini</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry12b.o(.ARM.Collect$$$$0000000E))

<P><STRONG><a name="[12b]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[12c]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$00000011))

<P><STRONG><a name="[3]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>ADC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>CAN0_EWMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>CAN0_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>CAN0_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>CAN0_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>CAN1_EWMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4d]"></a>CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5b]"></a>DCI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>DMA0_Channel0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>DMA0_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>DMA0_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>DMA0_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>DMA0_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>DMA0_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>DMA0_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>DMA0_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>DMA1_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>DMA1_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>DMA1_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>DMA1_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>DMA1_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>ENET_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>ENET_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>EXMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>EXTI5_9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>FMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5d]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>I2C0_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>I2C0_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[65]"></a>IPA_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>LVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>RCU_CTC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>SDIO_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>SPI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[60]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[61]"></a>SPI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[62]"></a>SPI5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>TAMPER_STAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>TIMER0_BRK_TIMER8_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>TIMER0_Channel_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>TIMER0_TRG_CMT_TIMER10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>TIMER0_UP_TIMER9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>TIMER1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>TIMER4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>TIMER5_DAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>TIMER7_BRK_TIMER11_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>TIMER7_Channel_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>TIMER7_TRG_CMT_TIMER13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>TIMER7_UP_TIMER12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[64]"></a>TLI_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[63]"></a>TLI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>TRNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>UART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5e]"></a>UART6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5f]"></a>UART7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>USART0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>USART1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>USART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>USBFS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>USBFS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>USBHS_EP1_In_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>USBHS_EP1_Out_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>USBHS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>USBHS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>WWDGT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[d0]"></a>__aeabi_memcpy</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_WriteCpuFlash
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FML_USART_RecvTask
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadBytesToBuffer
</UL>

<P><STRONG><a name="[12d]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[12e]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[6e]"></a>__aeabi_memset</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset$wrapper
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>

<P><STRONG><a name="[12f]"></a>__aeabi_memset4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[130]"></a>__aeabi_memset8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[6d]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[7e]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_29CMD
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_28CMD
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_26CMD
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_24CMD
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_23CMD
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_21CMD
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FML_USART_RecvTask
</UL>

<P><STRONG><a name="[131]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[6f]"></a>_memset$wrapper</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[11f]"></a>strlen</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, strlen.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_DHCP_REQUEST
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_DHCP_DISCOVER
</UL>

<P><STRONG><a name="[120]"></a>strcpy</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, strcpy.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_DHCP_REQUEST
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_DHCP_DISCOVER
</UL>

<P><STRONG><a name="[132]"></a>__aeabi_uidiv</STRONG> (Thumb, 0 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)

<P><STRONG><a name="[105]"></a>__aeabi_uidivmod</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[70]"></a>__aeabi_uldivmod</STRONG> (Thumb, 98 bytes, Stack size 40 bytes, uldiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[133]"></a>__I$use$fp</STRONG> (Thumb, 0 bytes, Stack size 48 bytes, iusefp.o(.text), UNUSED)

<P><STRONG><a name="[73]"></a>__aeabi_dadd</STRONG> (Thumb, 322 bytes, Stack size 48 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_lasr
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[77]"></a>__aeabi_dsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[78]"></a>__aeabi_drsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[79]"></a>__aeabi_dmul</STRONG> (Thumb, 228 bytes, Stack size 48 bytes, dmul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[7a]"></a>__aeabi_ddiv</STRONG> (Thumb, 222 bytes, Stack size 32 bytes, ddiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[7b]"></a>__aeabi_d2ulz</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, dfixul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[102]"></a>__aeabi_cdrcmple</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, cdrcmple.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[6c]"></a>__scatterload</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[134]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[72]"></a>__aeabi_llsl</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>

<P><STRONG><a name="[135]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[71]"></a>__aeabi_llsr</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, llushr.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>

<P><STRONG><a name="[136]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[74]"></a>__aeabi_lasr</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[137]"></a>_ll_sshift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)

<P><STRONG><a name="[76]"></a>_double_round</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, depilogue.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[75]"></a>_double_epilogue</STRONG> (Thumb, 156 bytes, Stack size 32 bytes, depilogue.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[138]"></a>__decompress</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[139]"></a>__decompress1</STRONG> (Thumb, 86 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[92]"></a>API_ACK_20CMD</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, api_lan_data_process .o(i.API_ACK_20CMD))
<BR><BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ExecuteSeverCMD
</UL>

<P><STRONG><a name="[7d]"></a>API_ACK_21CMD</STRONG> (Thumb, 96 bytes, Stack size 32 bytes, api_lan_data_process .o(i.API_ACK_21CMD))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = API_ACK_21CMD &rArr; send &rArr; send_data_processing &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16_USB
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Printf_Hex
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ExecuteSeverCMD
</UL>

<P><STRONG><a name="[83]"></a>API_ACK_22CMD</STRONG> (Thumb, 184 bytes, Stack size 32 bytes, api_lan_data_process .o(i.API_ACK_22CMD))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = API_ACK_22CMD &rArr; send &rArr; send_data_processing &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_EraseCpuFlash
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16_USB
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Printf_Hex
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ExecuteSeverCMD
</UL>

<P><STRONG><a name="[85]"></a>API_ACK_23CMD</STRONG> (Thumb, 218 bytes, Stack size 32 bytes, api_lan_data_process .o(i.API_ACK_23CMD))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = API_ACK_23CMD &rArr; send &rArr; send_data_processing &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_WriteCpuFlash
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_CmpCpuFlash
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16_USB
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Printf_Hex
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ExecuteSeverCMD
</UL>

<P><STRONG><a name="[88]"></a>API_ACK_24CMD</STRONG> (Thumb, 154 bytes, Stack size 32 bytes, api_lan_data_process .o(i.API_ACK_24CMD))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = API_ACK_24CMD &rArr; USER_EEPROM_WriteByte &rArr; EEPROM_SPI_WriteBuffer &rArr; EEPROM_SPI_WritePage &rArr; EEPROM_SPI_WaitStandbyState &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16_USB
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USER_EEPROM_WriteByte
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysReset_Condition
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Printf_Hex
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ExecuteSeverCMD
</UL>

<P><STRONG><a name="[93]"></a>API_ACK_25CMD</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, api_lan_data_process .o(i.API_ACK_25CMD))
<BR><BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ExecuteSeverCMD
</UL>

<P><STRONG><a name="[8b]"></a>API_ACK_26CMD</STRONG> (Thumb, 164 bytes, Stack size 32 bytes, api_lan_data_process .o(i.API_ACK_26CMD))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = API_ACK_26CMD &rArr; send &rArr; send_data_processing &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16_USB
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Printf_Hex
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ExecuteSeverCMD
</UL>

<P><STRONG><a name="[94]"></a>API_ACK_27CMD</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, api_lan_data_process .o(i.API_ACK_27CMD))
<BR><BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ExecuteSeverCMD
</UL>

<P><STRONG><a name="[8c]"></a>API_ACK_28CMD</STRONG> (Thumb, 108 bytes, Stack size 32 bytes, api_lan_data_process .o(i.API_ACK_28CMD))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = API_ACK_28CMD &rArr; send &rArr; send_data_processing &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16_USB
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Printf_Hex
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ExecuteSeverCMD
</UL>

<P><STRONG><a name="[8d]"></a>API_ACK_29CMD</STRONG> (Thumb, 182 bytes, Stack size 32 bytes, api_lan_data_process .o(i.API_ACK_29CMD))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = API_ACK_29CMD &rArr; send &rArr; send_data_processing &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16_USB
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Printf_Hex
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ExecuteSeverCMD
</UL>

<P><STRONG><a name="[8e]"></a>API_Chose_TS5A3359_GAIN</STRONG> (Thumb, 98 bytes, Stack size 8 bytes, gpio.o(i.API_Chose_TS5A3359_GAIN))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = API_Chose_TS5A3359_GAIN
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>

<P><STRONG><a name="[91]"></a>API_ExecuteSeverCMD</STRONG> (Thumb, 260 bytes, Stack size 8 bytes, api_lan_data_process .o(i.API_ExecuteSeverCMD))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = API_ExecuteSeverCMD &rArr; API_Write_SeverInfo &rArr; API_W5500_Reset &rArr; DHCP_run &rArr; check_DHCP_timeout &rArr; send_DHCP_REQUEST &rArr; sendto &rArr; send_data_processing &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Reset
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_SeverInfo
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_29CMD
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_28CMD
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_27CMD
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_26CMD
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_25CMD
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_24CMD
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_23CMD
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_22CMD
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_21CMD
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_20CMD
</UL>
<BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[97]"></a>API_Init_LAN</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, api_w5500.o(i.API_Init_LAN))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = API_Init_LAN &rArr; API_W5500_SPI0_Init &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_SPI0_Init
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_GPIO_Init
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Init_Net_Parameters
</UL>
<BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[9a]"></a>API_Init_Net_Parameters</STRONG> (Thumb, 212 bytes, Stack size 16 bytes, api_w5500.o(i.API_Init_Net_Parameters))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = API_Init_Net_Parameters &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Init_LAN
</UL>

<P><STRONG><a name="[9b]"></a>API_LED_GPIO</STRONG> (Thumb, 72 bytes, Stack size 8 bytes, gpio.o(i.API_LED_GPIO))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = API_LED_GPIO &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>

<P><STRONG><a name="[9f]"></a>API_PHY_Check</STRONG> (Thumb, 174 bytes, Stack size 8 bytes, api_w5500.o(i.API_PHY_Check))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = API_PHY_Check &rArr; getPHYStatus &rArr; IINCHIP_READ &rArr; IINCHIP_SpiSendData &rArr; API_SPI0_Send_Read_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysReset_Condition
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;getPHYStatus
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[82]"></a>API_Printf_Hex</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, usart.o(i.API_Printf_Hex))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = API_Printf_Hex &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_do_tcp_client
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_29CMD
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_28CMD
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_26CMD
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_24CMD
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_23CMD
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_22CMD
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_21CMD
</UL>

<P><STRONG><a name="[a1]"></a>API_Process_Lan_Data</STRONG> (Thumb, 256 bytes, Stack size 8 bytes, api_lan_data_process .o(i.API_Process_Lan_Data))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = API_Process_Lan_Data &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_do_tcp_client
</UL>

<P><STRONG><a name="[a2]"></a>API_SPI0_Send_Read_Byte</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, api_w5500.o(i.API_SPI0_Send_Read_Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = API_SPI0_Send_Read_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_flag_get
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_transmit
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_receive
</UL>
<BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_SpiSendData
</UL>

<P><STRONG><a name="[99]"></a>API_W5500_GPIO_Init</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, api_w5500.o(i.API_W5500_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = API_W5500_GPIO_Init &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Init_LAN
</UL>

<P><STRONG><a name="[95]"></a>API_W5500_Reset</STRONG> (Thumb, 438 bytes, Stack size 40 bytes, api_w5500.o(i.API_W5500_Reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = API_W5500_Reset &rArr; DHCP_run &rArr; check_DHCP_timeout &rArr; send_DHCP_REQUEST &rArr; sendto &rArr; send_data_processing &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;socket_buf_init
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_w5500_mac
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setSUBR
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setSIPR
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setGAR
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reset_w5500
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;getSHAR
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;getPHYStatus
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PHY_check
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHCP_run
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ExecuteSeverCMD
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_SeverInfo
</UL>

<P><STRONG><a name="[98]"></a>API_W5500_SPI0_Init</STRONG> (Thumb, 148 bytes, Stack size 32 bytes, api_w5500.o(i.API_W5500_SPI0_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = API_W5500_SPI0_Init &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_init
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Init_LAN
</UL>

<P><STRONG><a name="[96]"></a>API_Write_SeverInfo</STRONG> (Thumb, 244 bytes, Stack size 24 bytes, api_lan_data_process .o(i.API_Write_SeverInfo))
<BR><BR>[Stack]<UL><LI>Max Depth = 232<LI>Call Chain = API_Write_SeverInfo &rArr; API_W5500_Reset &rArr; DHCP_run &rArr; check_DHCP_timeout &rArr; send_DHCP_REQUEST &rArr; sendto &rArr; send_data_processing &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WriteBuffer
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_ReadBuffer
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Reset
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ExecuteSeverCMD
</UL>

<P><STRONG><a name="[b5]"></a>API_do_tcp_client</STRONG> (Thumb, 296 bytes, Stack size 24 bytes, usart.o(i.API_do_tcp_client))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = API_do_tcp_client &rArr; recv &rArr; recv_data_processing &rArr; wiz_read_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;socket
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setSn_IR
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recv
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;getSn_SR
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;getSn_RX_RSR
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;getSn_IR
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disconnect
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;connect
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;close
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Process_Lan_Data
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Printf_Hex
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[7]"></a>BusFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.BusFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[7f]"></a>CRC16_USB</STRONG> (Thumb, 126 bytes, Stack size 24 bytes, my_crc.o(i.CRC16_USB))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = CRC16_USB &rArr; InvertUint16
</UL>
<BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InvertUint8
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InvertUint16
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USER_EEPROM_WriteByte
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_29CMD
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_28CMD
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_26CMD
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_24CMD
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_23CMD
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_22CMD
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_21CMD
</UL>

<P><STRONG><a name="[ac]"></a>DHCP_run</STRONG> (Thumb, 532 bytes, Stack size 16 bytes, dhcp.o(i.DHCP_run))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = DHCP_run &rArr; check_DHCP_timeout &rArr; send_DHCP_REQUEST &rArr; sendto &rArr; send_data_processing &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;socket
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;getSn_SR
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_DHCP_REQUEST
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_DHCP_DISCOVER
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reset_DHCP_timeout
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parseDHCPMSG
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_dhcp_client
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_DHCP_timeout
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_DHCP_leasedIP
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Reset
</UL>

<P><STRONG><a name="[45]"></a>DMA1_Channel0_IRQHandler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.DMA1_Channel0_IRQHandler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>DMA1_Channel1_IRQHandler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.DMA1_Channel1_IRQHandler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>DMA1_Channel2_IRQHandler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.DMA1_Channel2_IRQHandler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[c8]"></a>DRV_SPI_SwapByte</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, spi.o(i.DRV_SPI_SwapByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_flag_get
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_transmit
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_receive
</UL>
<BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WritePage
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WaitStandbyState
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_SendInstruction
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_ReadBuffer
</UL>

<P><STRONG><a name="[a]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[b3]"></a>EEPROM_SPI_ReadBuffer</STRONG> (Thumb, 98 bytes, Stack size 24 bytes, eeprom_spi.o(i.EEPROM_SPI_ReadBuffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = EEPROM_SPI_ReadBuffer &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_flag_get
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DRV_SPI_SwapByte
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_SendInstruction
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USER_EEPROM_WriteByte
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USER_EEPROM_RedeByte
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_SeverInfo
</UL>

<P><STRONG><a name="[c9]"></a>EEPROM_SPI_SendInstruction</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, eeprom_spi.o(i.EEPROM_SPI_SendInstruction))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_flag_get
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DRV_SPI_SwapByte
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
</UL>
<BR>[Called By]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_WriteEnable
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_WriteDisable
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WritePage
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WaitStandbyState
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_ReadBuffer
</UL>

<P><STRONG><a name="[ca]"></a>EEPROM_SPI_WaitStandbyState</STRONG> (Thumb, 66 bytes, Stack size 16 bytes, eeprom_spi.o(i.EEPROM_SPI_WaitStandbyState))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = EEPROM_SPI_WaitStandbyState &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DRV_SPI_SwapByte
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_SendInstruction
</UL>
<BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WritePage
</UL>

<P><STRONG><a name="[b4]"></a>EEPROM_SPI_WriteBuffer</STRONG> (Thumb, 394 bytes, Stack size 40 bytes, eeprom_spi.o(i.EEPROM_SPI_WriteBuffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = EEPROM_SPI_WriteBuffer &rArr; EEPROM_SPI_WritePage &rArr; EEPROM_SPI_WaitStandbyState &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WritePage
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USER_EEPROM_WriteByte
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_SeverInfo
</UL>

<P><STRONG><a name="[cb]"></a>EEPROM_SPI_WritePage</STRONG> (Thumb, 170 bytes, Stack size 32 bytes, eeprom_spi.o(i.EEPROM_SPI_WritePage))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = EEPROM_SPI_WritePage &rArr; EEPROM_SPI_WaitStandbyState &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_flag_get
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DRV_SPI_SwapByte
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_WriteEnable
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_WriteDisable
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WaitStandbyState
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_SendInstruction
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
</UL>
<BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WriteBuffer
</UL>

<P><STRONG><a name="[cd]"></a>EEPROM_WriteDisable</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, eeprom_spi.o(i.EEPROM_WriteDisable))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = EEPROM_WriteDisable &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_SendInstruction
</UL>
<BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WritePage
</UL>

<P><STRONG><a name="[cc]"></a>EEPROM_WriteEnable</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, eeprom_spi.o(i.EEPROM_WriteEnable))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = EEPROM_WriteEnable &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_SendInstruction
</UL>
<BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WritePage
</UL>

<P><STRONG><a name="[35]"></a>EXTI10_15_IRQHandler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.EXTI10_15_IRQHandler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[107]"></a>FLASH_If_Init</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, flash.o(i.FLASH_If_Init))
<BR><BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_EraseCpuFlash
</UL>

<P><STRONG><a name="[ce]"></a>FML_USART_RecvTask</STRONG> (Thumb, 174 bytes, Stack size 2072 bytes, usart.o(i.FML_USART_RecvTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 2096<LI>Call Chain = FML_USART_RecvTask &rArr; ReadBytesToBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadBytesToBuffer
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_IDLECallBack
</UL>

<P><STRONG><a name="[d1]"></a>GPIO_Init</STRONG> (Thumb, 90 bytes, Stack size 8 bytes, gpio.o(i.GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = GPIO_Init &rArr; Init_GPIO_TS5A339 &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_GPIO_TS5A339
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_LED_GPIO
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Chose_TS5A3359_GAIN
</UL>
<BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[5]"></a>HardFault_Handler</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.HardFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysReset_Condition
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[d3]"></a>IINCHIP_READ</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, w5500_conf.o(i.IINCHIP_READ))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = IINCHIP_READ &rArr; IINCHIP_SpiSendData &rArr; API_SPI0_Send_Read_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;iinchip_cson
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;iinchip_csoff
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_SpiSendData
</UL>
<BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_data_processing
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recv_data_processing
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;getSn_TX_FSR
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sendto
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recvfrom
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;getPHYStatus
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;socket
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recv
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;getSn_SR
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;getSn_RX_RSR
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;getSn_IR
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disconnect
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;connect
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;close
</UL>

<P><STRONG><a name="[d5]"></a>IINCHIP_SpiSendData</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, w5500_conf.o(i.IINCHIP_SpiSendData))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = IINCHIP_SpiSendData &rArr; API_SPI0_Send_Read_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Read_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wiz_read_buf
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_WRITE
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_READ
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wiz_write_buf
</UL>

<P><STRONG><a name="[d7]"></a>IINCHIP_WRITE</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, w5500_conf.o(i.IINCHIP_WRITE))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = IINCHIP_WRITE &rArr; IINCHIP_SpiSendData &rArr; API_SPI0_Send_Read_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;iinchip_cson
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;iinchip_csoff
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_SpiSendData
</UL>
<BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_data_processing
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recv_data_processing
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sendto
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recvfrom
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;socket_buf_init
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;socket
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setSn_IR
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recv
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disconnect
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;connect
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;close
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setMR
</UL>

<P><STRONG><a name="[d2]"></a>Init_GPIO_TS5A339</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, gpio.o(i.Init_GPIO_TS5A339))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = Init_GPIO_TS5A339 &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>

<P><STRONG><a name="[6]"></a>MemManage_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.MemManage_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.NMI_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[a9]"></a>PHY_check</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, w5500_conf.o(i.PHY_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = PHY_check &rArr; getPHYStatus &rArr; IINCHIP_READ &rArr; IINCHIP_SpiSendData &rArr; API_SPI0_Send_Read_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;getPHYStatus
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;close
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Reset
</UL>

<P><STRONG><a name="[b]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.PendSV_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[9]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[8a]"></a>SysReset_Condition</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, user_step.o(i.SysReset_Condition))
<BR><BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_PHY_Check
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_PeriodElapsedCallback
<LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_24CMD
</UL>

<P><STRONG><a name="[c]"></a>SysTick_Handler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.SysTick_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SysTick_Handler
</UL>
<BR>[Calls]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_decrement
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[67]"></a>SystemInit</STRONG> (Thumb, 184 bytes, Stack size 8 bytes, system_gd32f4xx.o(i.SystemInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SystemInit &rArr; system_clock_config
</UL>
<BR>[Calls]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_config
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(.text)
</UL>
<P><STRONG><a name="[dc]"></a>TIMER1_Init</STRONG> (Thumb, 118 bytes, Stack size 32 bytes, time.o(i.TIMER1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = TIMER1_Init &rArr; timer_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_init
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_deinit
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_channel_output_shadow_config
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_channel_output_pulse_value_config
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_channel_output_mode_config
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_channel_output_config
</UL>
<BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[2a]"></a>TIMER2_IRQHandler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.TIMER2_IRQHandler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>TIMER3_IRQHandler</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.TIMER3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIMER3_IRQHandler &rArr; TIM_PeriodElapsedCallback
</UL>
<BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_PeriodElapsedCallback
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_flag_get
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>TIMER6_IRQHandler</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.TIMER6_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIMER6_IRQHandler &rArr; TIM_PeriodElapsedCallback
</UL>
<BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_PeriodElapsedCallback
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_flag_get
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[e5]"></a>TIMER6_Init</STRONG> (Thumb, 80 bytes, Stack size 24 bytes, time.o(i.TIMER6_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = TIMER6_Init &rArr; nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_enable
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_init
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_enable
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_deinit
</UL>
<BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[e4]"></a>TIM_PeriodElapsedCallback</STRONG> (Thumb, 374 bytes, Stack size 8 bytes, time.o(i.TIM_PeriodElapsedCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM_PeriodElapsedCallback
</UL>
<BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysReset_Condition
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER6_IRQHandler
<LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER3_IRQHandler
</UL>

<P><STRONG><a name="[ea]"></a>UART_IDLECallBack</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, usart.o(i.UART_IDLECallBack))
<BR><BR>[Stack]<UL><LI>Max Depth = 2104<LI>Call Chain = UART_IDLECallBack &rArr; FML_USART_RecvTask &rArr; ReadBytesToBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_flag_clear
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_receive
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FML_USART_RecvTask
</UL>
<BR>[Called By]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
</UL>

<P><STRONG><a name="[ed]"></a>UART_RxCpltCallback</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, usart.o(i.UART_RxCpltCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = UART_RxCpltCallback &rArr; RecvDataHandler &rArr; AddByteToBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_receive
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RecvDataHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
</UL>

<P><STRONG><a name="[ee]"></a>USART0_Init</STRONG> (Thumb, 210 bytes, Stack size 16 bytes, usart.o(i.USART0_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 132<LI>Call Chain = USART0_Init &rArr; usart_baudrate_set &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_clock_freq_get
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_word_length_set
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_transmit_config
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_stop_bit_set
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_receive_config
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_parity_config
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_hardware_flow_rts_config
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_hardware_flow_cts_config
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_enable
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_baudrate_set
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[34]"></a>USART2_IRQHandler</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.USART2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 2112<LI>Call Chain = USART2_IRQHandler &rArr; UART_IDLECallBack &rArr; FML_USART_RecvTask &rArr; ReadBytesToBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_RxCpltCallback
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_IDLECallBack
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_flag_get
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[fb]"></a>USER_EEPROM_RedeByte</STRONG> (Thumb, 72 bytes, Stack size 24 bytes, 25lc080a.o(i.USER_EEPROM_RedeByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = USER_EEPROM_RedeByte &rArr; EEPROM_SPI_ReadBuffer &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_ReadBuffer
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USER_EEPROM_WriteByte
</UL>

<P><STRONG><a name="[89]"></a>USER_EEPROM_WriteByte</STRONG> (Thumb, 556 bytes, Stack size 80 bytes, 25lc080a.o(i.USER_EEPROM_WriteByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = USER_EEPROM_WriteByte &rArr; EEPROM_SPI_WriteBuffer &rArr; EEPROM_SPI_WritePage &rArr; EEPROM_SPI_WaitStandbyState &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WriteBuffer
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_ReadBuffer
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16_USB
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USER_EEPROM_RedeByte
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;malloc
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;free
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_24CMD
</UL>

<P><STRONG><a name="[8]"></a>UsageFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.UsageFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[fe]"></a>__0printf</STRONG> (Thumb, 22 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[13a]"></a>__1printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[81]"></a>__2printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Init
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_do_tcp_client
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Reset
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_PHY_Check
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wiz_read_buf
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_data_processing
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recv_data_processing
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recvfrom
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_w5500_mac
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reset_w5500
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PHY_check
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHCP_run
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Init_Net_Parameters
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_SeverInfo
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_29CMD
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_28CMD
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_26CMD
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_24CMD
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_23CMD
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_22CMD
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_21CMD
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;connect
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Process_Lan_Data
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Printf_Hex
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_dhcp_client
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wiz_write_buf
</UL>

<P><STRONG><a name="[13b]"></a>__c89printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[13c]"></a>printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[100]"></a>__0sprintf</STRONG> (Thumb, 34 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[13d]"></a>__1sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[11e]"></a>__2sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_DHCP_REQUEST
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_DHCP_DISCOVER
</UL>

<P><STRONG><a name="[13e]"></a>__c89sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[13f]"></a>sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[140]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[141]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[142]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)

<P><STRONG><a name="[87]"></a>bsp_CmpCpuFlash</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, flash.o(i.bsp_CmpCpuFlash))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = bsp_CmpCpuFlash
</UL>
<BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_WriteCpuFlash
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_23CMD
</UL>

<P><STRONG><a name="[84]"></a>bsp_EraseCpuFlash</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, flash.o(i.bsp_EraseCpuFlash))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = bsp_EraseCpuFlash &rArr; fmc_sector_erase &rArr; fmc_ready_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_unlock
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_sector_erase
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_lock
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_GetSector
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_If_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_22CMD
</UL>

<P><STRONG><a name="[108]"></a>bsp_GetSector</STRONG> (Thumb, 454 bytes, Stack size 0 bytes, flash.o(i.bsp_GetSector))
<BR><BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_EraseCpuFlash
</UL>

<P><STRONG><a name="[86]"></a>bsp_WriteCpuFlash</STRONG> (Thumb, 160 bytes, Stack size 32 bytes, flash.o(i.bsp_WriteCpuFlash))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = bsp_WriteCpuFlash &rArr; fmc_word_program &rArr; fmc_ready_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_word_program
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_unlock
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_lock
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_CmpCpuFlash
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_23CMD
</UL>

<P><STRONG><a name="[c6]"></a>check_DHCP_leasedIP</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, dhcp.o(i.check_DHCP_leasedIP))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = check_DHCP_leasedIP &rArr; send_DHCP_DECLINE &rArr; sendto &rArr; send_data_processing &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sendto
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_DHCP_DECLINE
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHCP_run
</UL>

<P><STRONG><a name="[c5]"></a>check_DHCP_timeout</STRONG> (Thumb, 152 bytes, Stack size 8 bytes, dhcp.o(i.check_DHCP_timeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = check_DHCP_timeout &rArr; send_DHCP_REQUEST &rArr; sendto &rArr; send_data_processing &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_DHCP_REQUEST
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_DHCP_DISCOVER
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reset_DHCP_timeout
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHCP_run
</UL>

<P><STRONG><a name="[be]"></a>close</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, socket.o(i.close))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = close &rArr; IINCHIP_WRITE &rArr; IINCHIP_SpiSendData &rArr; API_SPI0_Send_Read_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_WRITE
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_READ
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_do_tcp_client
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PHY_check
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;socket
</UL>

<P><STRONG><a name="[b8]"></a>connect</STRONG> (Thumb, 320 bytes, Stack size 32 bytes, socket.o(i.connect))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = connect &rArr; getSn_IR &rArr; IINCHIP_READ &rArr; IINCHIP_SpiSendData &rArr; API_SPI0_Send_Read_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_WRITE
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_READ
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;getSn_IR
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_do_tcp_client
</UL>

<P><STRONG><a name="[0]"></a>default_ip_assign</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, dhcp.o(i.default_ip_assign))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = default_ip_assign &rArr; setSUBR &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setSUBR
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setSIPR
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setGAR
</UL>
<BR>[Called By]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;default_ip_update
</UL>
<BR>[Address Reference Count : 1]<UL><LI> dhcp.o(.data)
</UL>
<P><STRONG><a name="[2]"></a>default_ip_conflict</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, dhcp.o(i.default_ip_conflict))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = default_ip_conflict &rArr; setSHAR &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setSHAR
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setMR
</UL>
<BR>[Address Reference Count : 1]<UL><LI> dhcp.o(.data)
</UL>
<P><STRONG><a name="[1]"></a>default_ip_update</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, dhcp.o(i.default_ip_update))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = default_ip_update &rArr; default_ip_assign &rArr; setSUBR &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;default_ip_assign
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setSHAR
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setMR
</UL>
<BR>[Address Reference Count : 1]<UL><LI> dhcp.o(.data)
</UL>
<P><STRONG><a name="[a7]"></a>delay_1ms</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, systick.o(i.delay_1ms))
<BR><BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WritePage
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_SendInstruction
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_ReadBuffer
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Reset
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reset_w5500
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PHY_check
</UL>

<P><STRONG><a name="[da]"></a>delay_decrement</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, systick.o(i.delay_decrement))
<BR><BR>[Called By]<UL><LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[bd]"></a>disconnect</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, socket.o(i.disconnect))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = disconnect &rArr; IINCHIP_WRITE &rArr; IINCHIP_SpiSendData &rArr; API_SPI0_Send_Read_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_WRITE
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_READ
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_do_tcp_client
</UL>

<P><STRONG><a name="[10a]"></a>fmc_lock</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_fmc.o(i.fmc_lock))
<BR><BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_WriteCpuFlash
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_EraseCpuFlash
</UL>

<P><STRONG><a name="[110]"></a>fmc_ready_wait</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, gd32f4xx_fmc.o(i.fmc_ready_wait))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = fmc_ready_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_state_get
</UL>
<BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_word_program
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_sector_erase
</UL>

<P><STRONG><a name="[109]"></a>fmc_sector_erase</STRONG> (Thumb, 90 bytes, Stack size 12 bytes, gd32f4xx_fmc.o(i.fmc_sector_erase))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = fmc_sector_erase &rArr; fmc_ready_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_ready_wait
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_EraseCpuFlash
</UL>

<P><STRONG><a name="[111]"></a>fmc_state_get</STRONG> (Thumb, 70 bytes, Stack size 0 bytes, gd32f4xx_fmc.o(i.fmc_state_get))
<BR><BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_ready_wait
</UL>

<P><STRONG><a name="[106]"></a>fmc_unlock</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f4xx_fmc.o(i.fmc_unlock))
<BR><BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_WriteCpuFlash
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_EraseCpuFlash
</UL>

<P><STRONG><a name="[10b]"></a>fmc_word_program</STRONG> (Thumb, 80 bytes, Stack size 16 bytes, gd32f4xx_fmc.o(i.fmc_word_program))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = fmc_word_program &rArr; fmc_ready_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_ready_wait
</UL>
<BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_WriteCpuFlash
</UL>

<P><STRONG><a name="[69]"></a>fputc</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, usart.o(i.fputc))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = fputc &rArr; usart_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_flag_get
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_transmit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0printf)
</UL>
<P><STRONG><a name="[fd]"></a>free</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, malloc.o(i.free))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = free
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USER_EEPROM_WriteByte
</UL>

<P><STRONG><a name="[11c]"></a>getIINCHIP_TxMAX</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, w5500.o(i.getIINCHIP_TxMAX))
<BR><BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sendto
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send
</UL>

<P><STRONG><a name="[a0]"></a>getPHYStatus</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, w5500_conf.o(i.getPHYStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = getPHYStatus &rArr; IINCHIP_READ &rArr; IINCHIP_SpiSendData &rArr; API_SPI0_Send_Read_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_READ
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Reset
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_PHY_Check
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PHY_check
</UL>

<P><STRONG><a name="[a8]"></a>getSHAR</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, w5500.o(i.getSHAR))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = getSHAR &rArr; wiz_read_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wiz_read_buf
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Reset
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_w5500_mac
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;makeDHCPMSG
</UL>

<P><STRONG><a name="[b9]"></a>getSn_IR</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, w5500.o(i.getSn_IR))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = getSn_IR &rArr; IINCHIP_READ &rArr; IINCHIP_SpiSendData &rArr; API_SPI0_Send_Read_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_READ
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_do_tcp_client
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;connect
</UL>

<P><STRONG><a name="[bb]"></a>getSn_RX_RSR</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, w5500.o(i.getSn_RX_RSR))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = getSn_RX_RSR &rArr; IINCHIP_READ &rArr; IINCHIP_SpiSendData &rArr; API_SPI0_Send_Read_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_READ
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_do_tcp_client
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parseDHCPMSG
</UL>

<P><STRONG><a name="[b6]"></a>getSn_SR</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, w5500.o(i.getSn_SR))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = getSn_SR &rArr; IINCHIP_READ &rArr; IINCHIP_SpiSendData &rArr; API_SPI0_Send_Read_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_READ
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_do_tcp_client
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHCP_run
</UL>

<P><STRONG><a name="[115]"></a>getSn_TX_FSR</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, w5500.o(i.getSn_TX_FSR))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = getSn_TX_FSR &rArr; IINCHIP_READ &rArr; IINCHIP_SpiSendData &rArr; API_SPI0_Send_Read_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_READ
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send
</UL>

<P><STRONG><a name="[b0]"></a>gpio_af_set</STRONG> (Thumb, 94 bytes, Stack size 20 bytes, gd32f4xx_gpio.o(i.gpio_af_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_af_set
</UL>
<BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Init
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_SPI0_Init
</UL>

<P><STRONG><a name="[8f]"></a>gpio_bit_reset</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(i.gpio_bit_reset))
<BR><BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Chose_TS5A3359_GAIN
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_WriteEnable
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_WriteDisable
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WritePage
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WaitStandbyState
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_ReadBuffer
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_do_tcp_client
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_PHY_Check
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reset_w5500
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;iinchip_csoff
</UL>

<P><STRONG><a name="[90]"></a>gpio_bit_set</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(i.gpio_bit_set))
<BR><BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Chose_TS5A3359_GAIN
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_WriteEnable
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_WriteDisable
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WritePage
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WaitStandbyState
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_ReadBuffer
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_do_tcp_client
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_PHY_Check
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reset_w5500
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_SPI0_Init
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;iinchip_cson
</UL>

<P><STRONG><a name="[9d]"></a>gpio_mode_set</STRONG> (Thumb, 78 bytes, Stack size 20 bytes, gd32f4xx_gpio.o(i.gpio_mode_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_mode_set
</UL>
<BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_GPIO_TS5A339
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_LED_GPIO
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Init
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_SPI0_Init
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_GPIO_Init
</UL>

<P><STRONG><a name="[9e]"></a>gpio_output_options_set</STRONG> (Thumb, 66 bytes, Stack size 20 bytes, gd32f4xx_gpio.o(i.gpio_output_options_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_output_options_set
</UL>
<BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_GPIO_TS5A339
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_LED_GPIO
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Init
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_SPI0_Init
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_GPIO_Init
</UL>

<P><STRONG><a name="[d4]"></a>iinchip_csoff</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, w5500_conf.o(i.iinchip_csoff))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = iinchip_csoff
</UL>
<BR>[Calls]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wiz_read_buf
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_WRITE
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_READ
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wiz_write_buf
</UL>

<P><STRONG><a name="[d6]"></a>iinchip_cson</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, w5500_conf.o(i.iinchip_cson))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = iinchip_cson
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wiz_read_buf
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_WRITE
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_READ
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wiz_write_buf
</UL>

<P><STRONG><a name="[c1]"></a>init_dhcp_client</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, dhcp.o(i.init_dhcp_client))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = init_dhcp_client &rArr; setSUBR &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setSUBR
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setSIPR
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setGAR
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHCP_run
</UL>

<P><STRONG><a name="[66]"></a>main</STRONG> (Thumb, 272 bytes, Stack size 0 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = main &rArr; API_ExecuteSeverCMD &rArr; API_Write_SeverInfo &rArr; API_W5500_Reset &rArr; DHCP_run &rArr; check_DHCP_timeout &rArr; send_DHCP_REQUEST &rArr; sendto &rArr; send_data_processing &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_clock_freq_get
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_vector_table_set
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;systick_config
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Init
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER6_Init
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER1_Init
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_do_tcp_client
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Reset
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_PHY_Check
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Init_LAN
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ExecuteSeverCMD
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_enable
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[118]"></a>makeDHCPMSG</STRONG> (Thumb, 380 bytes, Stack size 24 bytes, dhcp.o(i.makeDHCPMSG))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = makeDHCPMSG &rArr; getSHAR &rArr; wiz_read_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;getSHAR
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_DHCP_REQUEST
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_DHCP_DISCOVER
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_DHCP_DECLINE
</UL>

<P><STRONG><a name="[fc]"></a>malloc</STRONG> (Thumb, 92 bytes, Stack size 20 bytes, malloc.o(i.malloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = malloc
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USER_EEPROM_WriteByte
</UL>

<P><STRONG><a name="[e6]"></a>nvic_irq_enable</STRONG> (Thumb, 186 bytes, Stack size 24 bytes, gd32f4xx_misc.o(i.nvic_irq_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_priority_group_set
</UL>
<BR>[Called By]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER6_Init
</UL>

<P><STRONG><a name="[119]"></a>nvic_priority_group_set</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_misc.o(i.nvic_priority_group_set))
<BR><BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
</UL>

<P><STRONG><a name="[116]"></a>nvic_vector_table_set</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_misc.o(i.nvic_vector_table_set))
<BR><BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c2]"></a>parseDHCPMSG</STRONG> (Thumb, 458 bytes, Stack size 40 bytes, dhcp.o(i.parseDHCPMSG))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = parseDHCPMSG &rArr; recvfrom &rArr; wiz_read_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recvfrom
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;getSn_RX_RSR
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHCP_run
</UL>

<P><STRONG><a name="[f0]"></a>rcu_clock_freq_get</STRONG> (Thumb, 264 bytes, Stack size 84 bytes, gd32f4xx_rcu.o(i.rcu_clock_freq_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = rcu_clock_freq_get
</UL>
<BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Init
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_baudrate_set
</UL>

<P><STRONG><a name="[9c]"></a>rcu_periph_clock_enable</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_periph_clock_enable))
<BR><BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_GPIO_TS5A339
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_LED_GPIO
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Init
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER6_Init
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER1_Init
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_SPI0_Init
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_GPIO_Init
</UL>

<P><STRONG><a name="[125]"></a>rcu_periph_reset_disable</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_periph_reset_disable))
<BR><BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_deinit
</UL>

<P><STRONG><a name="[124]"></a>rcu_periph_reset_enable</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_periph_reset_enable))
<BR><BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_deinit
</UL>

<P><STRONG><a name="[bc]"></a>recv</STRONG> (Thumb, 66 bytes, Stack size 24 bytes, socket.o(i.recv))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = recv &rArr; recv_data_processing &rArr; wiz_read_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recv_data_processing
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_WRITE
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_READ
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_do_tcp_client
</UL>

<P><STRONG><a name="[11b]"></a>recv_data_processing</STRONG> (Thumb, 116 bytes, Stack size 24 bytes, w5500.o(i.recv_data_processing))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = recv_data_processing &rArr; wiz_read_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wiz_read_buf
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_WRITE
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_READ
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recv
</UL>

<P><STRONG><a name="[11a]"></a>recvfrom</STRONG> (Thumb, 486 bytes, Stack size 48 bytes, socket.o(i.recvfrom))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = recvfrom &rArr; wiz_read_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wiz_read_buf
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_WRITE
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_READ
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parseDHCPMSG
</UL>

<P><STRONG><a name="[c7]"></a>reset_DHCP_timeout</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, dhcp.o(i.reset_DHCP_timeout))
<BR><BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHCP_run
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_DHCP_timeout
</UL>

<P><STRONG><a name="[a6]"></a>reset_w5500</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, w5500_conf.o(i.reset_w5500))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = reset_w5500 &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Reset
</UL>

<P><STRONG><a name="[80]"></a>send</STRONG> (Thumb, 252 bytes, Stack size 32 bytes, socket.o(i.send))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = send &rArr; send_data_processing &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_data_processing
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;getSn_TX_FSR
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;getIINCHIP_TxMAX
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_WRITE
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_READ
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;close
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_29CMD
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_28CMD
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_26CMD
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_24CMD
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_23CMD
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_22CMD
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_21CMD
</UL>

<P><STRONG><a name="[10d]"></a>send_DHCP_DECLINE</STRONG> (Thumb, 476 bytes, Stack size 16 bytes, dhcp.o(i.send_DHCP_DECLINE))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = send_DHCP_DECLINE &rArr; sendto &rArr; send_data_processing &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sendto
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;makeDHCPMSG
</UL>
<BR>[Called By]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_DHCP_leasedIP
</UL>

<P><STRONG><a name="[c3]"></a>send_DHCP_DISCOVER</STRONG> (Thumb, 462 bytes, Stack size 40 bytes, dhcp.o(i.send_DHCP_DISCOVER))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = send_DHCP_DISCOVER &rArr; sendto &rArr; send_data_processing &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sendto
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;makeDHCPMSG
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHCP_run
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_DHCP_timeout
</UL>

<P><STRONG><a name="[c4]"></a>send_DHCP_REQUEST</STRONG> (Thumb, 838 bytes, Stack size 40 bytes, dhcp.o(i.send_DHCP_REQUEST))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = send_DHCP_REQUEST &rArr; sendto &rArr; send_data_processing &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sendto
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;makeDHCPMSG
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHCP_run
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_DHCP_timeout
</UL>

<P><STRONG><a name="[11d]"></a>send_data_processing</STRONG> (Thumb, 116 bytes, Stack size 24 bytes, w5500.o(i.send_data_processing))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = send_data_processing &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_WRITE
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_READ
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wiz_write_buf
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sendto
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send
</UL>

<P><STRONG><a name="[10c]"></a>sendto</STRONG> (Thumb, 268 bytes, Stack size 32 bytes, socket.o(i.sendto))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = sendto &rArr; send_data_processing &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_data_processing
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;getIINCHIP_TxMAX
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_WRITE
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_READ
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_DHCP_REQUEST
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_DHCP_DISCOVER
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_DHCP_DECLINE
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_DHCP_leasedIP
</UL>

<P><STRONG><a name="[af]"></a>setGAR</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, w5500.o(i.setGAR))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = setGAR &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wiz_write_buf
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Reset
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_dhcp_client
<LI><a href="#[0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;default_ip_assign
</UL>

<P><STRONG><a name="[10e]"></a>setMR</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, w5500.o(i.setMR))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = setMR &rArr; IINCHIP_WRITE &rArr; IINCHIP_SpiSendData &rArr; API_SPI0_Send_Read_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_WRITE
</UL>
<BR>[Called By]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;default_ip_update
<LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;default_ip_conflict
</UL>

<P><STRONG><a name="[10f]"></a>setSHAR</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, w5500.o(i.setSHAR))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = setSHAR &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wiz_write_buf
</UL>
<BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_w5500_mac
<LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;default_ip_update
<LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;default_ip_conflict
</UL>

<P><STRONG><a name="[ad]"></a>setSIPR</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, w5500.o(i.setSIPR))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = setSIPR &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wiz_write_buf
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Reset
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_dhcp_client
<LI><a href="#[0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;default_ip_assign
</UL>

<P><STRONG><a name="[ae]"></a>setSUBR</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, w5500.o(i.setSUBR))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = setSUBR &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wiz_write_buf
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Reset
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_dhcp_client
<LI><a href="#[0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;default_ip_assign
</UL>

<P><STRONG><a name="[ba]"></a>setSn_IR</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, w5500.o(i.setSn_IR))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = setSn_IR &rArr; IINCHIP_WRITE &rArr; IINCHIP_SpiSendData &rArr; API_SPI0_Send_Read_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_WRITE
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_do_tcp_client
</UL>

<P><STRONG><a name="[aa]"></a>set_w5500_mac</STRONG> (Thumb, 88 bytes, Stack size 24 bytes, w5500_conf.o(i.set_w5500_mac))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = set_w5500_mac &rArr; getSHAR &rArr; wiz_read_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;getSHAR
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setSHAR
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Reset
</UL>

<P><STRONG><a name="[b7]"></a>socket</STRONG> (Thumb, 192 bytes, Stack size 24 bytes, socket.o(i.socket))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = socket &rArr; close &rArr; IINCHIP_WRITE &rArr; IINCHIP_SpiSendData &rArr; API_SPI0_Send_Read_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_WRITE
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_READ
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;close
</UL>
<BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_do_tcp_client
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHCP_run
</UL>

<P><STRONG><a name="[ab]"></a>socket_buf_init</STRONG> (Thumb, 128 bytes, Stack size 24 bytes, w5500.o(i.socket_buf_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = socket_buf_init &rArr; IINCHIP_WRITE &rArr; IINCHIP_SpiSendData &rArr; API_SPI0_Send_Read_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_WRITE
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Reset
</UL>

<P><STRONG><a name="[b2]"></a>spi_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_enable))
<BR><BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_SPI0_Init
</UL>

<P><STRONG><a name="[a5]"></a>spi_i2s_data_receive</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_i2s_data_receive))
<BR><BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DRV_SPI_SwapByte
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Read_Byte
</UL>

<P><STRONG><a name="[a4]"></a>spi_i2s_data_transmit</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_i2s_data_transmit))
<BR><BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DRV_SPI_SwapByte
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Read_Byte
</UL>

<P><STRONG><a name="[a3]"></a>spi_i2s_flag_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_i2s_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DRV_SPI_SwapByte
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WritePage
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_SendInstruction
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_ReadBuffer
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Read_Byte
</UL>

<P><STRONG><a name="[b1]"></a>spi_init</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_init))
<BR><BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_SPI0_Init
</UL>

<P><STRONG><a name="[117]"></a>systick_config</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, systick.o(i.systick_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = systick_config &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[df]"></a>timer_channel_output_config</STRONG> (Thumb, 484 bytes, Stack size 8 bytes, gd32f4xx_timer.o(i.timer_channel_output_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = timer_channel_output_config
</UL>
<BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER1_Init
</UL>

<P><STRONG><a name="[e1]"></a>timer_channel_output_mode_config</STRONG> (Thumb, 90 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_channel_output_mode_config))
<BR><BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER1_Init
</UL>

<P><STRONG><a name="[e0]"></a>timer_channel_output_pulse_value_config</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_channel_output_pulse_value_config))
<BR><BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER1_Init
</UL>

<P><STRONG><a name="[e2]"></a>timer_channel_output_shadow_config</STRONG> (Thumb, 90 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_channel_output_shadow_config))
<BR><BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER1_Init
</UL>

<P><STRONG><a name="[dd]"></a>timer_deinit</STRONG> (Thumb, 374 bytes, Stack size 8 bytes, gd32f4xx_timer.o(i.timer_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = timer_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER6_Init
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER1_Init
</UL>

<P><STRONG><a name="[e8]"></a>timer_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_enable))
<BR><BR>[Called By]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER6_Init
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[de]"></a>timer_init</STRONG> (Thumb, 122 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_init))
<BR><BR>[Called By]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER6_Init
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER1_Init
</UL>

<P><STRONG><a name="[e7]"></a>timer_interrupt_enable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_interrupt_enable))
<BR><BR>[Called By]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER6_Init
</UL>

<P><STRONG><a name="[e9]"></a>timer_interrupt_flag_clear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_interrupt_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_PeriodElapsedCallback
</UL>

<P><STRONG><a name="[e3]"></a>timer_interrupt_flag_get</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_interrupt_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER6_IRQHandler
<LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER3_IRQHandler
</UL>

<P><STRONG><a name="[f1]"></a>usart_baudrate_set</STRONG> (Thumb, 224 bytes, Stack size 32 bytes, gd32f4xx_usart.o(i.usart_baudrate_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = usart_baudrate_set &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_clock_freq_get
</UL>
<BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Init
</UL>

<P><STRONG><a name="[ec]"></a>usart_data_receive</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_data_receive))
<BR><BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_RxCpltCallback
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_IDLECallBack
</UL>

<P><STRONG><a name="[112]"></a>usart_data_transmit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_data_transmit))
<BR><BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
</UL>

<P><STRONG><a name="[ef]"></a>usart_deinit</STRONG> (Thumb, 210 bytes, Stack size 8 bytes, gd32f4xx_usart.o(i.usart_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Init
</UL>

<P><STRONG><a name="[f9]"></a>usart_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_enable))
<BR><BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Init
</UL>

<P><STRONG><a name="[113]"></a>usart_flag_get</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, gd32f4xx_usart.o(i.usart_flag_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
</UL>

<P><STRONG><a name="[f8]"></a>usart_hardware_flow_cts_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_hardware_flow_cts_config))
<BR><BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Init
</UL>

<P><STRONG><a name="[f7]"></a>usart_hardware_flow_rts_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_hardware_flow_rts_config))
<BR><BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Init
</UL>

<P><STRONG><a name="[eb]"></a>usart_interrupt_flag_clear</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, gd32f4xx_usart.o(i.usart_interrupt_flag_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_interrupt_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_IDLECallBack
</UL>

<P><STRONG><a name="[fa]"></a>usart_interrupt_flag_get</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, gd32f4xx_usart.o(i.usart_interrupt_flag_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = usart_interrupt_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
</UL>

<P><STRONG><a name="[f6]"></a>usart_parity_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_parity_config))
<BR><BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Init
</UL>

<P><STRONG><a name="[f2]"></a>usart_receive_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_receive_config))
<BR><BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Init
</UL>

<P><STRONG><a name="[f5]"></a>usart_stop_bit_set</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_stop_bit_set))
<BR><BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Init
</UL>

<P><STRONG><a name="[f3]"></a>usart_transmit_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_transmit_config))
<BR><BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Init
</UL>

<P><STRONG><a name="[f4]"></a>usart_word_length_set</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_word_length_set))
<BR><BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Init
</UL>

<P><STRONG><a name="[114]"></a>wiz_read_buf</STRONG> (Thumb, 78 bytes, Stack size 24 bytes, w5500_conf.o(i.wiz_read_buf))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = wiz_read_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;iinchip_cson
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;iinchip_csoff
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_SpiSendData
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recv_data_processing
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recvfrom
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;getSHAR
</UL>

<P><STRONG><a name="[121]"></a>wiz_write_buf</STRONG> (Thumb, 80 bytes, Stack size 24 bytes, w5500_conf.o(i.wiz_write_buf))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;iinchip_cson
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;iinchip_csoff
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_SpiSendData
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_data_processing
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setSUBR
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setSIPR
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setGAR
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setSHAR
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[122]"></a>system_clock_168m_25m_hxtal</STRONG> (Thumb, 240 bytes, Stack size 0 bytes, system_gd32f4xx.o(i.system_clock_168m_25m_hxtal))
<BR><BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_config
</UL>

<P><STRONG><a name="[db]"></a>system_clock_config</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, system_gd32f4xx.o(i.system_clock_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = system_clock_config
</UL>
<BR>[Calls]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_168m_25m_hxtal
</UL>
<BR>[Called By]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
</UL>

<P><STRONG><a name="[123]"></a>__NVIC_SetPriority</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, systick.o(i.__NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;systick_config
</UL>

<P><STRONG><a name="[c0]"></a>InvertUint16</STRONG> (Thumb, 58 bytes, Stack size 20 bytes, my_crc.o(i.InvertUint16))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = InvertUint16
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16_USB
</UL>

<P><STRONG><a name="[bf]"></a>InvertUint8</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, my_crc.o(i.InvertUint8))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = InvertUint8
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16_USB
</UL>

<P><STRONG><a name="[d9]"></a>AddByteToBuffer</STRONG> (Thumb, 110 bytes, Stack size 8 bytes, usart.o(i.AddByteToBuffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = AddByteToBuffer
</UL>
<BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RecvDataHandler
</UL>

<P><STRONG><a name="[cf]"></a>ReadBytesToBuffer</STRONG> (Thumb, 142 bytes, Stack size 24 bytes, usart.o(i.ReadBytesToBuffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = ReadBytesToBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FML_USART_RecvTask
</UL>

<P><STRONG><a name="[d8]"></a>RecvDataHandler</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, usart.o(i.RecvDataHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = RecvDataHandler &rArr; AddByteToBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AddByteToBuffer
</UL>
<BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_RxCpltCallback
</UL>

<P><STRONG><a name="[101]"></a>_fp_digits</STRONG> (Thumb, 366 bytes, Stack size 64 bytes, printfa.o(i._fp_digits), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[ff]"></a>_printf_core</STRONG> (Thumb, 1704 bytes, Stack size 136 bytes, printfa.o(i._printf_core), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>
<BR>[Called By]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0printf
</UL>

<P><STRONG><a name="[104]"></a>_printf_post_padding</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, printfa.o(i._printf_post_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[103]"></a>_printf_pre_padding</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, printfa.o(i._printf_pre_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[6a]"></a>_sputc</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, printfa.o(i._sputc))
<BR><BR>[Called By]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0sprintf)
</UL><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
