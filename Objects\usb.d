.\objects\usb.o: Utilities\usb.c
.\objects\usb.o: Utilities\usb.h
.\objects\usb.o: .\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\objects\usb.o: .\Firmware\CMSIS\core_cm4.h
.\objects\usb.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\usb.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h
.\objects\usb.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h
.\objects\usb.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h
.\objects\usb.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h
.\objects\usb.o: .\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h
.\objects\usb.o: .\USER\gd32f4xx_libopt.h
.\objects\usb.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h
.\objects\usb.o: .\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\objects\usb.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h
.\objects\usb.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h
.\objects\usb.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h
.\objects\usb.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h
.\objects\usb.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h
.\objects\usb.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h
.\objects\usb.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h
.\objects\usb.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h
.\objects\usb.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h
.\objects\usb.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h
.\objects\usb.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h
.\objects\usb.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h
.\objects\usb.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h
.\objects\usb.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h
.\objects\usb.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h
.\objects\usb.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h
.\objects\usb.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h
.\objects\usb.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h
.\objects\usb.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h
.\objects\usb.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h
.\objects\usb.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h
.\objects\usb.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h
.\objects\usb.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h
.\objects\usb.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h
.\objects\usb.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h
.\objects\usb.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdlib.h
.\objects\usb.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h
.\objects\usb.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h
.\objects\usb.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h
.\objects\usb.o: .\USBFS-object\usbd_core.h
.\objects\usb.o: .\USBFS-object\drv_usb_core.h
.\objects\usb.o: .\USBFS-object\drv_usb_regs.h
.\objects\usb.o: .\USBFS-object\usb_conf.h
.\objects\usb.o: .\Utilities\gd32f470v_start.h
.\objects\usb.o: .\USBFS-object\usb_ch9_std.h
.\objects\usb.o: .\USBFS-object\usbd_conf.h
.\objects\usb.o: .\USBFS-object\drv_usb_dev.h
.\objects\usb.o: .\USBFS-object\cdc_acm_core.h
.\objects\usb.o: .\USBFS-object\usbd_enum.h
.\objects\usb.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\wchar.h
.\objects\usb.o: .\USBFS-object\usb_cdc.h
.\objects\usb.o: Utilities\user_step.h
.\objects\usb.o: Utilities\25LC080A.h
.\objects\usb.o: Utilities\My_CRC.h
.\objects\usb.o: Utilities\eeprom_spi.h
.\objects\usb.o: .\USER\systick.h
.\objects\usb.o: Utilities\usart.h
.\objects\usb.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
.\objects\usb.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdio.h
.\objects\usb.o: Utilities\spi.h
.\objects\usb.o: Utilities\esp32_wifi.h
.\objects\usb.o: Utilities\gpio.h
.\objects\usb.o: Utilities\user_step.h
.\objects\usb.o: Utilities\dac.h
.\objects\usb.o: Utilities\adc.h
.\objects\usb.o: Utilities\mymath.h
.\objects\usb.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\math.h
.\objects\usb.o: Utilities\whut_math.h
.\objects\usb.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.7.0\CMSIS\DSP\Include\arm_math.h
.\objects\usb.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\float.h
.\objects\usb.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\limits.h
.\objects\usb.o: Utilities\flash.h
.\objects\usb.o: Utilities\rtc.h
.\objects\usb.o: Utilities\API_W5500.h
.\objects\usb.o: ..\Ethernet\W5500\w5500.h
.\objects\usb.o: ..\Ethernet\W5500\Types.h
.\objects\usb.o: Utilities\API_LAN_DATA_Process.h
.\objects\usb.o: ..\Ethernet\W5500\socket.h
