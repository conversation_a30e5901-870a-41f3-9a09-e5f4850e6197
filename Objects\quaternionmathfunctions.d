.\objects\quaternionmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\QuaternionMathFunctions\QuaternionMathFunctions.c
.\objects\quaternionmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\QuaternionMathFunctions\arm_quaternion_norm_f32.c
.\objects\quaternionmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/quaternion_math_functions.h
.\objects\quaternionmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\arm_math_types.h
.\objects\quaternionmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h
.\objects\quaternionmathfunctions.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\quaternionmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h
.\objects\quaternionmathfunctions.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
.\objects\quaternionmathfunctions.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\math.h
.\objects\quaternionmathfunctions.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\float.h
.\objects\quaternionmathfunctions.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\limits.h
.\objects\quaternionmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\arm_math_memory.h
.\objects\quaternionmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/none.h
.\objects\quaternionmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/utils.h
.\objects\quaternionmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\QuaternionMathFunctions\arm_quaternion_inverse_f32.c
.\objects\quaternionmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\QuaternionMathFunctions\arm_quaternion_conjugate_f32.c
.\objects\quaternionmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\QuaternionMathFunctions\arm_quaternion_normalize_f32.c
.\objects\quaternionmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\QuaternionMathFunctions\arm_quaternion_product_single_f32.c
.\objects\quaternionmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\QuaternionMathFunctions\arm_quaternion_product_f32.c
.\objects\quaternionmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\QuaternionMathFunctions\arm_quaternion2rotation_f32.c
.\objects\quaternionmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\QuaternionMathFunctions\arm_rotation2quaternion_f32.c
