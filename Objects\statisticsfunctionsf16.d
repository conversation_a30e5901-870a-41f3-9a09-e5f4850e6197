.\objects\statisticsfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\StatisticsFunctionsF16.c
.\objects\statisticsfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_max_f16.c
.\objects\statisticsfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/statistics_functions_f16.h
.\objects\statisticsfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\arm_math_types_f16.h
.\objects\statisticsfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\arm_math_types.h
.\objects\statisticsfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h
.\objects\statisticsfunctionsf16.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\statisticsfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h
.\objects\statisticsfunctionsf16.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
.\objects\statisticsfunctionsf16.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\math.h
.\objects\statisticsfunctionsf16.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\float.h
.\objects\statisticsfunctionsf16.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\limits.h
.\objects\statisticsfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\arm_math_memory.h
.\objects\statisticsfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/none.h
.\objects\statisticsfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/utils.h
.\objects\statisticsfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/basic_math_functions_f16.h
.\objects\statisticsfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/fast_math_functions_f16.h
.\objects\statisticsfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/fast_math_functions.h
.\objects\statisticsfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_min_f16.c
.\objects\statisticsfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_mean_f16.c
.\objects\statisticsfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_power_f16.c
.\objects\statisticsfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_rms_f16.c
.\objects\statisticsfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_std_f16.c
.\objects\statisticsfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_var_f16.c
.\objects\statisticsfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_entropy_f16.c
.\objects\statisticsfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_kullback_leibler_f16.c
.\objects\statisticsfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_logsumexp_dot_prod_f16.c
.\objects\statisticsfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_logsumexp_f16.c
.\objects\statisticsfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_max_no_idx_f16.c
.\objects\statisticsfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_absmax_f16.c
.\objects\statisticsfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_absmin_f16.c
