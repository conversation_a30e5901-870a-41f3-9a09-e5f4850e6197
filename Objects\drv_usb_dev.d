.\objects\drv_usb_dev.o: USBFS-object\drv_usb_dev.c
.\objects\drv_usb_dev.o: USBFS-object\drv_usb_hw.h
.\objects\drv_usb_dev.o: USBFS-object\usb_conf.h
.\objects\drv_usb_dev.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdlib.h
.\objects\drv_usb_dev.o: .\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\objects\drv_usb_dev.o: .\Firmware\CMSIS\core_cm4.h
.\objects\drv_usb_dev.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\drv_usb_dev.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h
.\objects\drv_usb_dev.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h
.\objects\drv_usb_dev.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h
.\objects\drv_usb_dev.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h
.\objects\drv_usb_dev.o: .\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h
.\objects\drv_usb_dev.o: .\USER\gd32f4xx_libopt.h
.\objects\drv_usb_dev.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h
.\objects\drv_usb_dev.o: .\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\objects\drv_usb_dev.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h
.\objects\drv_usb_dev.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h
.\objects\drv_usb_dev.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h
.\objects\drv_usb_dev.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h
.\objects\drv_usb_dev.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h
.\objects\drv_usb_dev.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h
.\objects\drv_usb_dev.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h
.\objects\drv_usb_dev.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h
.\objects\drv_usb_dev.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h
.\objects\drv_usb_dev.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h
.\objects\drv_usb_dev.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h
.\objects\drv_usb_dev.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h
.\objects\drv_usb_dev.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h
.\objects\drv_usb_dev.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h
.\objects\drv_usb_dev.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h
.\objects\drv_usb_dev.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h
.\objects\drv_usb_dev.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h
.\objects\drv_usb_dev.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h
.\objects\drv_usb_dev.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h
.\objects\drv_usb_dev.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h
.\objects\drv_usb_dev.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h
.\objects\drv_usb_dev.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h
.\objects\drv_usb_dev.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h
.\objects\drv_usb_dev.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h
.\objects\drv_usb_dev.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h
.\objects\drv_usb_dev.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h
.\objects\drv_usb_dev.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h
.\objects\drv_usb_dev.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h
.\objects\drv_usb_dev.o: .\Utilities\gd32f470v_start.h
.\objects\drv_usb_dev.o: USBFS-object\drv_usb_core.h
.\objects\drv_usb_dev.o: USBFS-object\drv_usb_regs.h
.\objects\drv_usb_dev.o: USBFS-object\usb_ch9_std.h
.\objects\drv_usb_dev.o: USBFS-object\usbd_conf.h
.\objects\drv_usb_dev.o: USBFS-object\drv_usb_dev.h
