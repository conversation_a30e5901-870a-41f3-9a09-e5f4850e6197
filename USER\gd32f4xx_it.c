/*!
    \file    gd32f4xx_it.c
    \brief   interrupt service routines
    
    \version 2020-12-04, V2.0.0, demo for GD32F4xx
*/

/*
    Copyright (c) 2020, GigaDevice Semiconductor Inc.

    Redistribution and use in source and binary forms, with or without modification, 
are permitted provided that the following conditions are met:

    1. Redistributions of source code must retain the above copyright notice, this 
       list of conditions and the following disclaimer.
    2. Redistributions in binary form must reproduce the above copyright notice, 
       this list of conditions and the following disclaimer in the documentation 
       and/or other materials provided with the distribution.
    3. Neither the name of the copyright holder nor the names of its contributors 
       may be used to endorse or promote products derived from this software without 
       specific prior written permission.

    THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" 
AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED 
WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. 
IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, 
INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT 
NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR 
PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, 
WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) 
ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY 
OF SUCH DAMAGE.
*/

#include "main.h"
#include "gd32f4xx_it.h"
#include "gd32f470v_start.h"
#include "systick.h"
#include "usart.h"
#include "time.h"
#include "adc.h"
#include "drv_usbd_int.h"

extern usb_core_driver cdc_acm;



//static void resume_mcu_clk(void);

/*!
    \brief      this function handles NMI exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void NMI_Handler(void)
{
}

/*!
    \brief      this function handles HardFault exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void HardFault_Handler(void)
{
   SysReset_Condition(0xAA553344);
  	/* if Hard Fault exception occurs, go to infinite loop */
    while (1){
    }
}

/*!
    \brief      this function handles MemManage exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void MemManage_Handler(void)
{
    /* if Memory Manage exception occurs, go to infinite loop */
    while (1){
    }
}

/*!
    \brief      this function handles BusFault exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void BusFault_Handler(void)
{
    /* if Bus Fault exception occurs, go to infinite loop */
    while (1){
    }
}

/*!
    \brief      this function handles UsageFault exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void UsageFault_Handler(void)
{
    /* if Usage Fault exception occurs, go to infinite loop */
    while (1){
    }
}

/*!
    \brief      this function handles SVC exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void SVC_Handler(void)
{
}

/*!
    \brief      this function handles DebugMon exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void DebugMon_Handler(void)
{
}

/*!
    \brief      this function handles PendSV exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void PendSV_Handler(void)
{
}

/*!
    \brief      this function handles SysTick exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void SysTick_Handler(void)
{
    delay_decrement();
}

void USART2_IRQHandler(void)
{
    if(RESET != usart_interrupt_flag_get(USART2,USART_INT_FLAG_RBNE))
		{
			UART_RxCpltCallback(USART2);
    }
       
    if(RESET != usart_interrupt_flag_get(USART2, USART_INT_FLAG_IDLE))
		{
			UART_IDLECallBack(USART2);
    }
}

void TIMER6_IRQHandler(void)
{
	  if(RESET != timer_interrupt_flag_get(TIMER6, TIMER_INT_FLAG_UP))
      TIM_PeriodElapsedCallback(TIMER6);
}

void TIMER3_IRQHandler(void)
{
	  if(RESET != timer_interrupt_flag_get(TIMER3, TIMER_INT_FLAG_UP))
      TIM_PeriodElapsedCallback(TIMER3);
}

//void ADC_IRQHandler(void)
//{
//	 if(RESET != adc_interrupt_flag_get(ADC0, ADC_INT_FLAG_EOC))
//		 ADC_Callback(ADC0);
//}
void DMA1_Channel0_IRQHandler(void)
{
;;
}

void DMA1_Channel1_IRQHandler(void)
{
;;
}

void DMA1_Channel2_IRQHandler(void)
{
;;
}
void TIMER2_IRQHandler(void)
{
//    usb_timer_irq();
}

#ifdef USE_USB_FS

/*!
    \brief      this function handles USBFS wakeup interrupt handler
    \param[in]  none
    \param[out] none
    \retval     none
*/


/*!
    \brief      this function handles USBHS wakeup interrupt handler
    \param[in]  none
    \param[out] none
    \retval     none
*/

#endif /* USE_USBFS */

#ifdef USE_USB_FS

/*!
    \brief      this function handles USBFS IRQ Handler
    \param[in]  none
    \param[out] none
    \retval     none
*/

#elif defined(USE_USB_HS)

/*!
    \brief      this function handles USBHS IRQ Handler
    \param[in]  none
    \param[out] none
    \retval     none
*/
void USBHS_IRQHandler(void)
{
    usbd_isr(&cdc_acm);
}

#endif /* USE_USBFS */

#ifdef USB_HS_DEDICATED_EP1_ENABLED



#endif /* USB_HS_DEDICATED_EP1_ENABLED */

/*!
    \brief      resume mcu clock
    \param[in]  none
    \param[out] none
    \retval     none
*/
//static void resume_mcu_clk(void)
//{
//    /* enable HSE */
//    rcu_osci_on(RCU_HXTAL);

//    /* wait till HSE is ready */
//    while(RESET == rcu_flag_get(RCU_FLAG_HXTALSTB)){
//    }

//    /* enable PLL */
//    rcu_osci_on(RCU_PLL_CK);

//    /* wait till PLL is ready */
//    while(RESET == rcu_flag_get(RCU_FLAG_PLLSTB)){
//    }

//    /* select PLL as system clock source */
//    rcu_system_clock_source_config(RCU_CKSYSSRC_PLLP);

//    /* wait till PLL is used as system clock source */
//    while(RCU_SCSS_PLLP != rcu_system_clock_source_get()){
//    }
//}


void EXTI10_15_IRQHandler(void)
{
;;
}
