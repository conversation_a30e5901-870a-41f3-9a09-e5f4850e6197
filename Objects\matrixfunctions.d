.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\MatrixFunctions.c
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_add_f32.c
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/matrix_functions.h
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\arm_math_types.h
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h
.\objects\matrixfunctions.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h
.\objects\matrixfunctions.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
.\objects\matrixfunctions.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\math.h
.\objects\matrixfunctions.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\float.h
.\objects\matrixfunctions.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\limits.h
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\arm_math_memory.h
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/none.h
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/utils.h
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_add_q15.c
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_add_q31.c
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_cmplx_mult_f32.c
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_cmplx_mult_q15.c
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_cmplx_mult_q31.c
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_init_f32.c
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_init_q15.c
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_init_q31.c
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_inverse_f32.c
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_inverse_f64.c
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_mult_f64.c
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_mult_f32.c
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_mult_fast_q15.c
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_mult_fast_q31.c
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_mult_q7.c
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_mult_q15.c
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_mult_q31.c
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_scale_f32.c
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_scale_q15.c
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_scale_q31.c
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_sub_f64.c
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_sub_f32.c
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_sub_q15.c
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_sub_q31.c
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_trans_f32.c
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_trans_f64.c
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_trans_q7.c
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_trans_q15.c
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_trans_q31.c
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_vec_mult_f32.c
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_vec_mult_q31.c
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_vec_mult_q15.c
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_vec_mult_q7.c
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_cmplx_trans_f32.c
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_cmplx_trans_q31.c
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_cmplx_trans_q15.c
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_cholesky_f64.c
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_cholesky_f32.c
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_solve_upper_triangular_f32.c
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_solve_lower_triangular_f32.c
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_solve_upper_triangular_f64.c
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_solve_lower_triangular_f64.c
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_ldlt_f32.c
.\objects\matrixfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_ldlt_f64.c
