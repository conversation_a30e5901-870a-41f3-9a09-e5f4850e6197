.\objects\basicmathfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\BasicMathFunctionsF16.c
.\objects\basicmathfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_abs_f16.c
.\objects\basicmathfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/basic_math_functions_f16.h
.\objects\basicmathfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\arm_math_types_f16.h
.\objects\basicmathfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\arm_math_types.h
.\objects\basicmathfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h
.\objects\basicmathfunctionsf16.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\basicmathfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h
.\objects\basicmathfunctionsf16.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
.\objects\basicmathfunctionsf16.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\math.h
.\objects\basicmathfunctionsf16.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\float.h
.\objects\basicmathfunctionsf16.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\limits.h
.\objects\basicmathfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\arm_math_memory.h
.\objects\basicmathfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/none.h
.\objects\basicmathfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/utils.h
.\objects\basicmathfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_add_f16.c
.\objects\basicmathfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_dot_prod_f16.c
.\objects\basicmathfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_mult_f16.c
.\objects\basicmathfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_negate_f16.c
.\objects\basicmathfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_offset_f16.c
.\objects\basicmathfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_scale_f16.c
.\objects\basicmathfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_sub_f16.c
.\objects\basicmathfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_clip_f16.c
