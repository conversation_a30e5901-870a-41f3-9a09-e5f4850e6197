.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\StatisticsFunctions.c
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_entropy_f32.c
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/statistics_functions.h
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\arm_math_types.h
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h
.\objects\statisticsfunctions.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h
.\objects\statisticsfunctions.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
.\objects\statisticsfunctions.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\math.h
.\objects\statisticsfunctions.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\float.h
.\objects\statisticsfunctions.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\limits.h
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\arm_math_memory.h
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/none.h
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/utils.h
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/basic_math_functions.h
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/fast_math_functions.h
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_entropy_f64.c
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_kullback_leibler_f32.c
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_kullback_leibler_f64.c
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_logsumexp_dot_prod_f32.c
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_logsumexp_f32.c
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_max_f32.c
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_max_q15.c
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_max_q31.c
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_max_q7.c
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_max_no_idx_f32.c
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_mean_f32.c
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_mean_q15.c
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_mean_q31.c
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_mean_q7.c
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_min_f32.c
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_min_q15.c
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_min_q31.c
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_min_q7.c
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_power_f32.c
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_power_q15.c
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_power_q31.c
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_power_q7.c
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_rms_f32.c
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_rms_q15.c
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_rms_q31.c
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_std_f32.c
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_std_q15.c
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_std_q31.c
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_var_f32.c
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_var_q15.c
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_var_q31.c
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_absmax_f32.c
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_absmax_q15.c
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_absmax_q31.c
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_absmax_q7.c
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_absmin_f32.c
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_absmin_q15.c
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_absmin_q31.c
.\objects\statisticsfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\StatisticsFunctions\arm_absmin_q7.c
