.\objects\distancefunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\DistanceFunctions\DistanceFunctions.c
.\objects\distancefunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\DistanceFunctions\arm_boolean_distance.c
.\objects\distancefunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/distance_functions.h
.\objects\distancefunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\arm_math_types.h
.\objects\distancefunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h
.\objects\distancefunctions.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\distancefunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h
.\objects\distancefunctions.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
.\objects\distancefunctions.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\math.h
.\objects\distancefunctions.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\float.h
.\objects\distancefunctions.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\limits.h
.\objects\distancefunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\arm_math_memory.h
.\objects\distancefunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/none.h
.\objects\distancefunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/utils.h
.\objects\distancefunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/statistics_functions.h
.\objects\distancefunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/basic_math_functions.h
.\objects\distancefunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/fast_math_functions.h
.\objects\distancefunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\DistanceFunctions\arm_boolean_distance_template.h
.\objects\distancefunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\DistanceFunctions\arm_boolean_distance_template.h
.\objects\distancefunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\DistanceFunctions\arm_boolean_distance_template.h
.\objects\distancefunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\DistanceFunctions\arm_boolean_distance_template.h
.\objects\distancefunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\DistanceFunctions\arm_braycurtis_distance_f32.c
.\objects\distancefunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\DistanceFunctions\arm_canberra_distance_f32.c
.\objects\distancefunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\DistanceFunctions\arm_chebyshev_distance_f32.c
.\objects\distancefunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\DistanceFunctions\arm_cityblock_distance_f32.c
.\objects\distancefunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\DistanceFunctions\arm_correlation_distance_f32.c
.\objects\distancefunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\DistanceFunctions\arm_cosine_distance_f32.c
.\objects\distancefunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\DistanceFunctions\arm_dice_distance.c
.\objects\distancefunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\DistanceFunctions\arm_euclidean_distance_f32.c
.\objects\distancefunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\DistanceFunctions\arm_hamming_distance.c
.\objects\distancefunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\DistanceFunctions\arm_jaccard_distance.c
.\objects\distancefunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\DistanceFunctions\arm_jensenshannon_distance_f32.c
.\objects\distancefunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\DistanceFunctions\arm_kulsinski_distance.c
.\objects\distancefunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\DistanceFunctions\arm_minkowski_distance_f32.c
.\objects\distancefunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\DistanceFunctions\arm_rogerstanimoto_distance.c
.\objects\distancefunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\DistanceFunctions\arm_russellrao_distance.c
.\objects\distancefunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\DistanceFunctions\arm_sokalmichener_distance.c
.\objects\distancefunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\DistanceFunctions\arm_sokalsneath_distance.c
.\objects\distancefunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\DistanceFunctions\arm_yule_distance.c
