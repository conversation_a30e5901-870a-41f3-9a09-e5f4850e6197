.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\BasicMathFunctions.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_abs_f32.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/basic_math_functions.h
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\arm_math_types.h
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h
.\objects\basicmathfunctions.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h
.\objects\basicmathfunctions.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
.\objects\basicmathfunctions.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\math.h
.\objects\basicmathfunctions.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\float.h
.\objects\basicmathfunctions.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\limits.h
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\arm_math_memory.h
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/none.h
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/utils.h
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_abs_q15.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_abs_q31.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_abs_q7.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_add_f32.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_add_q15.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_add_q31.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_add_q7.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_and_u16.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_and_u32.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_and_u8.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_dot_prod_f32.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_dot_prod_q15.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_dot_prod_q31.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_dot_prod_q7.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_mult_f32.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_mult_q15.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_mult_q31.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_mult_q7.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_negate_f32.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_negate_q15.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_negate_q31.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_negate_q7.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_not_u16.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_not_u32.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_not_u8.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_offset_f32.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_offset_q15.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_offset_q31.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_offset_q7.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_or_u16.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_or_u32.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_or_u8.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_scale_f32.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_scale_q15.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_scale_q31.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_scale_q7.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_shift_q15.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_shift_q31.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_shift_q7.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_sub_f32.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_sub_q15.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_sub_q31.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_sub_q7.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_xor_u16.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_xor_u32.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_xor_u8.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_clip_f32.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_clip_q31.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_clip_q15.c
.\objects\basicmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\BasicMathFunctions\arm_clip_q7.c
