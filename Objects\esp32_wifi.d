.\objects\esp32_wifi.o: Utilities\esp32_wifi.c
.\objects\esp32_wifi.o: Utilities\esp32_wifi.h
.\objects\esp32_wifi.o: Utilities\usart.h
.\objects\esp32_wifi.o: .\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\objects\esp32_wifi.o: .\Firmware\CMSIS\core_cm4.h
.\objects\esp32_wifi.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\esp32_wifi.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h
.\objects\esp32_wifi.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h
.\objects\esp32_wifi.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h
.\objects\esp32_wifi.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h
.\objects\esp32_wifi.o: .\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h
.\objects\esp32_wifi.o: .\USER\gd32f4xx_libopt.h
.\objects\esp32_wifi.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h
.\objects\esp32_wifi.o: .\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\objects\esp32_wifi.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h
.\objects\esp32_wifi.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h
.\objects\esp32_wifi.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h
.\objects\esp32_wifi.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h
.\objects\esp32_wifi.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h
.\objects\esp32_wifi.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h
.\objects\esp32_wifi.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h
.\objects\esp32_wifi.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h
.\objects\esp32_wifi.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h
.\objects\esp32_wifi.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h
.\objects\esp32_wifi.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h
.\objects\esp32_wifi.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h
.\objects\esp32_wifi.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h
.\objects\esp32_wifi.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h
.\objects\esp32_wifi.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h
.\objects\esp32_wifi.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h
.\objects\esp32_wifi.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h
.\objects\esp32_wifi.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h
.\objects\esp32_wifi.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h
.\objects\esp32_wifi.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h
.\objects\esp32_wifi.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h
.\objects\esp32_wifi.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h
.\objects\esp32_wifi.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h
.\objects\esp32_wifi.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h
.\objects\esp32_wifi.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h
.\objects\esp32_wifi.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdlib.h
.\objects\esp32_wifi.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h
.\objects\esp32_wifi.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h
.\objects\esp32_wifi.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h
.\objects\esp32_wifi.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
.\objects\esp32_wifi.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdio.h
.\objects\esp32_wifi.o: Utilities\gpio.h
.\objects\esp32_wifi.o: Utilities\user_step.h
.\objects\esp32_wifi.o: Utilities\25LC080A.h
.\objects\esp32_wifi.o: Utilities\My_CRC.h
.\objects\esp32_wifi.o: Utilities\eeprom_spi.h
.\objects\esp32_wifi.o: .\USER\systick.h
.\objects\esp32_wifi.o: Utilities\spi.h
.\objects\esp32_wifi.o: Utilities\esp32_wifi.h
.\objects\esp32_wifi.o: Utilities\dac.h
.\objects\esp32_wifi.o: Utilities\gd32f470v_start.h
.\objects\esp32_wifi.o: Utilities\adc.h
.\objects\esp32_wifi.o: Utilities\mymath.h
.\objects\esp32_wifi.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\math.h
.\objects\esp32_wifi.o: Utilities\whut_math.h
.\objects\esp32_wifi.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.7.0\CMSIS\DSP\Include\arm_math.h
.\objects\esp32_wifi.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\float.h
.\objects\esp32_wifi.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\limits.h
.\objects\esp32_wifi.o: Utilities\flash.h
.\objects\esp32_wifi.o: Utilities\rtc.h
