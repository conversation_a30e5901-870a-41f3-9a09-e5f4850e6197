#include "API_LAN_DATA_Process.h"
#include "API_W5500.h"

#include "gd32f4xx.h"
#include "gd32f470v_start.h"
#include "socket.h"
#include "mymath.h"
#include "user_step.h"

#include "w5500.h"
#include "w5500_conf.h"
#include "w5500_conf.h"

#include "utility.h"
#include "dhcp.h"
#include "tcp_client.h"
#include "socket.h"
extern LAN_PARA Lan_Para;
extern uint8_t dhcp_ok ;
extern uint8_t Soft_Ver[3];
extern uint8_t HardWare_Ver[2];
void API_Write_SeverInfo(void);

//����ת�ַ���
uint8_t API_Itoi(uint8_t  *str, unsigned long num)
{
    const uint8_t  index[]="0123456789";
    uint8_t i = 0, j = 0,temp;
    do
    {
        str[i++] = index[num%10];
        num /= 10;
    }while(num);

    str[i]='\0';
    for(j=0; j<=(i-1)/2; j++)
    {
        temp=str[j];
        str[j]=str[i-j-1];
        str[i-j-1]=temp;
    }
	return i;
}
//16��������ת�ַ���
void HEXArrayToStringArray(const uint8_t *inputdata, uint8_t *output,uint16_t length)
{
	uint16_t i =0;
	uint8_t TempBuff[254];
	for(i=0;i<length;i++)
	{
		TempBuff[2*i] = inputdata[i]>>4;
		TempBuff[2*i+1] = inputdata[i]&0x0f;
	}
	for(i=0;i<2*length;i++)
	{
		sprintf((char *)&output[i],"%X",TempBuff[i]);
	}
}

void API_AnalysisIpPortAddr(char* addr,uint8_t* outip)//���ַ���IP��ַת��Ϊ����IPǰ4�ֽڶ˿ں�2�ֽ�
{
		char* s=(char*)addr;
		char* ss=NULL;
		uint8_t n=0;
		uint16_t z;
		while (n<4&&(ss=strchr(s,'.'))!=NULL)
		{
				*ss=0;
				outip[n++]=atoi(s);
				s=ss+1;
		}
		if (n<4&&*s)
				outip[n]=atoi(s);
		if(strchr(s,':')!=NULL)
		{
				ss=strchr(s,':');
				n =ss-s+1;
				z= atoi(s+n);
			  outip[4] = (z>>8)&0xff; outip[5] = (z)&0xff;
		}
		__NOP();
}

void API_ACK_20CMD(void)//���ݴ���ģʽ
{
//	my_device_info.WiFi_Send_Mode = Lan_Para.LAN_RevBuf[1];//���ݴ���ģʽ
	// 00 Ϊ����ģʽ
	//02Ϊȫ����ģʽ
}
void API_ACK_21CMD(void)
{
		//uint8_t i,SendBuff[20];
		uint8_t SendBuff[20];
	  memset(  SendBuff, '\0', 20);
		SendBuff[0] = 0x12;
	 // for( i = 0; i < 16; i++) SendBuff[i + 1] = my_device_info.Device_ID[i];
		SendBuff[18] = CRC16_USB(SendBuff, 18) / 256;
		SendBuff[19] = CRC16_USB(SendBuff, 18) % 256;
	  send(SOCK_TCPC,SendBuff,20); 
		printf("ACK_21CMD:");
		API_Printf_Hex(SendBuff,  20);	
}
void API_ACK_22CMD(void)//��������ָ��
{
		uint8_t SendBuff[20];
		SendBuff[0] = 0x13;
//		SendBuff[1] = my_device_info.Bin_Ver[0];
	//	SendBuff[2] = my_device_info.Bin_Ver[1];
		SendBuff[3] = 0x15; //0x06��ʾ��Ҫ������0x15��ʾ����Ҫ����
		SendBuff[4] =Lan_Para.LAN_RevBuf[1];
		SendBuff[5] =Lan_Para.LAN_RevBuf[2];
//		if( my_device_info.Bin_Ver[0] != Lan_Para.LAN_RevBuf[1] || my_device_info.Bin_Ver[1] != Lan_Para.LAN_RevBuf[2] )	
		{
				Lan_Para.f_UpData =1;//������
			  Lan_Para.UpData_Cnt_1S=0;
				SendBuff[3] = 0x06; //0x06��ʾ��Ҫ������0x15��ʾ����Ҫ����
			 	bsp_EraseCpuFlash(0x08100000);//ֻ��һ������16KB
			 	bsp_EraseCpuFlash(0x08104000);//ֻ��һ������16KB			
			 	bsp_EraseCpuFlash(0x08108000);//ֻ��һ������16KB	
			 	bsp_EraseCpuFlash(0x0810C000);//ֻ��һ������16KB			
				bsp_EraseCpuFlash(0x08110000);//ֻ��һ������64KB
			
				bsp_EraseCpuFlash(0x08120000);//ֻ��һ������128KB
				bsp_EraseCpuFlash(0x08140000);//ֻ��һ������128KB
		   	bsp_EraseCpuFlash(0x08160000);//ֻ��һ������128KB
			
/*
#define ADDR_FLASH_SECTOR_0     ((unsigned int)0x08000000) 	//����0��ʼ��ַ, 16 Kbytes  
#define ADDR_FLASH_SECTOR_1     ((unsigned int)0x08004000) 	//����1��ʼ��ַ, 16 Kbytes  
#define ADDR_FLASH_SECTOR_2     ((unsigned int)0x08008000) 	//����2��ʼ��ַ, 16 Kbytes  
#define ADDR_FLASH_SECTOR_3     ((unsigned int)0x0800C000) 	//����3��ʼ��ַ, 16 Kbytes  
#define ADDR_FLASH_SECTOR_4     ((unsigned int)0x08010000) 	//����4��ʼ��ַ, 64 Kbytes  
#define ADDR_FLASH_SECTOR_5     ((unsigned int)0x08020000) 	//����5��ʼ��ַ, 128 Kbytes  
#define ADDR_FLASH_SECTOR_6     ((unsigned int)0x08040000) 	//����6��ʼ��ַ, 128 Kbytes  
#define ADDR_FLASH_SECTOR_7     ((unsigned int)0x08060000) 	//����7��ʼ��ַ, 128 Kbytes  
#define ADDR_FLASH_SECTOR_8     ((unsigned int)0x08080000) 	//����8��ʼ��ַ, 128 Kbytes  
#define ADDR_FLASH_SECTOR_9     ((unsigned int)0x080A0000) 	//����9��ʼ��ַ, 128 Kbytes  
#define ADDR_FLASH_SECTOR_10    ((unsigned int)0x080C0000) 	//����10��ʼ��ַ,128 Kbytes  
#define ADDR_FLASH_SECTOR_11    ((unsigned int)0x080E0000) 	//����11��ʼ��ַ,128 Kbytes  
*/
		}	
		SendBuff[18] = CRC16_USB(SendBuff, 18) / 256;
		SendBuff[19] = CRC16_USB(SendBuff, 18) % 256;
	  send(SOCK_TCPC,SendBuff,20); 		
		printf("ACK_22CMD:");
		API_Printf_Hex(SendBuff,  20);	
}

void API_ACK_23CMD(void)//�����ļ�
{
		uint8_t SendBuff[20];
		Lan_Para.UpData_Cnt_1S=0;	
		memset(  SendBuff, '\0', 20);
		SendBuff[0] = 0x14;
		SendBuff[1] = 0x15;  
		if(CRC16_USB( Lan_Para.LAN_RevBuf, 1029) == ( Lan_Para.LAN_RevBuf[1029] << 8 |  Lan_Para.LAN_RevBuf[1030]))// Write received data in Flash 
		{
				if (bsp_WriteCpuFlash(0x08100000 + (Lan_Para.LAN_RevBuf[3] * 256 + Lan_Para.LAN_RevBuf[4]) * 1024, & Lan_Para.LAN_RevBuf[5], 1024) == 0)
				{
						if(bsp_CmpCpuFlash(0x08100000 + (Lan_Para.LAN_RevBuf[3] * 256 + Lan_Para.LAN_RevBuf[4]) * 1024, & Lan_Para.LAN_RevBuf[5], 1024) == 0)
								SendBuff[1] = 0x06;
				}
		}  
		SendBuff[18] = CRC16_USB(SendBuff, 18) / 256;
		SendBuff[19] = CRC16_USB(SendBuff, 18) % 256;
	  send(SOCK_TCPC,SendBuff,20); 		
		printf("ACK_23CMD:");
		API_Printf_Hex(SendBuff,  20);				
}

void API_ACK_24CMD(void)//�����ļ�
{
		uint8_t SendBuff[20];		
	  Lan_Para.f_UpData =0;//�������
		Lan_Para.UpData_Cnt_1S=0;	
		memset(  SendBuff, '\0', 20);
		SendBuff[0] = 0x15;
		SendBuff[1] = 0x00;
		SendBuff[2] = 0x01;
		SendBuff[18] = CRC16_USB(SendBuff, 18) / 256;
		SendBuff[19] = CRC16_USB(SendBuff, 18) % 256;
	  send(SOCK_TCPC,SendBuff,20); 		
		printf("ACK_24CMD:");
		API_Printf_Hex(SendBuff,  20);	
			  SendBuff[0] = 0x01;
				USER_EEPROM_WriteByte(Addr_IAP_flag, &SendBuff[0] , 1);
		  	USER_EEPROM_WriteByte(Addr_IAP_flag+Addr_Back, &SendBuff[0], 1);
		SysReset_Condition(0xAABB1122);	//����ϵͳ
}
void API_ACK_25CMD(void)//�������봲У׼
{
//		my_device_info.Pd_auto_flag=1;
}

void API_ACK_26CMD(void)
{
		uint8_t i,SendBuff[20];
	  memset(  SendBuff, '\0', 20);
		SendBuff[0] = 0x18;
	  for( i = 0; i < 6; i++) SendBuff[i + 1] = Lan_Para.MAC[i];	
	  for(i=0;i<3;i++) SendBuff[i + 13] = Soft_Ver[i];
	  for(i=0;i<2;i++) SendBuff[i + 16] = HardWare_Ver[i];
	
		SendBuff[18] = CRC16_USB(SendBuff, 18) / 256;
		SendBuff[19] = CRC16_USB(SendBuff, 18) % 256;
	  send(SOCK_TCPC,SendBuff,20); 
	  printf("ACK_26CMD:");
		API_Printf_Hex(SendBuff,  20);	
}

void API_ACK_27CMD(void)//�������ڴ�У׼
{
//		my_device_info.Pd_auto_flag=2;
}

void API_ACK_28CMD(void)//�޸�Ӳ���豸��ID
{

		uint8_t i,SendBuff[20];
	  memset(  SendBuff, '\0', 20);
		SendBuff[0] = 0x19;
		for( i = 0; i < 16; i++)
		{
//			my_device_info.Device_ID[i] = Lan_Para.LAN_RevBuf[i+1];
		//	SendBuff[i + 1] = my_device_info.Device_ID[i];
		}


		SendBuff[18] = CRC16_USB(SendBuff, 18) / 256;
		SendBuff[19] = CRC16_USB(SendBuff, 18) % 256;
		send(SOCK_TCPC,SendBuff,20); 
		printf("ACK_28CMD:");
		API_Printf_Hex(SendBuff,  20);	
}

void API_ACK_29CMD(void)//�޸ķ�����IP����ַ
{
		uint8_t i,SendBuff[20];
		memset(  SendBuff, '\0', 20);
		SendBuff[0] = 0x20;
		for(i = 0; i < 6; i++)  
				SendBuff[i + 1] =  Lan_Para.LAN_RevBuf[i + 1];
	  for(i=0;i<4;i++)
		{  	
			  Lan_Para.Old_IP_S[i]=Lan_Para.IP_S[i];//�ȱ���һ��IP
			  Lan_Para.IP_S[i] = Lan_Para.LAN_RevBuf[i + 1];//������IP��ȡ
			
		}
		Lan_Para.Old_Port_S =Lan_Para.Port_S ;//���ݶ˿�
	  Lan_Para.Port_S = (Lan_Para.LAN_RevBuf[5]<<8)+Lan_Para.LAN_RevBuf[6];//�������˿�
	
		SendBuff[18] = CRC16_USB(SendBuff, 18) / 256;
		SendBuff[19] = CRC16_USB(SendBuff, 18) % 256;
		send(SOCK_TCPC,SendBuff,20); 
		printf("ACK_28CMD:");
		API_Printf_Hex(SendBuff,  20);	
}

void API_LanSock1Send_CRC( uint8_t* SendBuff, uint16_t Length)
{
    if(Lan_Para.f_Connect ==1)
		{
				SendBuff[Length - 2] = CRC16_USB(SendBuff, Length - 2) / 256;
				SendBuff[Length - 1] = CRC16_USB(SendBuff, Length - 2) % 256;
				send(SOCK_TCPC,SendBuff,Length);   
				//printf("���ڷ��͵�����Ϊ:");
			  API_Printf_Hex(SendBuff,  Length);
		}
		__NOP();
}


void API_Process_Lan_Data(void)//�����������·�������
{
    if(Lan_Para.RevLen >19)
		{
				printf("[DATA] Processing command: 0x%02X\r\n", Lan_Para.LAN_RevBuf[0]);
				switch(Lan_Para.LAN_RevBuf[0])
				{
					case 0x20:	{//���ݴ���ģʽ0x00��ʾ����Ϊ��������ģʽ��0x02��ʾ����Ϊȫ����ģʽ�Ĳ��ְ���ʽ��
							printf("[CMD] Data send mode command\r\n");
							Lan_Para.f_Data_SendMode = 1;
						  break;}
					case 0x21:  {//��ѯ��ƷID
							printf("[CMD] Query product ID\r\n");
					    Lan_Para.f_Query_ID = 1;
						  break;}
					case 0x22:  {//��������ָ��
							printf("[CMD] Update start command\r\n");
					    Lan_Para.f_UpdataStart = 1;
						  break;}
					case 0x23:	{//�����ļ�
							printf("[CMD] Update file command\r\n");
						Lan_Para.f_UpdataFile = 1;
							break;}
					case 0x24:{//�����ļ��������
							printf("[CMD] Update file send complete\r\n");
							Lan_Para.f_UpdataFileSendComp = 1;
							break;}
					case 0x25:{//�봲PDУ׼
							printf("[CMD] Out-bed PD calibration\r\n");
						  Lan_Para.f_OutBedPD_Calibration = 1;
							break;}
					case 0x26: {
							printf("[CMD] Query MAC and version\r\n");
							Lan_Para.f_Query_MAC_Ver=1;//��ѯMAC���汾��
						  break;}
					case 0x27: {//�ڴ�PDУ׼
							printf("[CMD] In-bed PD calibration\r\n");
						  Lan_Para.f_InBedPD_Calibration = 1;
							break;}
					case 0x28: {//�޸�Ӳ���豸��ID
							printf("[CMD] Change device ID\r\n");
						  Lan_Para.f_ChangID = 1;
							break;}
					case 0x29: {//�޸ķ�����IP����ַ
							printf("[CMD] Change server IP/Port\r\n");
						  Lan_Para.f_ChangIP_Port = 1;
							break;}
					default:
						printf("[CMD] Unknown command: 0x%02X\r\n", Lan_Para.LAN_RevBuf[0]);
						break;
				}
				Lan_Para.RevLen  =0;//���ճ�����λ
		}
		else if(Lan_Para.RevLen > 0) {
			printf("[DATA] Received short packet (%d bytes), ignoring\r\n", Lan_Para.RevLen);
			Lan_Para.RevLen = 0;
		}
}

void API_ExecuteSeverCMD(void)//ִ�з���������
{
		if(Lan_Para.f_Data_SendMode ==1){
				API_ACK_20CMD();
				Lan_Para.f_Data_SendMode =0;
		}
	  if(Lan_Para.f_Query_ID ==1)	{
				API_ACK_21CMD();
				Lan_Para.f_Query_ID =0;
		}
		if(Lan_Para.f_UpdataStart ==1){//��������ָ��
				API_ACK_22CMD();
				Lan_Para.f_UpdataStart =0;
		}
	  if(Lan_Para.f_UpdataFile ==1)	{//�����ļ�
				API_ACK_23CMD();
				Lan_Para.f_UpdataFile =0;
		}		
		if(Lan_Para.f_UpdataFileSendComp == 1)	{//�ļ��������

				API_ACK_24CMD();
				Lan_Para.f_UpdataFileSendComp =0;
		}
		if(Lan_Para.f_Query_MAC_Ver == 1){//��ѯMAC���汾��
				API_ACK_26CMD();
			  if(Lan_Para.f_WriteSeverInfo ==1){
					 Lan_Para.f_LogOnSever=1;//��������¼�ɹ�
					Lan_Para.f_WriteSeverInfo =0;
				}
				Lan_Para.f_Query_MAC_Ver =0;
		}
		if(Lan_Para.f_ChangID == 1){//�޸�Ӳ���豸��ID
				API_ACK_28CMD();
				Lan_Para.f_ChangID =0;
		}
    if(Lan_Para.f_OutBedPD_Calibration ==1){//�봲У׼
				API_ACK_25CMD();
				Lan_Para.f_OutBedPD_Calibration = 0;
		}			
		if(Lan_Para.f_InBedPD_Calibration ==1){//�ڴ�У׼
				Lan_Para.f_InBedPD_Calibration = 0;
				API_ACK_27CMD();
		}			
		if(Lan_Para.f_ChangIP_Port == 1)//�޸ķ�����IP����ַ
		{
				API_ACK_29CMD();
				API_W5500_Reset();
			  Lan_Para.f_WriteSeverInfo =1;
			 // API_do_tcp_client(); 
				Lan_Para.f_ChangIP_Port =0;
		}		
		 API_Write_SeverInfo();//д����flash��Ϣ
}

void API_Write_SeverInfo(void)
{
		static uint16_t Cnt_20ms=0;
	  if(Lan_Para.f_WriteSeverInfo==1)//��ʱ�ж�
			Cnt_20ms++;
		else
			Cnt_20ms=0;
	  if(Lan_Para.f_LogOnSever==1)//��������¼�ɹ� //�����������
		{
				 uint8_t  i,TempBuff[16]={0};
				 EEPROM_SPI_ReadBuffer(TempBuff,ADDr_eeProm_LAN_IP_Port, 16);
				 TempBuff[7] =0x01;//�Ƿ񱣴������IP�Ͷ˿�
		 
				for(i=0;i<4;i++)
						TempBuff[8+i] = Lan_Para.IP_S[i] ;
				TempBuff[12] = (Lan_Para.Port_S>>8)&0xff;
				TempBuff[13] = (Lan_Para.Port_S)&0xff;
				if( EEPROM_SPI_WriteBuffer(TempBuff,ADDr_eeProm_LAN_IP_Port, 16) ==0x01)
				{
					printf("�޸ķ������ɹ�дflash�ɹ�\r\n");
				}	
				else
				{
						printf("�޸ķ������ɹ�дflashʧ��\r\n");
						for(uint8_t i=0;i<4;i++)
								Lan_Para.IP_S[i] = Lan_Para.Old_IP_S[i];
						Lan_Para.Port_S =	Lan_Para.Old_Port_S ;//���ݶ˿�
						API_W5500_Reset();
					
				}
				Lan_Para.f_LogOnSever=0;
		}
		else
		{
			  if(Cnt_20ms>250)//����250ms��ʱ
				{
						for(uint8_t i=0;i<4;i++)
								Lan_Para.IP_S[i] = Lan_Para.Old_IP_S[i];
						Lan_Para.Port_S =	Lan_Para.Old_Port_S ;//���ݶ˿�
						API_W5500_Reset();
						printf("�޸ķ�����ʧ�ܣ��豸���Ӳ��Ϸ�������������ķ����\r\n");
				  	Lan_Para.f_WriteSeverInfo =0;
				}
		}
}
extern uint8_t HardWare_Ver[];
uint16_t  API_LAN_Info_To_String(uint8_t *Temp)//������Ϣת�ַ���
{
		uint8_t  Temp_MAC[12];
	  uint8_t  Temp_IP_S[20];//������IP
	  uint8_t  Temp_IP_S_Len;
	  uint8_t  Temp_Port_S[5];//�������˿�
	  uint8_t  Temp_Port_S_Len;
    uint8_t  Temp_Ver[7];
	  uint8_t  Temp_IP_B0[20];//����IP
		uint8_t  Temp_IP_B0_Len;
	  uint8_t  Temp_Port_B0[5];//�����˿�
		uint8_t  Temp_Port_B0_Len;
	uint8_t i,j,k,n,m;
	HEXArrayToStringArray(Lan_Para.MAC,Temp_MAC,sizeof(Lan_Para.MAC));//MAC��ַת�ַ���
	i = API_Itoi(Temp_IP_S,Lan_Para.IP_S[0]); 
		Temp_IP_S[i] = '.';	
	j = API_Itoi(Temp_IP_S+i+1,Lan_Para.IP_S[1]); 
	  Temp_IP_S[j+i+1] = '.';	
	k = API_Itoi(Temp_IP_S+j+i+1+1,Lan_Para.IP_S[2]); 
		Temp_IP_S[k+j+i+1+1] = '.';	
	m = API_Itoi(Temp_IP_S+k+j+i+1+1+1,Lan_Para.IP_S[3]); 
		Temp_IP_S[m+k+j+i+1+1+1] = ':';		
	Temp_IP_S_Len = m+k+j+i+1+1+1+1;
	Temp_Port_S_Len =  API_Itoi(Temp_Port_S, Lan_Para.Port_S);
	
	i = API_Itoi(Temp_IP_B0,Lan_Para.IP_B0[0]); 
		Temp_IP_B0[i] = '.';	
	j = API_Itoi(Temp_IP_B0+i+1,Lan_Para.IP_B0[1]); 
	  Temp_IP_B0[j+i+1] = '.';	
	k = API_Itoi(Temp_IP_B0+j+i+1+1,Lan_Para.IP_B0[2]); 
		Temp_IP_B0[k+j+i+1+1] = '.';	
	m = API_Itoi(Temp_IP_B0+k+j+i+1+1+1,Lan_Para.IP_B0[3]); 
		Temp_IP_B0[m+k+j+i+1+1+1] = ':';		
	Temp_IP_B0_Len = m+k+j+i+1+1+1+1;
	Temp_Port_B0_Len =  API_Itoi(Temp_Port_B0, Lan_Para.Port_B0);
	strcpy((char *)Temp,"-MAC:");
	strcpy((char *)Temp+5,(char *)Temp_MAC);
	strcpy((char *)Temp+5+12,(char *)'-');
	strcpy((char *)Temp+5+13,(char *)Temp_IP_S);
	strcpy((char *)Temp+5+13+Temp_IP_S_Len,(char *)Temp_Port_S);
	strcpy((char *)Temp+5+13+Temp_IP_S_Len+Temp_Port_S_Len,(char *)'-');
	strcpy((char *)Temp+5+13+Temp_IP_S_Len+Temp_Port_S_Len+1,(char *)Temp_IP_B0);
	strcpy((char *)Temp+5+13+Temp_IP_S_Len+Temp_Port_S_Len+1+Temp_IP_B0_Len,(char *)Temp_Port_B0);
	
	Temp_Ver[0] = '-';Temp_Ver[1] = 'V';;Temp_Ver[2] = 'e';;Temp_Ver[3] = 'r';
	Temp_Ver[4] = HardWare_Ver[0]+0x30;Temp_Ver[5] = '.';Temp_Ver[6] = HardWare_Ver[1]+0x30;

	strcpy((char *)Temp+5+13+Temp_IP_S_Len+Temp_Port_S_Len+Temp_IP_B0_Len+Temp_Port_B0_Len,(char *)Temp_Ver);
	n =Temp_IP_S_Len+5+13+Temp_Port_S_Len+Temp_IP_B0_Len+Temp_Port_B0_Len+7;	
	return n;
	
}


void API_Erase_Flash(void)
{
		uint8_t tempbuff[16];
		memset(  tempbuff, '\0', 16);
		EEPROM_SPI_WriteBuffer(tempbuff,ADDr_eeProm_LAN_IP_Port, 16);
		EEPROM_SPI_WriteBuffer(tempbuff,ADDr_eeProm_LAN_MAC, 16);
		EEPROM_SPI_WriteBuffer(tempbuff,ADDr_eeProm_LAN_IP_Port, 16);
		EEPROM_SPI_WriteBuffer(tempbuff,Addr_DevId+Addr_Back, 16);
    EEPROM_SPI_WriteBuffer(tempbuff,Addr_DevId, 16);
	  EEPROM_SPI_WriteBuffer(tempbuff,Addr_HardVer, 16);
	  EEPROM_SPI_WriteBuffer(tempbuff,Addr_HardVer+Addr_Back, 16);
		printf("flash�����ɹ�\r\n");
}
