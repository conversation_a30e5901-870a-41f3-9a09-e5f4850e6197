.\objects\matrixfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\MatrixFunctionsF16.c
.\objects\matrixfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_add_f16.c
.\objects\matrixfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/matrix_functions_f16.h
.\objects\matrixfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\arm_math_types_f16.h
.\objects\matrixfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\arm_math_types.h
.\objects\matrixfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h
.\objects\matrixfunctionsf16.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\matrixfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h
.\objects\matrixfunctionsf16.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
.\objects\matrixfunctionsf16.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\math.h
.\objects\matrixfunctionsf16.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\float.h
.\objects\matrixfunctionsf16.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\limits.h
.\objects\matrixfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\arm_math_memory.h
.\objects\matrixfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/none.h
.\objects\matrixfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/utils.h
.\objects\matrixfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_sub_f16.c
.\objects\matrixfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_trans_f16.c
.\objects\matrixfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_scale_f16.c
.\objects\matrixfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_mult_f16.c
.\objects\matrixfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_vec_mult_f16.c
.\objects\matrixfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_cmplx_trans_f16.c
.\objects\matrixfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_cmplx_mult_f16.c
.\objects\matrixfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_inverse_f16.c
.\objects\matrixfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_init_f16.c
.\objects\matrixfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_cholesky_f16.c
.\objects\matrixfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_solve_upper_triangular_f16.c
.\objects\matrixfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\MatrixFunctions\arm_mat_solve_lower_triangular_f16.c
