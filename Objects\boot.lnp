--cpu=Cortex-M4.fp.sp
".\objects\startup_gd32f450_470.o"
".\objects\gd32f4xx_adc.o"
".\objects\gd32f4xx_can.o"
".\objects\gd32f4xx_crc.o"
".\objects\gd32f4xx_ctc.o"
".\objects\gd32f4xx_dac.o"
".\objects\gd32f4xx_dbg.o"
".\objects\gd32f4xx_dci.o"
".\objects\gd32f4xx_dma.o"
".\objects\gd32f4xx_enet.o"
".\objects\gd32f4xx_exmc.o"
".\objects\gd32f4xx_exti.o"
".\objects\gd32f4xx_fmc.o"
".\objects\gd32f4xx_fwdgt.o"
".\objects\gd32f4xx_gpio.o"
".\objects\gd32f4xx_i2c.o"
".\objects\gd32f4xx_ipa.o"
".\objects\gd32f4xx_iref.o"
".\objects\gd32f4xx_misc.o"
".\objects\gd32f4xx_pmu.o"
".\objects\gd32f4xx_rcu.o"
".\objects\gd32f4xx_rtc.o"
".\objects\gd32f4xx_sdio.o"
".\objects\gd32f4xx_spi.o"
".\objects\gd32f4xx_syscfg.o"
".\objects\gd32f4xx_timer.o"
".\objects\gd32f4xx_tli.o"
".\objects\gd32f4xx_trng.o"
".\objects\gd32f4xx_usart.o"
".\objects\gd32f4xx_wwdgt.o"
".\objects\system_gd32f4xx.o"
".\objects\gd32f4xx_it.o"
".\objects\main.o"
".\objects\systick.o"
".\objects\25lc080a.o"
".\objects\adc.o"
".\objects\dac.o"
".\objects\dma.o"
".\objects\eeprom_spi.o"
".\objects\esp32_wifi.o"
".\objects\flash.o"
".\objects\gpio.o"
".\objects\my_crc.o"
".\objects\mymath.o"
".\objects\rtc.o"
".\objects\spi.o"
".\objects\time.o"
".\objects\usart.o"
".\objects\usb.o"
".\objects\user_step.o"
".\objects\gd32f470v_start.o"
".\Utilities\whut_math_gd32.lib"
".\objects\api_lan_data_process .o"
".\objects\api_tnrg.o"
".\objects\api_w5500.o"
".\objects\drv_usb_core.o"
".\objects\drv_usb_dev.o"
".\objects\drv_usbd_int.o"
".\objects\usbd_core.o"
".\objects\usbd_enum.o"
".\objects\usbd_transc.o"
".\objects\gd32f4xx_hw.o"
".\objects\cdc_acm_core.o"
".\objects\socket.o"
".\objects\utility.o"
".\objects\w5500.o"
".\objects\w5500_conf.o"
".\objects\dhcp.o"
".\objects\tcp_client.o"
"C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.7.0\CMSIS\DSP\Lib\ARM\arm_cortexM4lf_math.lib"
--library_type=microlib --strict --scatter ".\Objects\boot.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list ".\Listings\boot.map" -o .\Objects\boot.axf