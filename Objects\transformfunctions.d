.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\TransformFunctions.c
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_bitreversal.c
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/transform_functions.h
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\arm_math_types.h
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h
.\objects\transformfunctions.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h
.\objects\transformfunctions.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
.\objects\transformfunctions.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\math.h
.\objects\transformfunctions.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\float.h
.\objects\transformfunctions.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\limits.h
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\arm_math_memory.h
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/none.h
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/utils.h
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/basic_math_functions.h
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/complex_math_functions.h
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/fast_math_functions.h
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\arm_common_tables.h
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_bitreversal2.c
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_cfft_f32.c
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_cfft_f64.c
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_cfft_q15.c
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_cfft_q31.c
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_cfft_init_f32.c
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\arm_const_structs.h
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_cfft_init_f64.c
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_cfft_init_q15.c
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_cfft_init_q31.c
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_cfft_radix2_f32.c
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_cfft_radix2_q15.c
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_cfft_radix2_q31.c
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_cfft_radix4_f32.c
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_cfft_radix4_q15.c
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_cfft_radix4_q31.c
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_cfft_radix8_f32.c
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_rfft_fast_f32.c
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_rfft_fast_f64.c
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_rfft_fast_init_f32.c
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_rfft_fast_init_f64.c
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_dct4_f32.c
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_dct4_init_f32.c
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_dct4_init_q15.c
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_dct4_init_q31.c
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_dct4_q15.c
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_dct4_q31.c
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_rfft_f32.c
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_rfft_q15.c
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_rfft_q31.c
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_rfft_init_f32.c
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_rfft_init_q15.c
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_rfft_init_q31.c
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_cfft_radix4_init_f32.c
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_cfft_radix4_init_q15.c
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_cfft_radix4_init_q31.c
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_cfft_radix2_init_f32.c
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_cfft_radix2_init_q15.c
.\objects\transformfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_cfft_radix2_init_q31.c
