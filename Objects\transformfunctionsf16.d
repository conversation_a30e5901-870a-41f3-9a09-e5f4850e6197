.\objects\transformfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\TransformFunctionsF16.c
.\objects\transformfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_cfft_f16.c
.\objects\transformfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/transform_functions_f16.h
.\objects\transformfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\arm_math_types_f16.h
.\objects\transformfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\arm_math_types.h
.\objects\transformfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h
.\objects\transformfunctionsf16.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\transformfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h
.\objects\transformfunctionsf16.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
.\objects\transformfunctionsf16.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\math.h
.\objects\transformfunctionsf16.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\float.h
.\objects\transformfunctionsf16.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\limits.h
.\objects\transformfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\arm_math_memory.h
.\objects\transformfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/none.h
.\objects\transformfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/utils.h
.\objects\transformfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\arm_common_tables_f16.h
.\objects\transformfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_cfft_init_f16.c
.\objects\transformfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\arm_const_structs_f16.h
.\objects\transformfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\arm_common_tables.h
.\objects\transformfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/fast_math_functions.h
.\objects\transformfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_cfft_radix2_f16.c
.\objects\transformfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_cfft_radix4_f16.c
.\objects\transformfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_rfft_fast_init_f16.c
.\objects\transformfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_rfft_fast_f16.c
.\objects\transformfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_cfft_radix8_f16.c
.\objects\transformfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_bitreversal_f16.c
.\objects\transformfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_cfft_radix2_init_f16.c
.\objects\transformfunctionsf16.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\TransformFunctions\arm_cfft_radix4_init_f16.c
