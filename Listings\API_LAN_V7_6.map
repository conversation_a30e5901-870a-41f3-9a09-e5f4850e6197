Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_gd32f450_470.o(RESET) refers to startup_gd32f450_470.o(STACK) for __initial_sp
    startup_gd32f450_470.o(RESET) refers to startup_gd32f450_470.o(.text) for Reset_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.TIMER2_IRQHandler) for TIMER2_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.TIMER3_IRQHandler) for TIMER3_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.EXTI10_15_IRQHandler) for EXTI10_15_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.TIMER6_IRQHandler) for TIMER6_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.DMA1_Channel0_IRQHandler) for DMA1_Channel0_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.DMA1_Channel1_IRQHandler) for DMA1_Channel1_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.DMA1_Channel2_IRQHandler) for DMA1_Channel2_IRQHandler
    startup_gd32f450_470.o(.text) refers to system_gd32f4xx.o(i.SystemInit) for SystemInit
    startup_gd32f450_470.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    gd32f4xx_adc.o(i.adc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_adc.o(i.adc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_can.o(i.can_debug_freeze_disable) refers to gd32f4xx_dbg.o(i.dbg_periph_disable) for dbg_periph_disable
    gd32f4xx_can.o(i.can_debug_freeze_enable) refers to gd32f4xx_dbg.o(i.dbg_periph_enable) for dbg_periph_enable
    gd32f4xx_can.o(i.can_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_can.o(i.can_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_can.o(i.can_interrupt_flag_get) refers to gd32f4xx_can.o(i.can_receive_message_length_get) for can_receive_message_length_get
    gd32f4xx_can.o(i.can_interrupt_flag_get) refers to gd32f4xx_can.o(i.can_error_get) for can_error_get
    gd32f4xx_ctc.o(i.ctc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_ctc.o(i.ctc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_dac.o(i.dac_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_dac.o(i.dac_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_dci.o(i.dci_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_dci.o(i.dci_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_enet.o(i.enet_initpara_reset) for enet_initpara_reset
    gd32f4xx_enet.o(i.enet_descriptors_chain_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_descriptors_chain_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_descriptors_ring_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_descriptors_ring_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_disable) refers to gd32f4xx_enet.o(i.enet_tx_disable) for enet_tx_disable
    gd32f4xx_enet.o(i.enet_disable) refers to gd32f4xx_enet.o(i.enet_rx_disable) for enet_rx_disable
    gd32f4xx_enet.o(i.enet_enable) refers to gd32f4xx_enet.o(i.enet_tx_enable) for enet_tx_enable
    gd32f4xx_enet.o(i.enet_enable) refers to gd32f4xx_enet.o(i.enet_rx_enable) for enet_rx_enable
    gd32f4xx_enet.o(i.enet_frame_receive) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_frame_transmit) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_phy_config) for enet_phy_config
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_delay) for enet_delay
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_default_init) for enet_default_init
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_initpara_config) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_initpara_reset) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_enet.o(i.enet_delay) for enet_delay
    gd32f4xx_enet.o(i.enet_phyloopback_disable) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_phyloopback_enable) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_ptpframe_receive_normal_mode) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_ptpframe_transmit_normal_mode) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_registers_get) refers to gd32f4xx_enet.o(.constdata) for enet_reg_tab
    gd32f4xx_enet.o(i.enet_rxframe_drop) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_rxframe_size_get) refers to gd32f4xx_enet.o(i.enet_rxframe_drop) for enet_rxframe_drop
    gd32f4xx_enet.o(i.enet_rxframe_size_get) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_rxprocess_check_recovery) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_tx_disable) refers to gd32f4xx_enet.o(i.enet_txfifo_flush) for enet_txfifo_flush
    gd32f4xx_enet.o(i.enet_tx_enable) refers to gd32f4xx_enet.o(i.enet_txfifo_flush) for enet_txfifo_flush
    gd32f4xx_fmc.o(i.fmc_bank0_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_bank1_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_byte_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_halfword_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_mass_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_page_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_ready_wait) refers to gd32f4xx_fmc.o(i.fmc_state_get) for fmc_state_get
    gd32f4xx_fmc.o(i.fmc_sector_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_word_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_drp_disable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_drp_enable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_security_protection_config) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_user_write) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_write_protection_disable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_write_protection_enable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_gpio.o(i.gpio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_gpio.o(i.gpio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_i2c.o(i.i2c_clock_config) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_i2c.o(i.i2c_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_i2c.o(i.i2c_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_ipa.o(i.ipa_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_ipa.o(i.ipa_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_iref.o(i.iref_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_iref.o(i.iref_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_misc.o(i.nvic_irq_enable) refers to gd32f4xx_misc.o(i.nvic_priority_group_set) for nvic_priority_group_set
    gd32f4xx_pmu.o(i.pmu_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_pmu.o(i.pmu_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_pmu.o(i.pmu_highdriver_switch_select) refers to gd32f4xx_pmu.o(i.pmu_flag_get) for pmu_flag_get
    gd32f4xx_pmu.o(i.pmu_to_deepsleepmode) refers to gd32f4xx_pmu.o(.bss) for reg_snap
    gd32f4xx_rcu.o(i.rcu_deinit) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    gd32f4xx_rcu.o(i.rcu_osci_stab_wait) refers to gd32f4xx_rcu.o(i.rcu_flag_get) for rcu_flag_get
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_config) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_config) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_deinit) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_deinit) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_rtc.o(i.rtc_refclock_detection_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_refclock_detection_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_refclock_detection_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_refclock_detection_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_second_adjust) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_sdio.o(i.sdio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_sdio.o(i.sdio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_osci_on) for rcu_osci_on
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_i2s_clock_config) for rcu_i2s_clock_config
    gd32f4xx_spi.o(i.spi_i2s_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_spi.o(i.spi_i2s_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_syscfg.o(i.syscfg_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_syscfg.o(i.syscfg_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_timer.o(i.timer_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_timer.o(i.timer_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_timer.o(i.timer_external_clock_mode0_config) refers to gd32f4xx_timer.o(i.timer_external_trigger_config) for timer_external_trigger_config
    gd32f4xx_timer.o(i.timer_external_clock_mode1_config) refers to gd32f4xx_timer.o(i.timer_external_trigger_config) for timer_external_trigger_config
    gd32f4xx_timer.o(i.timer_external_trigger_as_external_clock_config) refers to gd32f4xx_timer.o(i.timer_input_trigger_source_select) for timer_input_trigger_source_select
    gd32f4xx_timer.o(i.timer_input_capture_config) refers to gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config) for timer_channel_input_capture_prescaler_config
    gd32f4xx_timer.o(i.timer_input_pwm_capture_config) refers to gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config) for timer_channel_input_capture_prescaler_config
    gd32f4xx_timer.o(i.timer_internal_trigger_as_external_clock_config) refers to gd32f4xx_timer.o(i.timer_input_trigger_source_select) for timer_input_trigger_source_select
    gd32f4xx_tli.o(i.tli_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_tli.o(i.tli_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_trng.o(i.trng_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_trng.o(i.trng_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_usart.o(i.usart_baudrate_set) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_usart.o(i.usart_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_usart.o(i.usart_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_wwdgt.o(i.wwdgt_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_wwdgt.o(i.wwdgt_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    system_gd32f4xx.o(i.SystemCoreClockUpdate) refers to system_gd32f4xx.o(.data) for SystemCoreClock
    system_gd32f4xx.o(i.SystemInit) refers to system_gd32f4xx.o(i.system_clock_config) for system_clock_config
    system_gd32f4xx.o(i.system_clock_config) refers to system_gd32f4xx.o(i.system_clock_168m_25m_hxtal) for system_clock_168m_25m_hxtal
    gd32f4xx_it.o(i.HardFault_Handler) refers to user_step.o(i.SysReset_Condition) for SysReset_Condition
    gd32f4xx_it.o(i.SysTick_Handler) refers to systick.o(i.delay_decrement) for delay_decrement
    gd32f4xx_it.o(i.TIMER3_IRQHandler) refers to gd32f4xx_timer.o(i.timer_interrupt_flag_get) for timer_interrupt_flag_get
    gd32f4xx_it.o(i.TIMER3_IRQHandler) refers to time.o(i.TIM_PeriodElapsedCallback) for TIM_PeriodElapsedCallback
    gd32f4xx_it.o(i.TIMER6_IRQHandler) refers to gd32f4xx_timer.o(i.timer_interrupt_flag_get) for timer_interrupt_flag_get
    gd32f4xx_it.o(i.TIMER6_IRQHandler) refers to time.o(i.TIM_PeriodElapsedCallback) for TIM_PeriodElapsedCallback
    gd32f4xx_it.o(i.USART2_IRQHandler) refers to gd32f4xx_usart.o(i.usart_interrupt_flag_get) for usart_interrupt_flag_get
    gd32f4xx_it.o(i.USART2_IRQHandler) refers to usart.o(i.UART_RxCpltCallback) for UART_RxCpltCallback
    gd32f4xx_it.o(i.USART2_IRQHandler) refers to usart.o(i.UART_IDLECallBack) for UART_IDLECallBack
    main.o(i.main) refers to gd32f4xx_misc.o(i.nvic_vector_table_set) for nvic_vector_table_set
    main.o(i.main) refers to systick.o(i.systick_config) for systick_config
    main.o(i.main) refers to gpio.o(i.GPIO_Init) for GPIO_Init
    main.o(i.main) refers to usart.o(i.USART0_Init) for USART0_Init
    main.o(i.main) refers to printfa.o(i.__0printf) for __2printf
    main.o(i.main) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    main.o(i.main) refers to time.o(i.TIMER6_Init) for TIMER6_Init
    main.o(i.main) refers to time.o(i.TIMER3_Init) for TIMER3_Init
    main.o(i.main) refers to rtc.o(i.RTC_Init) for RTC_Init
    main.o(i.main) refers to time.o(i.TIMER1_Init) for TIMER1_Init
    main.o(i.main) refers to spi.o(i.SPI1_Init) for SPI1_Init
    main.o(i.main) refers to api_tnrg.o(i.API_RNG_Init) for API_RNG_Init
    main.o(i.main) refers to gd32f4xx_timer.o(i.timer_enable) for timer_enable
    main.o(i.main) refers to api_w5500.o(i.API_Init_LAN) for API_Init_LAN
    main.o(i.main) refers to api_w5500.o(i.API_W5500_Reset) for API_W5500_Reset
    main.o(i.main) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    main.o(i.main) refers to usart.o(i.API_do_tcp_client) for API_do_tcp_client
    main.o(i.main) refers to api_lan_data_process .o(i.API_ExecuteSeverCMD) for API_ExecuteSeverCMD
    main.o(i.main) refers to api_w5500.o(i.API_PHY_Check) for API_PHY_Check
    main.o(i.main) refers to main.o(i.print_system_status) for print_system_status
    main.o(i.main) refers to main.o(.data) for Soft_Ver
    main.o(i.main) refers to time.o(.bss) for g_tTimeSign
    main.o(i.print_system_status) refers to printfa.o(i.__0printf) for __2printf
    main.o(i.print_system_status) refers to main.o(.data) for status_counter
    main.o(i.print_system_status) refers to api_w5500.o(.RAM_D3) for Lan_Para
    systick.o(i.delay_1ms) refers to systick.o(.data) for delay
    systick.o(i.delay_decrement) refers to systick.o(.data) for delay
    systick.o(i.systick_config) refers to systick.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    systick.o(i.systick_config) refers to system_gd32f4xx.o(.data) for SystemCoreClock
    25lc080a.o(i.EEPROM_WritePage) refers to malloc.o(i.malloc) for malloc
    25lc080a.o(i.EEPROM_WritePage) refers to eeprom_spi.o(i.EEPROM_SPI_WriteBuffer) for EEPROM_SPI_WriteBuffer
    25lc080a.o(i.EEPROM_WritePage) refers to eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) for EEPROM_SPI_ReadBuffer
    25lc080a.o(i.EEPROM_WritePage) refers to malloc.o(i.free) for free
    25lc080a.o(i.EEPROM_WritePage) refers to my_crc.o(i.CRC16_USB) for CRC16_USB
    25lc080a.o(i.EEPROM_WritePage) refers to 25lc080a.o(i.USER_EEPROM_WriteByte) for USER_EEPROM_WriteByte
    25lc080a.o(i.EEPROM_WritePage) refers to 25lc080a.o(i.USER_EEPROM_RedeByte) for USER_EEPROM_RedeByte
    25lc080a.o(i.USER_EEPROM_RedeByte) refers to eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) for EEPROM_SPI_ReadBuffer
    25lc080a.o(i.USER_EEPROM_RedePage) refers to malloc.o(i.malloc) for malloc
    25lc080a.o(i.USER_EEPROM_RedePage) refers to eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) for EEPROM_SPI_ReadBuffer
    25lc080a.o(i.USER_EEPROM_RedePage) refers to my_crc.o(i.CRC16_USB) for CRC16_USB
    25lc080a.o(i.USER_EEPROM_RedePage) refers to malloc.o(i.free) for free
    25lc080a.o(i.USER_EEPROM_WriteByte) refers to malloc.o(i.malloc) for malloc
    25lc080a.o(i.USER_EEPROM_WriteByte) refers to eeprom_spi.o(i.EEPROM_SPI_WriteBuffer) for EEPROM_SPI_WriteBuffer
    25lc080a.o(i.USER_EEPROM_WriteByte) refers to eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) for EEPROM_SPI_ReadBuffer
    25lc080a.o(i.USER_EEPROM_WriteByte) refers to my_crc.o(i.CRC16_USB) for CRC16_USB
    25lc080a.o(i.USER_EEPROM_WriteByte) refers to 25lc080a.o(i.USER_EEPROM_RedeByte) for USER_EEPROM_RedeByte
    25lc080a.o(i.USER_EEPROM_WriteByte) refers to malloc.o(i.free) for free
    25lc080a.o(i.USER_EEPROM_WritePage) refers to 25lc080a.o(i.EEPROM_WritePage) for EEPROM_WritePage
    eeprom_spi.o(i.EEPROM_ReadStatusRegister) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    eeprom_spi.o(i.EEPROM_ReadStatusRegister) refers to eeprom_spi.o(i.EEPROM_SendByte) for EEPROM_SendByte
    eeprom_spi.o(i.EEPROM_ReadStatusRegister) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) refers to systick.o(i.delay_1ms) for delay_1ms
    eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) refers to eeprom_spi.o(i.EEPROM_SPI_SendInstruction) for EEPROM_SPI_SendInstruction
    eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) refers to spi.o(i.DRV_SPI_SwapByte) for DRV_SPI_SwapByte
    eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    eeprom_spi.o(i.EEPROM_SPI_SendInstruction) refers to systick.o(i.delay_1ms) for delay_1ms
    eeprom_spi.o(i.EEPROM_SPI_SendInstruction) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    eeprom_spi.o(i.EEPROM_SPI_SendInstruction) refers to spi.o(i.DRV_SPI_SwapByte) for DRV_SPI_SwapByte
    eeprom_spi.o(i.EEPROM_SPI_WaitStandbyState) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    eeprom_spi.o(i.EEPROM_SPI_WaitStandbyState) refers to eeprom_spi.o(i.EEPROM_SPI_SendInstruction) for EEPROM_SPI_SendInstruction
    eeprom_spi.o(i.EEPROM_SPI_WaitStandbyState) refers to spi.o(i.DRV_SPI_SwapByte) for DRV_SPI_SwapByte
    eeprom_spi.o(i.EEPROM_SPI_WaitStandbyState) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    eeprom_spi.o(i.EEPROM_SPI_WriteBuffer) refers to eeprom_spi.o(i.EEPROM_SPI_WritePage) for EEPROM_SPI_WritePage
    eeprom_spi.o(i.EEPROM_SPI_WritePage) refers to systick.o(i.delay_1ms) for delay_1ms
    eeprom_spi.o(i.EEPROM_SPI_WritePage) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    eeprom_spi.o(i.EEPROM_SPI_WritePage) refers to eeprom_spi.o(i.EEPROM_WriteEnable) for EEPROM_WriteEnable
    eeprom_spi.o(i.EEPROM_SPI_WritePage) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    eeprom_spi.o(i.EEPROM_SPI_WritePage) refers to eeprom_spi.o(i.EEPROM_SPI_SendInstruction) for EEPROM_SPI_SendInstruction
    eeprom_spi.o(i.EEPROM_SPI_WritePage) refers to spi.o(i.DRV_SPI_SwapByte) for DRV_SPI_SwapByte
    eeprom_spi.o(i.EEPROM_SPI_WritePage) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    eeprom_spi.o(i.EEPROM_SPI_WritePage) refers to eeprom_spi.o(i.EEPROM_SPI_WaitStandbyState) for EEPROM_SPI_WaitStandbyState
    eeprom_spi.o(i.EEPROM_SPI_WritePage) refers to eeprom_spi.o(i.EEPROM_WriteDisable) for EEPROM_WriteDisable
    eeprom_spi.o(i.EEPROM_SendByte) refers to systick.o(i.delay_1ms) for delay_1ms
    eeprom_spi.o(i.EEPROM_SendByte) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    eeprom_spi.o(i.EEPROM_SendByte) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    eeprom_spi.o(i.EEPROM_SendByte) refers to gd32f4xx_spi.o(i.spi_i2s_data_receive) for spi_i2s_data_receive
    eeprom_spi.o(i.EEPROM_WriteDisable) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    eeprom_spi.o(i.EEPROM_WriteDisable) refers to eeprom_spi.o(i.EEPROM_SPI_SendInstruction) for EEPROM_SPI_SendInstruction
    eeprom_spi.o(i.EEPROM_WriteDisable) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    eeprom_spi.o(i.EEPROM_WriteEnable) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    eeprom_spi.o(i.EEPROM_WriteEnable) refers to eeprom_spi.o(i.EEPROM_SPI_SendInstruction) for EEPROM_SPI_SendInstruction
    eeprom_spi.o(i.EEPROM_WriteEnable) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    eeprom_spi.o(i.EEPROM_WriteStatusRegister) refers to eeprom_spi.o(i.EEPROM_WriteEnable) for EEPROM_WriteEnable
    eeprom_spi.o(i.EEPROM_WriteStatusRegister) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    eeprom_spi.o(i.EEPROM_WriteStatusRegister) refers to eeprom_spi.o(i.EEPROM_SPI_SendInstruction) for EEPROM_SPI_SendInstruction
    eeprom_spi.o(i.EEPROM_WriteStatusRegister) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    eeprom_spi.o(i.EEPROM_WriteStatusRegister) refers to eeprom_spi.o(i.EEPROM_WriteDisable) for EEPROM_WriteDisable
    eeprom_spi.o(i.EEPROM_WriteStatusRegister) refers to eeprom_spi.o(i.EEPROM_SPI_WaitStandbyState) for EEPROM_SPI_WaitStandbyState
    flash.o(i.bsp_EraseCpuFlash) refers to gd32f4xx_fmc.o(i.fmc_unlock) for fmc_unlock
    flash.o(i.bsp_EraseCpuFlash) refers to flash.o(i.FLASH_If_Init) for FLASH_If_Init
    flash.o(i.bsp_EraseCpuFlash) refers to flash.o(i.bsp_GetSector) for bsp_GetSector
    flash.o(i.bsp_EraseCpuFlash) refers to gd32f4xx_fmc.o(i.fmc_sector_erase) for fmc_sector_erase
    flash.o(i.bsp_EraseCpuFlash) refers to gd32f4xx_fmc.o(i.fmc_lock) for fmc_lock
    flash.o(i.bsp_WriteCpuFlash) refers to flash.o(i.bsp_CmpCpuFlash) for bsp_CmpCpuFlash
    flash.o(i.bsp_WriteCpuFlash) refers to gd32f4xx_fmc.o(i.fmc_unlock) for fmc_unlock
    flash.o(i.bsp_WriteCpuFlash) refers to gd32f4xx_fmc.o(i.fmc_word_program) for fmc_word_program
    flash.o(i.bsp_WriteCpuFlash) refers to memcpya.o(.text) for __aeabi_memcpy
    flash.o(i.bsp_WriteCpuFlash) refers to gd32f4xx_fmc.o(i.fmc_lock) for fmc_lock
    gpio.o(i.API_Chose_TS5A3359_GAIN) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    gpio.o(i.API_Chose_TS5A3359_GAIN) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    gpio.o(i.API_LED_GPIO) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gpio.o(i.API_LED_GPIO) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    gpio.o(i.API_LED_GPIO) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    gpio.o(i.GPIO_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gpio.o(i.GPIO_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    gpio.o(i.GPIO_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    gpio.o(i.GPIO_Init) refers to gpio.o(i.Init_GPIO_TS5A339) for Init_GPIO_TS5A339
    gpio.o(i.GPIO_Init) refers to gpio.o(i.API_Chose_TS5A3359_GAIN) for API_Chose_TS5A3359_GAIN
    gpio.o(i.GPIO_Init) refers to gpio.o(i.API_LED_GPIO) for API_LED_GPIO
    gpio.o(i.GPIO_Init) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    gpio.o(i.Init_GPIO_TS5A339) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gpio.o(i.Init_GPIO_TS5A339) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    gpio.o(i.Init_GPIO_TS5A339) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    my_crc.o(i.CRC16_USB) refers to my_crc.o(i.InvertUint8) for InvertUint8
    my_crc.o(i.CRC16_USB) refers to my_crc.o(i.InvertUint16) for InvertUint16
    rtc.o(i.RTC_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    rtc.o(i.RTC_Init) refers to gd32f4xx_pmu.o(i.pmu_backup_write_enable) for pmu_backup_write_enable
    rtc.o(i.RTC_Init) refers to gd32f4xx_rcu.o(i.rcu_osci_on) for rcu_osci_on
    rtc.o(i.RTC_Init) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    rtc.o(i.RTC_Init) refers to gd32f4xx_rcu.o(i.rcu_rtc_clock_config) for rcu_rtc_clock_config
    rtc.o(i.RTC_Init) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    rtc.o(i.RTC_Init) refers to gd32f4xx_rtc.o(i.rtc_init) for rtc_init
    rtc.o(i.RTC_Init) refers to rtc.o(.bss) for rtc_initpara
    rtc.o(i.rtc_register_set) refers to gd32f4xx_rtc.o(i.rtc_init) for rtc_init
    rtc.o(i.rtc_register_set) refers to rtc.o(.bss) for rtc_initpara
    spi.o(i.DRV_SPI_SwapByte) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    spi.o(i.DRV_SPI_SwapByte) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    spi.o(i.DRV_SPI_SwapByte) refers to gd32f4xx_spi.o(i.spi_i2s_data_receive) for spi_i2s_data_receive
    spi.o(i.SPI1_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    spi.o(i.SPI1_Init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    spi.o(i.SPI1_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    spi.o(i.SPI1_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    spi.o(i.SPI1_Init) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    spi.o(i.SPI1_Init) refers to gd32f4xx_spi.o(i.spi_init) for spi_init
    spi.o(i.SPI1_Init) refers to gd32f4xx_spi.o(i.spi_enable) for spi_enable
    time.o(i.TIMER1_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    time.o(i.TIMER1_Init) refers to gd32f4xx_timer.o(i.timer_deinit) for timer_deinit
    time.o(i.TIMER1_Init) refers to gd32f4xx_timer.o(i.timer_init) for timer_init
    time.o(i.TIMER1_Init) refers to gd32f4xx_timer.o(i.timer_channel_output_config) for timer_channel_output_config
    time.o(i.TIMER1_Init) refers to gd32f4xx_timer.o(i.timer_channel_output_pulse_value_config) for timer_channel_output_pulse_value_config
    time.o(i.TIMER1_Init) refers to gd32f4xx_timer.o(i.timer_channel_output_mode_config) for timer_channel_output_mode_config
    time.o(i.TIMER1_Init) refers to gd32f4xx_timer.o(i.timer_channel_output_shadow_config) for timer_channel_output_shadow_config
    time.o(i.TIMER3_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    time.o(i.TIMER3_Init) refers to gd32f4xx_timer.o(i.timer_deinit) for timer_deinit
    time.o(i.TIMER3_Init) refers to gd32f4xx_timer.o(i.timer_init) for timer_init
    time.o(i.TIMER3_Init) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    time.o(i.TIMER3_Init) refers to gd32f4xx_timer.o(i.timer_interrupt_enable) for timer_interrupt_enable
    time.o(i.TIMER3_Init) refers to gd32f4xx_timer.o(i.timer_enable) for timer_enable
    time.o(i.TIMER6_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    time.o(i.TIMER6_Init) refers to gd32f4xx_timer.o(i.timer_deinit) for timer_deinit
    time.o(i.TIMER6_Init) refers to gd32f4xx_timer.o(i.timer_init) for timer_init
    time.o(i.TIMER6_Init) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    time.o(i.TIMER6_Init) refers to gd32f4xx_timer.o(i.timer_interrupt_enable) for timer_interrupt_enable
    time.o(i.TIMER6_Init) refers to gd32f4xx_timer.o(i.timer_enable) for timer_enable
    time.o(i.TIM_PeriodElapsedCallback) refers to gd32f4xx_timer.o(i.timer_interrupt_flag_clear) for timer_interrupt_flag_clear
    time.o(i.TIM_PeriodElapsedCallback) refers to user_step.o(i.SysReset_Condition) for SysReset_Condition
    time.o(i.TIM_PeriodElapsedCallback) refers to time.o(.data) for tim6_msTic
    time.o(i.TIM_PeriodElapsedCallback) refers to dhcp.o(.data) for dhcp_time
    time.o(i.TIM_PeriodElapsedCallback) refers to api_w5500.o(.RAM_D3) for Lan_Para
    time.o(i.TIM_PeriodElapsedCallback) refers to time.o(.bss) for g_tTimeSign
    usart.o(i.API_Printf_Hex) refers to printfa.o(i.__0printf) for __2printf
    usart.o(i.API_do_tcp_client) refers to w5500.o(i.getSn_SR) for getSn_SR
    usart.o(i.API_do_tcp_client) refers to printfa.o(i.__0printf) for __2printf
    usart.o(i.API_do_tcp_client) refers to socket.o(i.socket) for socket
    usart.o(i.API_do_tcp_client) refers to socket.o(i.connect) for connect
    usart.o(i.API_do_tcp_client) refers to w5500.o(i.getSn_IR) for getSn_IR
    usart.o(i.API_do_tcp_client) refers to w5500.o(i.setSn_IR) for setSn_IR
    usart.o(i.API_do_tcp_client) refers to w5500.o(i.getSn_RX_RSR) for getSn_RX_RSR
    usart.o(i.API_do_tcp_client) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    usart.o(i.API_do_tcp_client) refers to socket.o(i.recv) for recv
    usart.o(i.API_do_tcp_client) refers to usart.o(i.API_Printf_Hex) for API_Printf_Hex
    usart.o(i.API_do_tcp_client) refers to api_lan_data_process .o(i.API_Process_Lan_Data) for API_Process_Lan_Data
    usart.o(i.API_do_tcp_client) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    usart.o(i.API_do_tcp_client) refers to socket.o(i.disconnect) for disconnect
    usart.o(i.API_do_tcp_client) refers to socket.o(i.close) for close
    usart.o(i.API_do_tcp_client) refers to usart.o(.data) for last_state
    usart.o(i.API_do_tcp_client) refers to api_w5500.o(.RAM_D3) for Lan_Para
    usart.o(i.ESP32_IO_Init) refers to memseta.o(.text) for __aeabi_memclr4
    usart.o(i.ESP32_IO_Init) refers to usart.o(i.InitBuffer) for InitBuffer
    usart.o(i.ESP32_IO_Init) refers to usart.o(.RAM_D3) for sg_tUsartDriveHandle
    usart.o(i.FML_USART_RecvTask) refers to memseta.o(.text) for __aeabi_memclr4
    usart.o(i.FML_USART_RecvTask) refers to usart.o(i.ReadBytesToBuffer) for ReadBytesToBuffer
    usart.o(i.FML_USART_RecvTask) refers to memcpya.o(.text) for __aeabi_memcpy
    usart.o(i.FML_USART_RecvTask) refers to usart.o(.RAM_D3) for sg_tUsartDriveHandle
    usart.o(i.FML_USART_Register) refers to usart.o(.RAM_D3) for sg_tUsartDriveHandle
    usart.o(i.InitBuffer) refers to memseta.o(.text) for __aeabi_memclr
    usart.o(i.ReadBytesToBuffer) refers to memcpya.o(.text) for __aeabi_memcpy
    usart.o(i.RecvDataHandler) refers to usart.o(i.AddByteToBuffer) for AddByteToBuffer
    usart.o(i.UART_IDLECallBack) refers to gd32f4xx_usart.o(i.usart_interrupt_flag_clear) for usart_interrupt_flag_clear
    usart.o(i.UART_IDLECallBack) refers to gd32f4xx_usart.o(i.usart_data_receive) for usart_data_receive
    usart.o(i.UART_IDLECallBack) refers to usart.o(i.FML_USART_RecvTask) for FML_USART_RecvTask
    usart.o(i.UART_RxCpltCallback) refers to gd32f4xx_usart.o(i.usart_data_receive) for usart_data_receive
    usart.o(i.UART_RxCpltCallback) refers to usart.o(i.RecvDataHandler) for RecvDataHandler
    usart.o(i.UART_RxCpltCallback) refers to usart.o(.RAM_D3) for usart2_buf
    usart.o(i.UARTx_SendBuffer) refers to gd32f4xx_usart.o(i.usart_flag_get) for usart_flag_get
    usart.o(i.UARTx_SendBuffer) refers to gd32f4xx_usart.o(i.usart_data_transmit) for usart_data_transmit
    usart.o(i.USART0_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    usart.o(i.USART0_Init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    usart.o(i.USART0_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    usart.o(i.USART0_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    usart.o(i.USART0_Init) refers to gd32f4xx_usart.o(i.usart_deinit) for usart_deinit
    usart.o(i.USART0_Init) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    usart.o(i.USART0_Init) refers to gd32f4xx_usart.o(i.usart_baudrate_set) for usart_baudrate_set
    usart.o(i.USART0_Init) refers to gd32f4xx_usart.o(i.usart_receive_config) for usart_receive_config
    usart.o(i.USART0_Init) refers to gd32f4xx_usart.o(i.usart_transmit_config) for usart_transmit_config
    usart.o(i.USART0_Init) refers to gd32f4xx_usart.o(i.usart_word_length_set) for usart_word_length_set
    usart.o(i.USART0_Init) refers to gd32f4xx_usart.o(i.usart_stop_bit_set) for usart_stop_bit_set
    usart.o(i.USART0_Init) refers to gd32f4xx_usart.o(i.usart_parity_config) for usart_parity_config
    usart.o(i.USART0_Init) refers to gd32f4xx_usart.o(i.usart_hardware_flow_rts_config) for usart_hardware_flow_rts_config
    usart.o(i.USART0_Init) refers to gd32f4xx_usart.o(i.usart_hardware_flow_cts_config) for usart_hardware_flow_cts_config
    usart.o(i.USART0_Init) refers to gd32f4xx_usart.o(i.usart_enable) for usart_enable
    usart.o(i.USART0_Init) refers to printfa.o(i.__0printf) for __2printf
    usart.o(i.USART2_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    usart.o(i.USART2_Init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    usart.o(i.USART2_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    usart.o(i.USART2_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    usart.o(i.USART2_Init) refers to gd32f4xx_usart.o(i.usart_deinit) for usart_deinit
    usart.o(i.USART2_Init) refers to gd32f4xx_usart.o(i.usart_baudrate_set) for usart_baudrate_set
    usart.o(i.USART2_Init) refers to gd32f4xx_usart.o(i.usart_receive_config) for usart_receive_config
    usart.o(i.USART2_Init) refers to gd32f4xx_usart.o(i.usart_transmit_config) for usart_transmit_config
    usart.o(i.USART2_Init) refers to gd32f4xx_usart.o(i.usart_enable) for usart_enable
    usart.o(i.USART2_Init) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    usart.o(i.USART2_Init) refers to gd32f4xx_usart.o(i.usart_interrupt_enable) for usart_interrupt_enable
    usart.o(i.fputc) refers to gd32f4xx_usart.o(i.usart_data_transmit) for usart_data_transmit
    usart.o(i.fputc) refers to gd32f4xx_usart.o(i.usart_flag_get) for usart_flag_get
    user_step.o(i.SysReset_Condition) refers to user_step.o(.NoInit) for g_JumpInit
    gd32f470v_start.o(i.gd_eval_BEEP_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f470v_start.o(i.gd_eval_BEEP_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    gd32f470v_start.o(i.gd_eval_BEEP_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    gd32f470v_start.o(i.gd_eval_BEEP_init) refers to gd32f470v_start.o(.data) for BEEP_CLK
    gd32f470v_start.o(i.gd_eval_BEEP_off) refers to gd32f470v_start.o(.data) for BEEP_GPIO_PIN
    gd32f470v_start.o(i.gd_eval_BEEP_on) refers to gd32f470v_start.o(.data) for BEEP_GPIO_PIN
    gd32f470v_start.o(i.gd_eval_key_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f470v_start.o(i.gd_eval_key_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    gd32f470v_start.o(i.gd_eval_key_init) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    gd32f470v_start.o(i.gd_eval_key_init) refers to gd32f4xx_syscfg.o(i.syscfg_exti_line_config) for syscfg_exti_line_config
    gd32f470v_start.o(i.gd_eval_key_init) refers to gd32f4xx_exti.o(i.exti_init) for exti_init
    gd32f470v_start.o(i.gd_eval_key_init) refers to gd32f4xx_exti.o(i.exti_interrupt_flag_clear) for exti_interrupt_flag_clear
    gd32f470v_start.o(i.gd_eval_key_init) refers to gd32f470v_start.o(.data) for KEY_CLK
    gd32f470v_start.o(i.gd_eval_key_state_get) refers to gd32f4xx_gpio.o(i.gpio_input_bit_get) for gpio_input_bit_get
    gd32f470v_start.o(i.gd_eval_key_state_get) refers to gd32f470v_start.o(.data) for KEY_PIN
    gd32f470v_start.o(i.gd_eval_led_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f470v_start.o(i.gd_eval_led_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    gd32f470v_start.o(i.gd_eval_led_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    gd32f470v_start.o(i.gd_eval_led_init) refers to gd32f470v_start.o(.data) for GPIO_CLK
    gd32f470v_start.o(i.gd_eval_led_off) refers to gd32f470v_start.o(.data) for GPIO_PIN
    gd32f470v_start.o(i.gd_eval_led_on) refers to gd32f470v_start.o(.data) for GPIO_PIN
    gd32f470v_start.o(i.gd_eval_led_toggle) refers to gd32f470v_start.o(.data) for GPIO_PIN
    api_lan_data_process .o(i.API_ACK_21CMD) refers to memseta.o(.text) for __aeabi_memclr4
    api_lan_data_process .o(i.API_ACK_21CMD) refers to my_crc.o(i.CRC16_USB) for CRC16_USB
    api_lan_data_process .o(i.API_ACK_21CMD) refers to socket.o(i.send) for send
    api_lan_data_process .o(i.API_ACK_21CMD) refers to printfa.o(i.__0printf) for __2printf
    api_lan_data_process .o(i.API_ACK_21CMD) refers to usart.o(i.API_Printf_Hex) for API_Printf_Hex
    api_lan_data_process .o(i.API_ACK_22CMD) refers to flash.o(i.bsp_EraseCpuFlash) for bsp_EraseCpuFlash
    api_lan_data_process .o(i.API_ACK_22CMD) refers to my_crc.o(i.CRC16_USB) for CRC16_USB
    api_lan_data_process .o(i.API_ACK_22CMD) refers to socket.o(i.send) for send
    api_lan_data_process .o(i.API_ACK_22CMD) refers to printfa.o(i.__0printf) for __2printf
    api_lan_data_process .o(i.API_ACK_22CMD) refers to usart.o(i.API_Printf_Hex) for API_Printf_Hex
    api_lan_data_process .o(i.API_ACK_22CMD) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_lan_data_process .o(i.API_ACK_23CMD) refers to memseta.o(.text) for __aeabi_memclr4
    api_lan_data_process .o(i.API_ACK_23CMD) refers to my_crc.o(i.CRC16_USB) for CRC16_USB
    api_lan_data_process .o(i.API_ACK_23CMD) refers to flash.o(i.bsp_WriteCpuFlash) for bsp_WriteCpuFlash
    api_lan_data_process .o(i.API_ACK_23CMD) refers to flash.o(i.bsp_CmpCpuFlash) for bsp_CmpCpuFlash
    api_lan_data_process .o(i.API_ACK_23CMD) refers to socket.o(i.send) for send
    api_lan_data_process .o(i.API_ACK_23CMD) refers to printfa.o(i.__0printf) for __2printf
    api_lan_data_process .o(i.API_ACK_23CMD) refers to usart.o(i.API_Printf_Hex) for API_Printf_Hex
    api_lan_data_process .o(i.API_ACK_23CMD) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_lan_data_process .o(i.API_ACK_24CMD) refers to memseta.o(.text) for __aeabi_memclr4
    api_lan_data_process .o(i.API_ACK_24CMD) refers to my_crc.o(i.CRC16_USB) for CRC16_USB
    api_lan_data_process .o(i.API_ACK_24CMD) refers to socket.o(i.send) for send
    api_lan_data_process .o(i.API_ACK_24CMD) refers to printfa.o(i.__0printf) for __2printf
    api_lan_data_process .o(i.API_ACK_24CMD) refers to usart.o(i.API_Printf_Hex) for API_Printf_Hex
    api_lan_data_process .o(i.API_ACK_24CMD) refers to 25lc080a.o(i.USER_EEPROM_WriteByte) for USER_EEPROM_WriteByte
    api_lan_data_process .o(i.API_ACK_24CMD) refers to user_step.o(i.SysReset_Condition) for SysReset_Condition
    api_lan_data_process .o(i.API_ACK_24CMD) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_lan_data_process .o(i.API_ACK_26CMD) refers to memseta.o(.text) for __aeabi_memclr4
    api_lan_data_process .o(i.API_ACK_26CMD) refers to my_crc.o(i.CRC16_USB) for CRC16_USB
    api_lan_data_process .o(i.API_ACK_26CMD) refers to socket.o(i.send) for send
    api_lan_data_process .o(i.API_ACK_26CMD) refers to printfa.o(i.__0printf) for __2printf
    api_lan_data_process .o(i.API_ACK_26CMD) refers to usart.o(i.API_Printf_Hex) for API_Printf_Hex
    api_lan_data_process .o(i.API_ACK_26CMD) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_lan_data_process .o(i.API_ACK_26CMD) refers to main.o(.data) for Soft_Ver
    api_lan_data_process .o(i.API_ACK_28CMD) refers to memseta.o(.text) for __aeabi_memclr4
    api_lan_data_process .o(i.API_ACK_28CMD) refers to my_crc.o(i.CRC16_USB) for CRC16_USB
    api_lan_data_process .o(i.API_ACK_28CMD) refers to socket.o(i.send) for send
    api_lan_data_process .o(i.API_ACK_28CMD) refers to printfa.o(i.__0printf) for __2printf
    api_lan_data_process .o(i.API_ACK_28CMD) refers to usart.o(i.API_Printf_Hex) for API_Printf_Hex
    api_lan_data_process .o(i.API_ACK_29CMD) refers to memseta.o(.text) for __aeabi_memclr4
    api_lan_data_process .o(i.API_ACK_29CMD) refers to my_crc.o(i.CRC16_USB) for CRC16_USB
    api_lan_data_process .o(i.API_ACK_29CMD) refers to socket.o(i.send) for send
    api_lan_data_process .o(i.API_ACK_29CMD) refers to printfa.o(i.__0printf) for __2printf
    api_lan_data_process .o(i.API_ACK_29CMD) refers to usart.o(i.API_Printf_Hex) for API_Printf_Hex
    api_lan_data_process .o(i.API_ACK_29CMD) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_lan_data_process .o(i.API_AnalysisIpPortAddr) refers to atoi.o(.text) for atoi
    api_lan_data_process .o(i.API_AnalysisIpPortAddr) refers to strchr.o(.text) for strchr
    api_lan_data_process .o(i.API_Erase_Flash) refers to eeprom_spi.o(i.EEPROM_SPI_WriteBuffer) for EEPROM_SPI_WriteBuffer
    api_lan_data_process .o(i.API_Erase_Flash) refers to printfa.o(i.__0printf) for __2printf
    api_lan_data_process .o(i.API_ExecuteSeverCMD) refers to api_lan_data_process .o(i.API_ACK_20CMD) for API_ACK_20CMD
    api_lan_data_process .o(i.API_ExecuteSeverCMD) refers to api_lan_data_process .o(i.API_ACK_21CMD) for API_ACK_21CMD
    api_lan_data_process .o(i.API_ExecuteSeverCMD) refers to api_lan_data_process .o(i.API_ACK_22CMD) for API_ACK_22CMD
    api_lan_data_process .o(i.API_ExecuteSeverCMD) refers to api_lan_data_process .o(i.API_ACK_23CMD) for API_ACK_23CMD
    api_lan_data_process .o(i.API_ExecuteSeverCMD) refers to api_lan_data_process .o(i.API_ACK_24CMD) for API_ACK_24CMD
    api_lan_data_process .o(i.API_ExecuteSeverCMD) refers to api_lan_data_process .o(i.API_ACK_26CMD) for API_ACK_26CMD
    api_lan_data_process .o(i.API_ExecuteSeverCMD) refers to api_lan_data_process .o(i.API_ACK_28CMD) for API_ACK_28CMD
    api_lan_data_process .o(i.API_ExecuteSeverCMD) refers to api_lan_data_process .o(i.API_ACK_25CMD) for API_ACK_25CMD
    api_lan_data_process .o(i.API_ExecuteSeverCMD) refers to api_lan_data_process .o(i.API_ACK_27CMD) for API_ACK_27CMD
    api_lan_data_process .o(i.API_ExecuteSeverCMD) refers to api_lan_data_process .o(i.API_ACK_29CMD) for API_ACK_29CMD
    api_lan_data_process .o(i.API_ExecuteSeverCMD) refers to api_w5500.o(i.API_W5500_Reset) for API_W5500_Reset
    api_lan_data_process .o(i.API_ExecuteSeverCMD) refers to api_lan_data_process .o(i.API_Write_SeverInfo) for API_Write_SeverInfo
    api_lan_data_process .o(i.API_ExecuteSeverCMD) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_lan_data_process .o(i.API_LAN_Info_To_String) refers to api_lan_data_process .o(i.HEXArrayToStringArray) for HEXArrayToStringArray
    api_lan_data_process .o(i.API_LAN_Info_To_String) refers to api_lan_data_process .o(i.API_Itoi) for API_Itoi
    api_lan_data_process .o(i.API_LAN_Info_To_String) refers to strcpy.o(.text) for strcpy
    api_lan_data_process .o(i.API_LAN_Info_To_String) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_lan_data_process .o(i.API_LAN_Info_To_String) refers to main.o(.data) for HardWare_Ver
    api_lan_data_process .o(i.API_LanSock1Send_CRC) refers to my_crc.o(i.CRC16_USB) for CRC16_USB
    api_lan_data_process .o(i.API_LanSock1Send_CRC) refers to socket.o(i.send) for send
    api_lan_data_process .o(i.API_LanSock1Send_CRC) refers to usart.o(i.API_Printf_Hex) for API_Printf_Hex
    api_lan_data_process .o(i.API_LanSock1Send_CRC) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_lan_data_process .o(i.API_Process_Lan_Data) refers to printfa.o(i.__0printf) for __2printf
    api_lan_data_process .o(i.API_Process_Lan_Data) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_lan_data_process .o(i.API_Write_SeverInfo) refers to eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) for EEPROM_SPI_ReadBuffer
    api_lan_data_process .o(i.API_Write_SeverInfo) refers to eeprom_spi.o(i.EEPROM_SPI_WriteBuffer) for EEPROM_SPI_WriteBuffer
    api_lan_data_process .o(i.API_Write_SeverInfo) refers to printfa.o(i.__0printf) for __2printf
    api_lan_data_process .o(i.API_Write_SeverInfo) refers to api_w5500.o(i.API_W5500_Reset) for API_W5500_Reset
    api_lan_data_process .o(i.API_Write_SeverInfo) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_lan_data_process .o(i.API_Write_SeverInfo) refers to api_lan_data_process .o(.data) for Cnt_20ms
    api_lan_data_process .o(i.HEXArrayToStringArray) refers to printfa.o(i.__0sprintf) for __2sprintf
    api_tnrg.o(i.API_RNG_Init) refers to eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) for EEPROM_SPI_ReadBuffer
    api_tnrg.o(i.API_RNG_Init) refers to printfa.o(i.__0printf) for __2printf
    api_tnrg.o(i.API_RNG_Init) refers to usart.o(i.API_Printf_Hex) for API_Printf_Hex
    api_tnrg.o(i.API_RNG_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    api_tnrg.o(i.API_RNG_Init) refers to gd32f4xx_trng.o(i.trng_deinit) for trng_deinit
    api_tnrg.o(i.API_RNG_Init) refers to gd32f4xx_trng.o(i.trng_enable) for trng_enable
    api_tnrg.o(i.API_RNG_Init) refers to gd32f4xx_trng.o(i.trng_flag_get) for trng_flag_get
    api_tnrg.o(i.API_RNG_Init) refers to api_tnrg.o(i.get_hard_rand_data) for get_hard_rand_data
    api_tnrg.o(i.API_RNG_Init) refers to eeprom_spi.o(i.EEPROM_SPI_WriteBuffer) for EEPROM_SPI_WriteBuffer
    api_tnrg.o(i.API_RNG_Init) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_tnrg.o(i.get_hard_rand_data) refers to gd32f4xx_trng.o(i.trng_flag_get) for trng_flag_get
    api_tnrg.o(i.get_hard_rand_data) refers to gd32f4xx_trng.o(i.trng_get_true_random_data) for trng_get_true_random_data
    api_w5500.o(i.API_Init_LAN) refers to api_w5500.o(i.API_W5500_SPI0_Init) for API_W5500_SPI0_Init
    api_w5500.o(i.API_Init_LAN) refers to api_w5500.o(i.API_W5500_GPIO_Init) for API_W5500_GPIO_Init
    api_w5500.o(i.API_Init_LAN) refers to api_w5500.o(i.API_Init_Net_Parameters) for API_Init_Net_Parameters
    api_w5500.o(i.API_Init_Net_Parameters) refers to eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) for EEPROM_SPI_ReadBuffer
    api_w5500.o(i.API_Init_Net_Parameters) refers to printfa.o(i.__0printf) for __2printf
    api_w5500.o(i.API_Init_Net_Parameters) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_w5500.o(i.API_PHY_Check) refers to w5500_conf.o(i.getPHYStatus) for getPHYStatus
    api_w5500.o(i.API_PHY_Check) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    api_w5500.o(i.API_PHY_Check) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    api_w5500.o(i.API_PHY_Check) refers to user_step.o(i.SysReset_Condition) for SysReset_Condition
    api_w5500.o(i.API_PHY_Check) refers to printfa.o(i.__0printf) for __2printf
    api_w5500.o(i.API_PHY_Check) refers to api_w5500.o(.data) for i
    api_w5500.o(i.API_PHY_Check) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_w5500.o(i.API_SPI0_Send_Read_Byte) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    api_w5500.o(i.API_SPI0_Send_Read_Byte) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    api_w5500.o(i.API_SPI0_Send_Read_Byte) refers to gd32f4xx_spi.o(i.spi_i2s_data_receive) for spi_i2s_data_receive
    api_w5500.o(i.API_W5500_GPIO_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    api_w5500.o(i.API_W5500_GPIO_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    api_w5500.o(i.API_W5500_GPIO_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    api_w5500.o(i.API_W5500_Reset) refers to printfa.o(i.__0printf) for __2printf
    api_w5500.o(i.API_W5500_Reset) refers to w5500_conf.o(i.reset_w5500) for reset_w5500
    api_w5500.o(i.API_W5500_Reset) refers to w5500_conf.o(i.PHY_check) for PHY_check
    api_w5500.o(i.API_W5500_Reset) refers to w5500_conf.o(i.set_w5500_mac) for set_w5500_mac
    api_w5500.o(i.API_W5500_Reset) refers to w5500.o(i.socket_buf_init) for socket_buf_init
    api_w5500.o(i.API_W5500_Reset) refers to dhcp.o(i.DHCP_run) for DHCP_run
    api_w5500.o(i.API_W5500_Reset) refers to main.o(.data) for dhcp_ok
    api_w5500.o(i.API_W5500_Reset) refers to dhcp.o(.data) for Conflict_flag
    api_w5500.o(i.API_W5500_Reset) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_w5500.o(i.API_W5500_Reset) refers to w5500.o(.data) for rxsize
    api_w5500.o(i.API_W5500_SPI0_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    api_w5500.o(i.API_W5500_SPI0_Init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    api_w5500.o(i.API_W5500_SPI0_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    api_w5500.o(i.API_W5500_SPI0_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    api_w5500.o(i.API_W5500_SPI0_Init) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    api_w5500.o(i.API_W5500_SPI0_Init) refers to gd32f4xx_spi.o(i.spi_init) for spi_init
    api_w5500.o(i.API_W5500_SPI0_Init) refers to gd32f4xx_spi.o(i.spi_enable) for spi_enable
    socket.o(i.close) refers to w5500_conf.o(i.IINCHIP_WRITE) for IINCHIP_WRITE
    socket.o(i.close) refers to w5500_conf.o(i.IINCHIP_READ) for IINCHIP_READ
    socket.o(i.connect) refers to w5500_conf.o(i.IINCHIP_WRITE) for IINCHIP_WRITE
    socket.o(i.connect) refers to w5500_conf.o(i.IINCHIP_READ) for IINCHIP_READ
    socket.o(i.connect) refers to w5500.o(i.getSn_IR) for getSn_IR
    socket.o(i.connect) refers to printfa.o(i.__0printf) for __2printf
    socket.o(i.connect) refers to api_w5500.o(.RAM_D3) for Lan_Para
    socket.o(i.disconnect) refers to w5500_conf.o(i.IINCHIP_WRITE) for IINCHIP_WRITE
    socket.o(i.disconnect) refers to w5500_conf.o(i.IINCHIP_READ) for IINCHIP_READ
    socket.o(i.listen) refers to w5500_conf.o(i.IINCHIP_READ) for IINCHIP_READ
    socket.o(i.listen) refers to w5500_conf.o(i.IINCHIP_WRITE) for IINCHIP_WRITE
    socket.o(i.recv) refers to w5500.o(i.recv_data_processing) for recv_data_processing
    socket.o(i.recv) refers to w5500_conf.o(i.IINCHIP_WRITE) for IINCHIP_WRITE
    socket.o(i.recv) refers to w5500_conf.o(i.IINCHIP_READ) for IINCHIP_READ
    socket.o(i.recvfrom) refers to w5500_conf.o(i.IINCHIP_READ) for IINCHIP_READ
    socket.o(i.recvfrom) refers to w5500_conf.o(i.wiz_read_buf) for wiz_read_buf
    socket.o(i.recvfrom) refers to w5500_conf.o(i.IINCHIP_WRITE) for IINCHIP_WRITE
    socket.o(i.recvfrom) refers to printfa.o(i.__0printf) for __2printf
    socket.o(i.send) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    socket.o(i.send) refers to w5500.o(i.getIINCHIP_TxMAX) for getIINCHIP_TxMAX
    socket.o(i.send) refers to w5500.o(i.getSn_TX_FSR) for getSn_TX_FSR
    socket.o(i.send) refers to w5500_conf.o(i.IINCHIP_READ) for IINCHIP_READ
    socket.o(i.send) refers to w5500.o(i.send_data_processing) for send_data_processing
    socket.o(i.send) refers to w5500_conf.o(i.IINCHIP_WRITE) for IINCHIP_WRITE
    socket.o(i.send) refers to printfa.o(i.__0printf) for __2printf
    socket.o(i.send) refers to socket.o(i.close) for close
    socket.o(i.send) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    socket.o(i.send) refers to api_w5500.o(.RAM_D3) for Lan_Para
    socket.o(i.sendto) refers to w5500.o(i.getIINCHIP_TxMAX) for getIINCHIP_TxMAX
    socket.o(i.sendto) refers to w5500_conf.o(i.IINCHIP_WRITE) for IINCHIP_WRITE
    socket.o(i.sendto) refers to w5500.o(i.send_data_processing) for send_data_processing
    socket.o(i.sendto) refers to w5500_conf.o(i.IINCHIP_READ) for IINCHIP_READ
    socket.o(i.socket) refers to socket.o(i.close) for close
    socket.o(i.socket) refers to w5500_conf.o(i.IINCHIP_WRITE) for IINCHIP_WRITE
    socket.o(i.socket) refers to w5500_conf.o(i.IINCHIP_READ) for IINCHIP_READ
    socket.o(i.socket) refers to api_w5500.o(.RAM_D3) for Lan_Para
    utility.o(i.atoi16) refers to utility.o(i.c2d) for c2d
    utility.o(i.atoi32) refers to utility.o(i.c2d) for c2d
    utility.o(i.htonl) refers to utility.o(i.swapl) for swapl
    utility.o(i.htons) refers to utility.o(i.swaps) for swaps
    utility.o(i.inet_addr_) refers to strcpy.o(.text) for strcpy
    utility.o(i.inet_addr_) refers to strtok.o(.text) for strtok
    utility.o(i.inet_addr_) refers to utility.o(i.atoi16) for atoi16
    utility.o(i.inet_ntoa) refers to memseta.o(.text) for __aeabi_memclr
    utility.o(i.inet_ntoa) refers to printfa.o(i.__0sprintf) for __2sprintf
    utility.o(i.inet_ntoa) refers to utility.o(.bss) for addr_str
    utility.o(i.inet_ntoa_pad) refers to memseta.o(.text) for __aeabi_memclr
    utility.o(i.inet_ntoa_pad) refers to printfa.o(i.__0printf) for printf
    utility.o(i.inet_ntoa_pad) refers to utility.o(.bss) for addr_str
    utility.o(i.itoa) refers to memseta.o(.text) for __aeabi_memset
    utility.o(i.mid) refers to strstr.o(.text) for strstr
    utility.o(i.mid) refers to strlen.o(.text) for strlen
    utility.o(i.mid) refers to strncpy.o(.text) for strncpy
    utility.o(i.ntohl) refers to utility.o(i.htonl) for htonl
    utility.o(i.ntohs) refers to utility.o(i.htons) for htons
    utility.o(i.validatoi) refers to utility.o(i.c2d) for c2d
    utility.o(i.validatoi) refers to utility.o(i.atoi16) for atoi16
    utility.o(i.verify_ip_address) refers to strcpy.o(.text) for strcpy
    utility.o(i.verify_ip_address) refers to strtok.o(.text) for strtok
    utility.o(i.verify_ip_address) refers to utility.o(i.validatoi) for validatoi
    w5500.o(i.clearIR) refers to w5500.o(i.getIR) for getIR
    w5500.o(i.clearIR) refers to w5500_conf.o(i.IINCHIP_WRITE) for IINCHIP_WRITE
    w5500.o(i.getGAR) refers to w5500_conf.o(i.wiz_read_buf) for wiz_read_buf
    w5500.o(i.getIINCHIP_RxMAX) refers to w5500.o(.bss) for RSIZE
    w5500.o(i.getIINCHIP_TxMAX) refers to w5500.o(.bss) for SSIZE
    w5500.o(i.getIR) refers to w5500_conf.o(i.IINCHIP_READ) for IINCHIP_READ
    w5500.o(i.getSHAR) refers to w5500_conf.o(i.wiz_read_buf) for wiz_read_buf
    w5500.o(i.getSIPR) refers to w5500_conf.o(i.wiz_read_buf) for wiz_read_buf
    w5500.o(i.getSUBR) refers to w5500_conf.o(i.wiz_read_buf) for wiz_read_buf
    w5500.o(i.getSn_IR) refers to w5500_conf.o(i.IINCHIP_READ) for IINCHIP_READ
    w5500.o(i.getSn_RX_RSR) refers to w5500_conf.o(i.IINCHIP_READ) for IINCHIP_READ
    w5500.o(i.getSn_SR) refers to w5500_conf.o(i.IINCHIP_READ) for IINCHIP_READ
    w5500.o(i.getSn_TX_FSR) refers to w5500_conf.o(i.IINCHIP_READ) for IINCHIP_READ
    w5500.o(i.iinchip_init) refers to w5500.o(i.setMR) for setMR
    w5500.o(i.recv_data_processing) refers to printfa.o(i.__0printf) for __2printf
    w5500.o(i.recv_data_processing) refers to w5500_conf.o(i.IINCHIP_READ) for IINCHIP_READ
    w5500.o(i.recv_data_processing) refers to w5500_conf.o(i.wiz_read_buf) for wiz_read_buf
    w5500.o(i.recv_data_processing) refers to w5500_conf.o(i.IINCHIP_WRITE) for IINCHIP_WRITE
    w5500.o(i.send_data_processing) refers to printfa.o(i.__0printf) for __2printf
    w5500.o(i.send_data_processing) refers to w5500_conf.o(i.IINCHIP_READ) for IINCHIP_READ
    w5500.o(i.send_data_processing) refers to w5500_conf.o(i.wiz_write_buf) for wiz_write_buf
    w5500.o(i.send_data_processing) refers to w5500_conf.o(i.IINCHIP_WRITE) for IINCHIP_WRITE
    w5500.o(i.setGAR) refers to w5500_conf.o(i.wiz_write_buf) for wiz_write_buf
    w5500.o(i.setMR) refers to w5500_conf.o(i.IINCHIP_WRITE) for IINCHIP_WRITE
    w5500.o(i.setRCR) refers to w5500_conf.o(i.IINCHIP_WRITE) for IINCHIP_WRITE
    w5500.o(i.setRTR) refers to w5500_conf.o(i.IINCHIP_WRITE) for IINCHIP_WRITE
    w5500.o(i.setSHAR) refers to w5500_conf.o(i.wiz_write_buf) for wiz_write_buf
    w5500.o(i.setSIPR) refers to w5500_conf.o(i.wiz_write_buf) for wiz_write_buf
    w5500.o(i.setSUBR) refers to w5500_conf.o(i.wiz_write_buf) for wiz_write_buf
    w5500.o(i.setSn_IR) refers to w5500_conf.o(i.IINCHIP_WRITE) for IINCHIP_WRITE
    w5500.o(i.setSn_MSS) refers to w5500_conf.o(i.IINCHIP_WRITE) for IINCHIP_WRITE
    w5500.o(i.setSn_TTL) refers to w5500_conf.o(i.IINCHIP_WRITE) for IINCHIP_WRITE
    w5500.o(i.socket_buf_init) refers to w5500_conf.o(i.IINCHIP_WRITE) for IINCHIP_WRITE
    w5500.o(i.socket_buf_init) refers to w5500.o(.bss) for SSIZE
    w5500_conf.o(i.IINCHIP_READ) refers to w5500_conf.o(i.iinchip_csoff) for iinchip_csoff
    w5500_conf.o(i.IINCHIP_READ) refers to w5500_conf.o(i.IINCHIP_SpiSendData) for IINCHIP_SpiSendData
    w5500_conf.o(i.IINCHIP_READ) refers to w5500_conf.o(i.iinchip_cson) for iinchip_cson
    w5500_conf.o(i.IINCHIP_SpiSendData) refers to api_w5500.o(i.API_SPI0_Send_Read_Byte) for API_SPI0_Send_Read_Byte
    w5500_conf.o(i.IINCHIP_WRITE) refers to w5500_conf.o(i.iinchip_csoff) for iinchip_csoff
    w5500_conf.o(i.IINCHIP_WRITE) refers to w5500_conf.o(i.IINCHIP_SpiSendData) for IINCHIP_SpiSendData
    w5500_conf.o(i.IINCHIP_WRITE) refers to w5500_conf.o(i.iinchip_cson) for iinchip_cson
    w5500_conf.o(i.PHY_check) refers to w5500_conf.o(i.getPHYStatus) for getPHYStatus
    w5500_conf.o(i.PHY_check) refers to printfa.o(i.__0printf) for __2printf
    w5500_conf.o(i.PHY_check) refers to socket.o(i.close) for close
    w5500_conf.o(i.PHY_check) refers to systick.o(i.delay_1ms) for delay_1ms
    w5500_conf.o(i.dhcp_timer_init) refers to w5500_conf.o(i.timer2_init) for timer2_init
    w5500_conf.o(i.getPHYStatus) refers to w5500_conf.o(i.IINCHIP_READ) for IINCHIP_READ
    w5500_conf.o(i.iinchip_csoff) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    w5500_conf.o(i.iinchip_cson) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    w5500_conf.o(i.ntp_timer_init) refers to w5500_conf.o(i.timer2_init) for timer2_init
    w5500_conf.o(i.reset_w5500) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    w5500_conf.o(i.reset_w5500) refers to systick.o(i.delay_1ms) for delay_1ms
    w5500_conf.o(i.reset_w5500) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    w5500_conf.o(i.reset_w5500) refers to printfa.o(i.__0printf) for __2printf
    w5500_conf.o(i.reset_w5500) refers to api_w5500.o(.RAM_D3) for Lan_Para
    w5500_conf.o(i.setKPALVTR) refers to w5500_conf.o(i.IINCHIP_WRITE) for IINCHIP_WRITE
    w5500_conf.o(i.set_w5500_mac) refers to w5500.o(i.setSHAR) for setSHAR
    w5500_conf.o(i.set_w5500_mac) refers to w5500.o(i.getSHAR) for getSHAR
    w5500_conf.o(i.set_w5500_mac) refers to printfa.o(i.__0printf) for __2printf
    w5500_conf.o(i.set_w5500_mac) refers to api_w5500.o(.RAM_D3) for Lan_Para
    w5500_conf.o(i.set_w5500_netinfo) refers to printfa.o(i.__0printf) for __2printf
    w5500_conf.o(i.set_w5500_netinfo) refers to w5500.o(i.setSUBR) for setSUBR
    w5500_conf.o(i.set_w5500_netinfo) refers to w5500.o(i.setGAR) for setGAR
    w5500_conf.o(i.set_w5500_netinfo) refers to w5500.o(i.setSIPR) for setSIPR
    w5500_conf.o(i.set_w5500_netinfo) refers to w5500.o(i.getSIPR) for getSIPR
    w5500_conf.o(i.set_w5500_netinfo) refers to w5500.o(i.getSUBR) for getSUBR
    w5500_conf.o(i.set_w5500_netinfo) refers to w5500.o(i.getGAR) for getGAR
    w5500_conf.o(i.set_w5500_netinfo) refers to w5500_conf.o(.data) for ip_from
    w5500_conf.o(i.set_w5500_netinfo) refers to api_w5500.o(.RAM_D3) for Lan_Para
    w5500_conf.o(i.timer2_isr) refers to w5500_conf.o(.data) for ms
    w5500_conf.o(i.timer2_isr) refers to dhcp.o(.data) for dhcp_time
    w5500_conf.o(i.wiz_read_buf) refers to printfa.o(i.__0printf) for __2printf
    w5500_conf.o(i.wiz_read_buf) refers to w5500_conf.o(i.iinchip_csoff) for iinchip_csoff
    w5500_conf.o(i.wiz_read_buf) refers to w5500_conf.o(i.IINCHIP_SpiSendData) for IINCHIP_SpiSendData
    w5500_conf.o(i.wiz_read_buf) refers to w5500_conf.o(i.iinchip_cson) for iinchip_cson
    w5500_conf.o(i.wiz_write_buf) refers to printfa.o(i.__0printf) for __2printf
    w5500_conf.o(i.wiz_write_buf) refers to w5500_conf.o(i.iinchip_csoff) for iinchip_csoff
    w5500_conf.o(i.wiz_write_buf) refers to w5500_conf.o(i.IINCHIP_SpiSendData) for IINCHIP_SpiSendData
    w5500_conf.o(i.wiz_write_buf) refers to w5500_conf.o(i.iinchip_cson) for iinchip_cson
    dhcp.o(i.DHCP_run) refers to dhcp.o(i.init_dhcp_client) for init_dhcp_client
    dhcp.o(i.DHCP_run) refers to w5500.o(i.getSn_SR) for getSn_SR
    dhcp.o(i.DHCP_run) refers to socket.o(i.socket) for socket
    dhcp.o(i.DHCP_run) refers to dhcp.o(i.parseDHCPMSG) for parseDHCPMSG
    dhcp.o(i.DHCP_run) refers to dhcp.o(i.send_DHCP_DISCOVER) for send_DHCP_DISCOVER
    dhcp.o(i.DHCP_run) refers to dhcp.o(i.send_DHCP_REQUEST) for send_DHCP_REQUEST
    dhcp.o(i.DHCP_run) refers to dhcp.o(i.check_DHCP_timeout) for check_DHCP_timeout
    dhcp.o(i.DHCP_run) refers to dhcp.o(i.check_DHCP_leasedIP) for check_DHCP_leasedIP
    dhcp.o(i.DHCP_run) refers to printfa.o(i.__0printf) for __2printf
    dhcp.o(i.DHCP_run) refers to dhcp.o(i.reset_DHCP_timeout) for reset_DHCP_timeout
    dhcp.o(i.DHCP_run) refers to api_w5500.o(.RAM_D3) for Lan_Para
    dhcp.o(i.DHCP_run) refers to dhcp.o(.data) for Conflict_flag
    dhcp.o(i.DHCP_run) refers to main.o(.data) for dhcp_ok
    dhcp.o(i.DHCP_timer_handler) refers to dhcp.o(.data) for dhcp_tick_cnt
    dhcp.o(i.check_DHCP_Timeout) refers to dhcp.o(i.send_DHCP_DISCOVER) for send_DHCP_DISCOVER
    dhcp.o(i.check_DHCP_Timeout) refers to dhcp.o(i.send_DHCP_REQUEST) for send_DHCP_REQUEST
    dhcp.o(i.check_DHCP_Timeout) refers to dhcp.o(i.reset_DHCP_timeout) for reset_DHCP_timeout
    dhcp.o(i.check_DHCP_Timeout) refers to dhcp.o(.data) for dhcp_retry_count
    dhcp.o(i.check_DHCP_leasedIP) refers to socket.o(i.sendto) for sendto
    dhcp.o(i.check_DHCP_leasedIP) refers to dhcp.o(i.send_DHCP_DECLINE) for send_DHCP_DECLINE
    dhcp.o(i.check_DHCP_leasedIP) refers to dhcp.o(.data) for DHCP_allocated_ip
    dhcp.o(i.check_DHCP_timeout) refers to dhcp.o(i.send_DHCP_DISCOVER) for send_DHCP_DISCOVER
    dhcp.o(i.check_DHCP_timeout) refers to dhcp.o(i.send_DHCP_REQUEST) for send_DHCP_REQUEST
    dhcp.o(i.check_DHCP_timeout) refers to dhcp.o(i.reset_DHCP_timeout) for reset_DHCP_timeout
    dhcp.o(i.check_DHCP_timeout) refers to dhcp.o(.data) for dhcp_retry_count
    dhcp.o(i.default_ip_assign) refers to w5500.o(i.setSIPR) for setSIPR
    dhcp.o(i.default_ip_assign) refers to w5500.o(i.setSUBR) for setSUBR
    dhcp.o(i.default_ip_assign) refers to w5500.o(i.setGAR) for setGAR
    dhcp.o(i.default_ip_assign) refers to dhcp.o(.data) for DHCP_allocated_ip
    dhcp.o(i.default_ip_conflict) refers to w5500.o(i.setMR) for setMR
    dhcp.o(i.default_ip_conflict) refers to w5500.o(i.setSHAR) for setSHAR
    dhcp.o(i.default_ip_conflict) refers to api_w5500.o(.RAM_D3) for Lan_Para
    dhcp.o(i.default_ip_update) refers to w5500.o(i.setMR) for setMR
    dhcp.o(i.default_ip_update) refers to dhcp.o(i.default_ip_assign) for default_ip_assign
    dhcp.o(i.default_ip_update) refers to w5500.o(i.setSHAR) for setSHAR
    dhcp.o(i.default_ip_update) refers to api_w5500.o(.RAM_D3) for Lan_Para
    dhcp.o(i.init_dhcp_client) refers to w5500.o(i.setSUBR) for setSUBR
    dhcp.o(i.init_dhcp_client) refers to w5500.o(i.setGAR) for setGAR
    dhcp.o(i.init_dhcp_client) refers to w5500.o(i.setSIPR) for setSIPR
    dhcp.o(i.init_dhcp_client) refers to printfa.o(i.__0printf) for __2printf
    dhcp.o(i.init_dhcp_client) refers to dhcp.o(.data) for dhcp_state
    dhcp.o(i.makeDHCPMSG) refers to w5500.o(i.getSHAR) for getSHAR
    dhcp.o(i.makeDHCPMSG) refers to dhcp.o(.data) for pDHCPMSG
    dhcp.o(i.makeDHCPMSG) refers to api_w5500.o(.RAM_D3) for Lan_Para
    dhcp.o(i.parseDHCPMSG) refers to w5500.o(i.getSn_RX_RSR) for getSn_RX_RSR
    dhcp.o(i.parseDHCPMSG) refers to socket.o(i.recvfrom) for recvfrom
    dhcp.o(i.parseDHCPMSG) refers to dhcp.o(.data) for pDHCPMSG
    dhcp.o(i.parseDHCPMSG) refers to api_w5500.o(.RAM_D3) for Lan_Para
    dhcp.o(i.reset_DHCP_timeout) refers to dhcp.o(.data) for dhcp_time
    dhcp.o(i.send_DHCP_DECLINE) refers to dhcp.o(i.makeDHCPMSG) for makeDHCPMSG
    dhcp.o(i.send_DHCP_DECLINE) refers to socket.o(i.sendto) for sendto
    dhcp.o(i.send_DHCP_DECLINE) refers to dhcp.o(.data) for pDHCPMSG
    dhcp.o(i.send_DHCP_DECLINE) refers to api_w5500.o(.RAM_D3) for Lan_Para
    dhcp.o(i.send_DHCP_DISCOVER) refers to dhcp.o(i.makeDHCPMSG) for makeDHCPMSG
    dhcp.o(i.send_DHCP_DISCOVER) refers to printfa.o(i.__0sprintf) for __2sprintf
    dhcp.o(i.send_DHCP_DISCOVER) refers to strlen.o(.text) for strlen
    dhcp.o(i.send_DHCP_DISCOVER) refers to strcpy.o(.text) for strcpy
    dhcp.o(i.send_DHCP_DISCOVER) refers to socket.o(i.sendto) for sendto
    dhcp.o(i.send_DHCP_DISCOVER) refers to dhcp.o(.data) for pDHCPMSG
    dhcp.o(i.send_DHCP_DISCOVER) refers to api_w5500.o(.RAM_D3) for Lan_Para
    dhcp.o(i.send_DHCP_REQUEST) refers to dhcp.o(i.makeDHCPMSG) for makeDHCPMSG
    dhcp.o(i.send_DHCP_REQUEST) refers to printfa.o(i.__0sprintf) for __2sprintf
    dhcp.o(i.send_DHCP_REQUEST) refers to strlen.o(.text) for strlen
    dhcp.o(i.send_DHCP_REQUEST) refers to strcpy.o(.text) for strcpy
    dhcp.o(i.send_DHCP_REQUEST) refers to socket.o(i.sendto) for sendto
    dhcp.o(i.send_DHCP_REQUEST) refers to dhcp.o(.data) for dhcp_state
    dhcp.o(i.send_DHCP_REQUEST) refers to api_w5500.o(.RAM_D3) for Lan_Para
    dhcp.o(.data) refers to dhcp.o(.bss) for EXTERN_DHCPBUF
    dhcp.o(.data) refers to dhcp.o(i.default_ip_assign) for default_ip_assign
    dhcp.o(.data) refers to dhcp.o(i.default_ip_update) for default_ip_update
    dhcp.o(.data) refers to dhcp.o(i.default_ip_conflict) for default_ip_conflict
    tcp_client.o(i.do_tcp_client) refers to w5500.o(i.getSn_SR) for getSn_SR
    tcp_client.o(i.do_tcp_client) refers to socket.o(i.socket) for socket
    tcp_client.o(i.do_tcp_client) refers to socket.o(i.connect) for connect
    tcp_client.o(i.do_tcp_client) refers to w5500.o(i.getSn_IR) for getSn_IR
    tcp_client.o(i.do_tcp_client) refers to w5500.o(i.setSn_IR) for setSn_IR
    tcp_client.o(i.do_tcp_client) refers to w5500.o(i.getSn_RX_RSR) for getSn_RX_RSR
    tcp_client.o(i.do_tcp_client) refers to socket.o(i.recv) for recv
    tcp_client.o(i.do_tcp_client) refers to printfa.o(i.__0printf) for __2printf
    tcp_client.o(i.do_tcp_client) refers to socket.o(i.disconnect) for disconnect
    tcp_client.o(i.do_tcp_client) refers to socket.o(i.close) for close
    tcp_client.o(i.do_tcp_client) refers to api_w5500.o(.RAM_D3) for Lan_Para
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    strtok.o(.text) refers to strtok.o(.data) for .data
    strtok_r.o(.text) refers to strtok_r.o(.data) for .data
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    malloc.o(i.free) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to startup_gd32f450_470.o(HEAP) for __heap_base
    mallocr.o(i.__free$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.__malloc$realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocr.o(i.__malloc$realloc) refers to startup_gd32f450_470.o(HEAP) for __heap_base
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocr.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.realloc) refers to mallocr.o(i.__free$realloc) for __free$realloc
    mallocr.o(i.realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.realloc) refers to mallocr.o(i.__malloc$realloc) for __malloc$realloc
    mallocr.o(i.realloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to startup_gd32f450_470.o(HEAP) for __heap_base
    malloca.o(i.__free$memalign) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__malloc$memalign) refers to malloca.o(i.__aligned_malloc) for __aligned_malloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocra.o(i.__aligned_malloc$realloc) refers to startup_gd32f450_470.o(HEAP) for __heap_base
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__free$realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__malloc$realloc$memalign) refers to mallocra.o(i.__aligned_malloc$realloc) for __aligned_malloc$realloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__free$realloc$memalign) for __free$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__malloc$realloc$memalign) for __malloc$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocra.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    atoi.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    atoi.o(.text) refers to strtol.o(.text) for strtol
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_gd32f450_470.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_gd32f450_470.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    strtol.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    strtol.o(.text) refers to _strtoul.o(.text) for _strtoul
    strtol.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    ctype_o.o(.text) refers to ctype_o.o(.constdata) for .constdata
    ctype_o.o(.constdata) refers to ctype_o.o(.constdata) for __ctype_table
    isalnum_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isalpha_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isblank_o.o(.text) refers to ctype_o.o(.constdata) for __ctype_table
    iscntrl_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isdigit_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isgraph_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    islower_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isprint_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    ispunct_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isspace_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isupper_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isxdigit_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    _strtoul.o(.text) refers to _chval.o(.text) for _chval
    _strtoul.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr


==============================================================================

Removing Unused input sections from the image.

    Removing gd32f4xx_adc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_adc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_adc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_adc.o(i.adc_calibration_enable), (42 bytes).
    Removing gd32f4xx_adc.o(i.adc_channel_16_to_18), (96 bytes).
    Removing gd32f4xx_adc.o(i.adc_channel_length_config), (82 bytes).
    Removing gd32f4xx_adc.o(i.adc_clock_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_data_alignment_config), (22 bytes).
    Removing gd32f4xx_adc.o(i.adc_deinit), (20 bytes).
    Removing gd32f4xx_adc.o(i.adc_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_discontinuous_mode_config), (82 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_mode_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_mode_enable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_request_after_last_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_request_after_last_enable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_enable), (18 bytes).
    Removing gd32f4xx_adc.o(i.adc_end_of_conversion_config), (34 bytes).
    Removing gd32f4xx_adc.o(i.adc_external_trigger_config), (52 bytes).
    Removing gd32f4xx_adc.o(i.adc_external_trigger_source_config), (48 bytes).
    Removing gd32f4xx_adc.o(i.adc_flag_clear), (8 bytes).
    Removing gd32f4xx_adc.o(i.adc_flag_get), (14 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_channel_config), (124 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_channel_offset_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_data_read), (46 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_software_startconv_flag_get), (16 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_disable), (66 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_enable), (66 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_flag_clear), (8 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_flag_get), (112 bytes).
    Removing gd32f4xx_adc.o(i.adc_oversample_mode_config), (58 bytes).
    Removing gd32f4xx_adc.o(i.adc_oversample_mode_disable), (14 bytes).
    Removing gd32f4xx_adc.o(i.adc_oversample_mode_enable), (14 bytes).
    Removing gd32f4xx_adc.o(i.adc_regular_channel_config), (172 bytes).
    Removing gd32f4xx_adc.o(i.adc_regular_data_read), (8 bytes).
    Removing gd32f4xx_adc.o(i.adc_regular_software_startconv_flag_get), (16 bytes).
    Removing gd32f4xx_adc.o(i.adc_resolution_config), (16 bytes).
    Removing gd32f4xx_adc.o(i.adc_software_trigger_enable), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_special_function_config), (90 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_delay_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_dma_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_dma_request_after_last_disable), (24 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_dma_request_after_last_enable), (24 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_mode_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_regular_data_read), (12 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_disable), (50 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_group_channel_enable), (64 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_single_channel_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_single_channel_enable), (24 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_threshold_config), (14 bytes).
    Removing gd32f4xx_can.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_can.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_can.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_can.o(i.can1_filter_start_bank), (56 bytes).
    Removing gd32f4xx_can.o(i.can_debug_freeze_disable), (44 bytes).
    Removing gd32f4xx_can.o(i.can_debug_freeze_enable), (44 bytes).
    Removing gd32f4xx_can.o(i.can_deinit), (52 bytes).
    Removing gd32f4xx_can.o(i.can_error_get), (12 bytes).
    Removing gd32f4xx_can.o(i.can_fifo_release), (32 bytes).
    Removing gd32f4xx_can.o(i.can_filter_init), (272 bytes).
    Removing gd32f4xx_can.o(i.can_flag_clear), (16 bytes).
    Removing gd32f4xx_can.o(i.can_flag_get), (30 bytes).
    Removing gd32f4xx_can.o(i.can_init), (286 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_disable), (8 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_enable), (8 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_flag_get), (116 bytes).
    Removing gd32f4xx_can.o(i.can_message_receive), (228 bytes).
    Removing gd32f4xx_can.o(i.can_message_transmit), (336 bytes).
    Removing gd32f4xx_can.o(i.can_receive_error_number_get), (8 bytes).
    Removing gd32f4xx_can.o(i.can_receive_message_length_get), (26 bytes).
    Removing gd32f4xx_can.o(i.can_struct_para_init), (164 bytes).
    Removing gd32f4xx_can.o(i.can_time_trigger_mode_disable), (48 bytes).
    Removing gd32f4xx_can.o(i.can_time_trigger_mode_enable), (48 bytes).
    Removing gd32f4xx_can.o(i.can_transmission_stop), (80 bytes).
    Removing gd32f4xx_can.o(i.can_transmit_error_number_get), (10 bytes).
    Removing gd32f4xx_can.o(i.can_transmit_states), (124 bytes).
    Removing gd32f4xx_can.o(i.can_wakeup), (48 bytes).
    Removing gd32f4xx_can.o(i.can_working_mode_set), (168 bytes).
    Removing gd32f4xx_crc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_crc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_crc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_crc.o(i.crc_block_data_calculate), (36 bytes).
    Removing gd32f4xx_crc.o(i.crc_data_register_read), (12 bytes).
    Removing gd32f4xx_crc.o(i.crc_data_register_reset), (20 bytes).
    Removing gd32f4xx_crc.o(i.crc_deinit), (24 bytes).
    Removing gd32f4xx_crc.o(i.crc_free_data_register_read), (12 bytes).
    Removing gd32f4xx_crc.o(i.crc_free_data_register_write), (12 bytes).
    Removing gd32f4xx_crc.o(i.crc_single_data_calculate), (16 bytes).
    Removing gd32f4xx_ctc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_ctc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_ctc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_clock_limit_value_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_capture_value_read), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_direction_read), (24 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_disable), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_enable), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_reload_value_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_reload_value_read), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_deinit), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_flag_clear), (36 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_flag_get), (24 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_hardware_trim_mode_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_disable), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_enable), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_flag_clear), (36 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_flag_get), (56 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_irc48m_trim_value_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_irc48m_trim_value_read), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_refsource_polarity_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_refsource_prescaler_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_refsource_signal_select), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_software_refsource_pulse_generate), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_usbsof_signal_select), (28 bytes).
    Removing gd32f4xx_dac.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dac.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dac.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_data_set), (64 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_disable), (24 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_enable), (24 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_interrupt_disable), (24 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_interrupt_enable), (24 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_output_buffer_disable), (24 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_output_buffer_enable), (24 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_software_trigger_disable), (20 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_software_trigger_enable), (20 bytes).
    Removing gd32f4xx_dac.o(i.dac_data_set), (88 bytes).
    Removing gd32f4xx_dac.o(i.dac_deinit), (20 bytes).
    Removing gd32f4xx_dac.o(i.dac_disable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_dma_disable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_dma_enable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_enable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_flag_clear), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_flag_get), (40 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_disable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_enable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_flag_clear), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_flag_get), (68 bytes).
    Removing gd32f4xx_dac.o(i.dac_lfsr_noise_config), (52 bytes).
    Removing gd32f4xx_dac.o(i.dac_output_buffer_disable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_output_buffer_enable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_output_value_get), (28 bytes).
    Removing gd32f4xx_dac.o(i.dac_software_trigger_disable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_software_trigger_enable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_triangle_noise_config), (52 bytes).
    Removing gd32f4xx_dac.o(i.dac_trigger_disable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_trigger_enable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_trigger_source_config), (52 bytes).
    Removing gd32f4xx_dac.o(i.dac_wave_bit_width_config), (52 bytes).
    Removing gd32f4xx_dac.o(i.dac_wave_mode_config), (52 bytes).
    Removing gd32f4xx_dbg.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dbg.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dbg.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_deinit), (16 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_id_get), (12 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_low_power_disable), (16 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_low_power_enable), (16 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_periph_disable), (32 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_periph_enable), (32 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_trace_pin_disable), (20 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_trace_pin_enable), (20 bytes).
    Removing gd32f4xx_dci.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dci.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dci.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_dci.o(i.dci_capture_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_capture_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_crop_window_config), (24 bytes).
    Removing gd32f4xx_dci.o(i.dci_crop_window_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_crop_window_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_data_read), (12 bytes).
    Removing gd32f4xx_dci.o(i.dci_deinit), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_embedded_sync_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_embedded_sync_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_flag_get), (36 bytes).
    Removing gd32f4xx_dci.o(i.dci_init), (52 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_disable), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_enable), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_dci.o(i.dci_jpeg_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_jpeg_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_sync_codes_config), (24 bytes).
    Removing gd32f4xx_dci.o(i.dci_sync_codes_unmask_config), (24 bytes).
    Removing gd32f4xx_dma.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dma.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dma.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_dma.o(i.dma_channel_disable), (32 bytes).
    Removing gd32f4xx_dma.o(i.dma_channel_enable), (32 bytes).
    Removing gd32f4xx_dma.o(i.dma_channel_subperipheral_select), (38 bytes).
    Removing gd32f4xx_dma.o(i.dma_circulation_disable), (32 bytes).
    Removing gd32f4xx_dma.o(i.dma_circulation_enable), (32 bytes).
    Removing gd32f4xx_dma.o(i.dma_deinit), (164 bytes).
    Removing gd32f4xx_dma.o(i.dma_fifo_status_get), (20 bytes).
    Removing gd32f4xx_dma.o(i.dma_flag_clear), (62 bytes).
    Removing gd32f4xx_dma.o(i.dma_flag_get), (76 bytes).
    Removing gd32f4xx_dma.o(i.dma_flow_controller_config), (64 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_disable), (62 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_enable), (62 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_flag_clear), (62 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_flag_get), (516 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_address_config), (32 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_address_generation_config), (64 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_burst_beats_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_width_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_multi_data_mode_init), (356 bytes).
    Removing gd32f4xx_dma.o(i.dma_multi_data_para_struct_init), (40 bytes).
    Removing gd32f4xx_dma.o(i.dma_periph_address_config), (16 bytes).
    Removing gd32f4xx_dma.o(i.dma_periph_burst_beats_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_periph_width_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_peripheral_address_generation_config), (126 bytes).
    Removing gd32f4xx_dma.o(i.dma_priority_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_single_data_mode_init), (344 bytes).
    Removing gd32f4xx_dma.o(i.dma_single_data_para_struct_init), (34 bytes).
    Removing gd32f4xx_dma.o(i.dma_switch_buffer_mode_config), (76 bytes).
    Removing gd32f4xx_dma.o(i.dma_switch_buffer_mode_enable), (66 bytes).
    Removing gd32f4xx_dma.o(i.dma_transfer_direction_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_transfer_number_config), (16 bytes).
    Removing gd32f4xx_dma.o(i.dma_transfer_number_get), (16 bytes).
    Removing gd32f4xx_dma.o(i.dma_using_memory_get), (28 bytes).
    Removing gd32f4xx_enet.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_enet.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_enet.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_enet.o(i.enet_address_filter_config), (32 bytes).
    Removing gd32f4xx_enet.o(i.enet_address_filter_disable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_address_filter_enable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_current_desc_address_get), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_debug_status_get), (108 bytes).
    Removing gd32f4xx_enet.o(i.enet_default_init), (104 bytes).
    Removing gd32f4xx_enet.o(i.enet_deinit), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_delay), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_flag_clear), (8 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_flag_get), (14 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_flag_set), (8 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_information_get), (100 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_select_normal_mode), (20 bytes).
    Removing gd32f4xx_enet.o(i.enet_descriptors_chain_init), (200 bytes).
    Removing gd32f4xx_enet.o(i.enet_descriptors_ring_init), (236 bytes).
    Removing gd32f4xx_enet.o(i.enet_disable), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_dma_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_dma_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_dmaprocess_resume), (28 bytes).
    Removing gd32f4xx_enet.o(i.enet_dmaprocess_state_get), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_enable), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_flag_clear), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_flag_get), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_fliter_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_fliter_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_flowcontrol_feature_disable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_flowcontrol_feature_enable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_flowcontrol_threshold_config), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_forward_feature_disable), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_forward_feature_enable), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_frame_receive), (248 bytes).
    Removing gd32f4xx_enet.o(i.enet_frame_transmit), (204 bytes).
    Removing gd32f4xx_enet.o(i.enet_init), (868 bytes).
    Removing gd32f4xx_enet.o(i.enet_initpara_config), (356 bytes).
    Removing gd32f4xx_enet.o(i.enet_initpara_reset), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_disable), (72 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_enable), (72 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_flag_clear), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_flag_get), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_mac_address_get), (60 bytes).
    Removing gd32f4xx_enet.o(i.enet_mac_address_set), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_missed_frame_counter_get), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_counters_get), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_counters_preset_config), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_counters_reset), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_feature_disable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_feature_enable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_pauseframe_config), (44 bytes).
    Removing gd32f4xx_enet.o(i.enet_pauseframe_detect_config), (28 bytes).
    Removing gd32f4xx_enet.o(i.enet_pauseframe_generate), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_phy_config), (216 bytes).
    Removing gd32f4xx_enet.o(i.enet_phy_write_read), (156 bytes).
    Removing gd32f4xx_enet.o(i.enet_phyloopback_disable), (50 bytes).
    Removing gd32f4xx_enet.o(i.enet_phyloopback_enable), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_expected_time_config), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init), (236 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init), (280 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_pps_output_frequency_config), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_subsecond_increment_config), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_system_time_get), (32 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_timestamp_addend_config), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_timestamp_function_config), (256 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_timestamp_update_config), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptpframe_receive_normal_mode), (340 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptpframe_transmit_normal_mode), (444 bytes).
    Removing gd32f4xx_enet.o(i.enet_registers_get), (56 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_desc_delay_receive_complete_interrupt), (20 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_desc_immediate_receive_complete_interrupt), (10 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_disable), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_enable), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_rxframe_drop), (172 bytes).
    Removing gd32f4xx_enet.o(i.enet_rxframe_size_get), (152 bytes).
    Removing gd32f4xx_enet.o(i.enet_rxprocess_check_recovery), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_software_reset), (60 bytes).
    Removing gd32f4xx_enet.o(i.enet_transmit_checksum_config), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_tx_disable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_tx_enable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_txfifo_flush), (52 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_filter_config), (28 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_filter_register_pointer_reset), (20 bytes).
    Removing gd32f4xx_enet.o(.bss), (15460 bytes).
    Removing gd32f4xx_enet.o(.constdata), (116 bytes).
    Removing gd32f4xx_enet.o(.data), (20 bytes).
    Removing gd32f4xx_exmc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_exmc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_exmc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_ecc_get), (12 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_flag_clear), (52 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_flag_get), (52 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_disable), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_enable), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_flag_clear), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_flag_get), (72 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_deinit), (42 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_disable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_ecc_config), (48 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_enable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_init), (172 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_struct_para_init), (54 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_consecutive_clock_config), (42 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_deinit), (36 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_disable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_enable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_init), (228 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_page_size_config), (40 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_struct_para_init), (106 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_deinit), (36 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_disable), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_enable), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_init), (188 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_struct_para_init), (60 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_autorefresh_number_set), (32 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_bankstatus_get), (32 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_command_config), (28 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_deinit), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_init), (280 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_readsample_config), (32 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_readsample_enable), (44 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_refresh_count_set), (36 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_struct_para_init), (66 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_write_protection_config), (64 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_deinit), (44 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_high_id_get), (12 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_init), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_low_id_get), (12 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_read_command_set), (28 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_read_id_command_send), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_send_command_state_get), (48 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_struct_para_init), (20 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_write_cmd_send), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_write_command_set), (28 bytes).
    Removing gd32f4xx_exti.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_exti.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_exti.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_exti.o(i.exti_deinit), (28 bytes).
    Removing gd32f4xx_exti.o(i.exti_event_disable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_event_enable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_flag_clear), (12 bytes).
    Removing gd32f4xx_exti.o(i.exti_flag_get), (24 bytes).
    Removing gd32f4xx_exti.o(i.exti_init), (188 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_disable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_enable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_flag_clear), (12 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_flag_get), (36 bytes).
    Removing gd32f4xx_exti.o(i.exti_software_interrupt_disable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_software_interrupt_enable), (16 bytes).
    Removing gd32f4xx_fmc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_fmc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_fmc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_bank0_erase), (68 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_bank1_erase), (68 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_byte_program), (80 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_flag_clear), (12 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_flag_get), (24 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_halfword_program), (84 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_disable), (16 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_enable), (16 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_flag_clear), (12 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_flag_get), (64 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_mass_erase), (72 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_page_erase), (124 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_wscnt_set), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_boot_mode_config), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp0_get), (32 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp1_get), (32 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp_disable), (96 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp_enable), (104 bytes).
    Removing gd32f4xx_fmc.o(i.ob_erase), (76 bytes).
    Removing gd32f4xx_fmc.o(i.ob_lock), (20 bytes).
    Removing gd32f4xx_fmc.o(i.ob_security_protection_config), (40 bytes).
    Removing gd32f4xx_fmc.o(i.ob_spc_get), (28 bytes).
    Removing gd32f4xx_fmc.o(i.ob_start), (20 bytes).
    Removing gd32f4xx_fmc.o(i.ob_unlock), (36 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_bor_threshold), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_bor_threshold_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_write), (52 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection0_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection1_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection_disable), (72 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection_enable), (72 bytes).
    Removing gd32f4xx_fwdgt.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_fwdgt.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_fwdgt.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_config), (104 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_counter_reload), (16 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_enable), (16 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_flag_get), (24 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_write_disable), (12 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_write_enable), (16 bytes).
    Removing gd32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_gpio.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_bit_toggle), (4 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_bit_write), (10 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_deinit), (206 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_input_bit_get), (16 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_input_port_get), (8 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_output_bit_get), (16 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_output_port_get), (8 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_pin_lock), (18 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_port_toggle), (8 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_port_write), (4 bytes).
    Removing gd32f4xx_i2c.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_i2c.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_i2c.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_ack_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_ackpos_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_analog_noise_filter_disable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_analog_noise_filter_enable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_clock_config), (228 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_data_receive), (8 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_data_transmit), (6 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_deinit), (88 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_digital_noise_filter_config), (8 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_disable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dma_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dma_last_transfer_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dualaddr_disable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dualaddr_enable), (12 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_enable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_flag_clear), (40 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_flag_get), (30 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_disable), (26 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_enable), (26 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_flag_clear), (44 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_flag_get), (92 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_master_addressing), (20 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_mode_addr_config), (28 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_pec_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_pec_transfer_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_pec_value_get), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_disable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_enable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_timeout_disable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_timeout_enable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_slave_response_to_gcall_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_smbus_alert_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_smbus_arp_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_smbus_type_config), (24 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_software_reset_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_start_on_bus), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_stop_on_bus), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_stretch_scl_low_config), (16 bytes).
    Removing gd32f4xx_ipa.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_ipa.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_ipa.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_init), (164 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_lut_init), (100 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_lut_loading_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_struct_para_init), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_deinit), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_destination_init), (316 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_destination_struct_para_init), (22 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_flag_clear), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_flag_get), (24 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_init), (164 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_lut_init), (100 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_lut_loading_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_struct_para_init), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_inter_timer_config), (36 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_disable), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_enable), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interval_clock_num_config), (28 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_line_mark_config), (24 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_pixel_format_convert_mode_set), (28 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_hangup_disable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_hangup_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_stop_disable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_stop_enable), (20 bytes).
    Removing gd32f4xx_iref.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_iref.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_iref.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_iref.o(i.iref_deinit), (20 bytes).
    Removing gd32f4xx_iref.o(i.iref_disable), (20 bytes).
    Removing gd32f4xx_iref.o(i.iref_enable), (20 bytes).
    Removing gd32f4xx_iref.o(i.iref_mode_set), (28 bytes).
    Removing gd32f4xx_iref.o(i.iref_precision_trim_value_set), (28 bytes).
    Removing gd32f4xx_iref.o(i.iref_sink_set), (28 bytes).
    Removing gd32f4xx_iref.o(i.iref_step_data_config), (28 bytes).
    Removing gd32f4xx_misc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_misc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_misc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_misc.o(i.nvic_irq_disable), (24 bytes).
    Removing gd32f4xx_misc.o(i.system_lowpower_reset), (16 bytes).
    Removing gd32f4xx_misc.o(i.system_lowpower_set), (16 bytes).
    Removing gd32f4xx_misc.o(i.systick_clksource_set), (40 bytes).
    Removing gd32f4xx_pmu.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_pmu.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_pmu.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_backup_ldo_config), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_backup_write_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_deinit), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_flag_clear), (48 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_flag_get), (24 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_highdriver_mode_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_highdriver_mode_enable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_highdriver_switch_select), (44 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_ldo_output_select), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lowdriver_mode_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lowdriver_mode_enable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lowpower_driver_config), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lvd_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lvd_select), (48 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_normalpower_driver_config), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_to_deepsleepmode), (244 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_to_sleepmode), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_to_standbymode), (56 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_wakeup_pin_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_wakeup_pin_enable), (20 bytes).
    Removing gd32f4xx_pmu.o(.bss), (16 bytes).
    Removing gd32f4xx_rcu.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_rcu.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_rcu.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ahb_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_all_reset_flag_clear), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_apb1_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_apb2_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_bkp_reset_disable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_bkp_reset_enable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ck48m_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ckout0_config), (28 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ckout1_config), (28 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_deepsleep_voltage_set), (16 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_deinit), (140 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_hxtal_clock_monitor_disable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_hxtal_clock_monitor_enable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_i2s_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_disable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_enable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_flag_clear), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_flag_get), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_irc16m_adjust_value_set), (28 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_lxtal_drive_capability_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_bypass_mode_disable), (116 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_bypass_mode_enable), (116 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_off), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_periph_clock_disable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_periph_clock_sleep_disable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_periph_clock_sleep_enable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_pll48m_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_pll_config), (132 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_plli2s_config), (44 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_pllsai_config), (72 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_rtc_div_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_spread_spectrum_config), (32 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_spread_spectrum_disable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_spread_spectrum_enable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_system_clock_source_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_system_clock_source_get), (16 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_timer_clock_prescaler_config), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_tli_clock_div_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_voltage_key_unlock), (16 bytes).
    Removing gd32f4xx_rtc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_rtc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_rtc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_config), (100 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_disable), (128 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_enable), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_get), (68 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_output_config), (84 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_subsecond_config), (52 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_subsecond_get), (32 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_bypass_shadow_disable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_bypass_shadow_enable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_calibration_output_config), (48 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_coarse_calibration_config), (116 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_current_time_get), (100 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_deinit), (204 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_flag_clear), (16 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_flag_get), (20 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_hour_adjust), (36 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_interrupt_disable), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_interrupt_enable), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_refclock_detection_disable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_refclock_detection_enable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_second_adjust), (108 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_smooth_calibration_config), (80 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_subsecond_get), (20 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_tamper0_pin_map), (28 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_tamper_disable), (16 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_tamper_enable), (200 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_disable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_enable), (48 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_get), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_pin_map), (28 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_subsecond_get), (12 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_clock_set), (92 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_disable), (84 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_enable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_timer_get), (12 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_timer_set), (76 bytes).
    Removing gd32f4xx_sdio.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_sdio.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_sdio.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_bus_mode_set), (28 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_completion_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_completion_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_interrupt_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_interrupt_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_clock_config), (52 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_clock_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_clock_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_command_index_get), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_command_response_config), (56 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_csm_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_csm_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_data_config), (60 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_data_counter_get), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_data_read), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_data_transfer_config), (28 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_data_write), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_deinit), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_dma_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_dma_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_dsm_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_dsm_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_fifo_counter_get), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_flag_clear), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_flag_get), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_hardware_clock_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_hardware_clock_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_interrupt_disable), (16 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_interrupt_enable), (16 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_interrupt_flag_clear), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_interrupt_flag_get), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_operation_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_operation_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_power_state_get), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_power_state_set), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_readwait_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_readwait_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_readwait_type_set), (40 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_response_get), (60 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_stop_readwait_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_stop_readwait_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_suspend_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_suspend_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_wait_type_set), (28 bytes).
    Removing gd32f4xx_spi.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_spi.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_spi.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_spi.o(i.i2s_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.i2s_enable), (10 bytes).
    Removing gd32f4xx_spi.o(i.i2s_full_duplex_mode_config), (48 bytes).
    Removing gd32f4xx_spi.o(i.i2s_init), (28 bytes).
    Removing gd32f4xx_spi.o(i.i2s_psc_config), (292 bytes).
    Removing gd32f4xx_spi.o(i.qspi_disable), (14 bytes).
    Removing gd32f4xx_spi.o(i.qspi_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.qspi_io23_output_disable), (14 bytes).
    Removing gd32f4xx_spi.o(i.qspi_io23_output_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.qspi_read_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.qspi_write_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_bidirectional_transfer_config), (26 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_error_clear), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_get), (16 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_next), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_off), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_on), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_polynomial_get), (8 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_polynomial_set), (4 bytes).
    Removing gd32f4xx_spi.o(i.spi_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_dma_disable), (22 bytes).
    Removing gd32f4xx_spi.o(i.spi_dma_enable), (22 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_data_frame_format_config), (16 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_deinit), (172 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_interrupt_disable), (48 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_interrupt_enable), (48 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_interrupt_flag_get), (112 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_internal_high), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_internal_low), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_output_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_output_enable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_struct_para_init), (18 bytes).
    Removing gd32f4xx_spi.o(i.spi_ti_mode_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_ti_mode_enable), (10 bytes).
    Removing gd32f4xx_syscfg.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_syscfg.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_syscfg.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_bootmode_config), (28 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_compensation_config), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_deinit), (20 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_enet_phy_interface_config), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_exmc_swap_config), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_exti_line_config), (172 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_flag_get), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_fmc_swap_config), (24 bytes).
    Removing gd32f4xx_timer.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_timer.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_timer.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_timer.o(i.timer_auto_reload_shadow_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_auto_reload_shadow_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_automatic_output_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_automatic_output_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_autoreload_value_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_config), (30 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_struct_para_init), (18 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_capture_value_register_read), (42 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_complementary_output_polarity_config), (70 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_complementary_output_state_config), (70 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_control_shadow_config), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_control_shadow_update_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_dma_request_source_select), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_input_struct_para_init), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_clear_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_fast_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_polarity_config), (92 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_state_config), (92 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_struct_para_init), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_remap_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_alignment), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_down_direction), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_read), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_up_direction), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_value_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_dma_disable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_dma_enable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_dma_transfer_config), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_event_software_generate), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_clock_mode0_config), (40 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_clock_mode1_config), (32 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_clock_mode1_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_trigger_as_external_clock_config), (166 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_trigger_config), (30 bytes).
    Removing gd32f4xx_timer.o(i.timer_flag_clear), (6 bytes).
    Removing gd32f4xx_timer.o(i.timer_flag_get), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_hall_mode_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_input_capture_config), (326 bytes).
    Removing gd32f4xx_timer.o(i.timer_input_pwm_capture_config), (356 bytes).
    Removing gd32f4xx_timer.o(i.timer_input_trigger_source_select), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_internal_clock_config), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_internal_trigger_as_external_clock_config), (32 bytes).
    Removing gd32f4xx_timer.o(i.timer_interrupt_disable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_master_output_trigger_source_select), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_master_slave_mode_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_output_value_selection_config), (34 bytes).
    Removing gd32f4xx_timer.o(i.timer_prescaler_config), (14 bytes).
    Removing gd32f4xx_timer.o(i.timer_prescaler_read), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_primary_output_config), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_quadrature_decoder_mode_config), (64 bytes).
    Removing gd32f4xx_timer.o(i.timer_repetition_value_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_single_pulse_mode_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_slave_mode_select), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_struct_para_init), (22 bytes).
    Removing gd32f4xx_timer.o(i.timer_update_event_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_update_event_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_update_source_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_write_chxval_register_config), (34 bytes).
    Removing gd32f4xx_tli.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_tli.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_tli.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_tli.o(i.tli_color_key_disable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_color_key_enable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_color_key_init), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_current_pos_get), (12 bytes).
    Removing gd32f4xx_tli.o(i.tli_deinit), (20 bytes).
    Removing gd32f4xx_tli.o(i.tli_disable), (20 bytes).
    Removing gd32f4xx_tli.o(i.tli_dither_config), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_enable), (20 bytes).
    Removing gd32f4xx_tli.o(i.tli_flag_get), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_init), (188 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_disable), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_enable), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_flag_get), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_disable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_enable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_init), (152 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_struct_para_init), (48 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_window_offset_modify), (228 bytes).
    Removing gd32f4xx_tli.o(i.tli_line_mark_set), (24 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_disable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_enable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_init), (28 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_struct_para_init), (12 bytes).
    Removing gd32f4xx_tli.o(i.tli_reload_config), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_struct_para_init), (34 bytes).
    Removing gd32f4xx_trng.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_trng.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_trng.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_trng.o(i.trng_disable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_disable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_enable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_usart.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_usart.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_usart.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_usart.o(i.usart_address_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_block_length_config), (28 bytes).
    Removing gd32f4xx_usart.o(i.usart_break_frame_coherence_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_data_first_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_dma_receive_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_dma_transmit_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_flag_clear), (26 bytes).
    Removing gd32f4xx_usart.o(i.usart_guard_time_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_halfduplex_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_halfduplex_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_hardware_flow_coherence_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_interrupt_disable), (26 bytes).
    Removing gd32f4xx_usart.o(i.usart_interrupt_enable), (26 bytes).
    Removing gd32f4xx_usart.o(i.usart_invert_config), (104 bytes).
    Removing gd32f4xx_usart.o(i.usart_irda_lowpower_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_irda_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_irda_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_lin_break_detection_length_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_lin_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_lin_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_mute_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_mute_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_mute_mode_wakeup_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_oversample_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_parity_check_coherence_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_prescaler_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_receiver_timeout_disable), (14 bytes).
    Removing gd32f4xx_usart.o(i.usart_receiver_timeout_enable), (14 bytes).
    Removing gd32f4xx_usart.o(i.usart_receiver_timeout_threshold_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_sample_bit_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_send_break), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_autoretry_config), (26 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_nack_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_nack_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_synchronous_clock_config), (34 bytes).
    Removing gd32f4xx_usart.o(i.usart_synchronous_clock_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_synchronous_clock_enable), (10 bytes).
    Removing gd32f4xx_wwdgt.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_wwdgt.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_wwdgt.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_config), (56 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_counter_update), (28 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_deinit), (20 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_enable), (20 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_flag_clear), (20 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_flag_get), (24 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_interrupt_enable), (20 bytes).
    Removing system_gd32f4xx.o(.rev16_text), (4 bytes).
    Removing system_gd32f4xx.o(.revsh_text), (4 bytes).
    Removing system_gd32f4xx.o(.rrx_text), (6 bytes).
    Removing system_gd32f4xx.o(i.SystemCoreClockUpdate), (272 bytes).
    Removing gd32f4xx_it.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_it.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_it.o(.rrx_text), (6 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing systick.o(.rev16_text), (4 bytes).
    Removing systick.o(.revsh_text), (4 bytes).
    Removing systick.o(.rrx_text), (6 bytes).
    Removing 25lc080a.o(.rev16_text), (4 bytes).
    Removing 25lc080a.o(.revsh_text), (4 bytes).
    Removing 25lc080a.o(.rrx_text), (6 bytes).
    Removing 25lc080a.o(i.EEPROM_WritePage), (730 bytes).
    Removing 25lc080a.o(i.USER_EEPROM_RedePage), (680 bytes).
    Removing 25lc080a.o(i.USER_EEPROM_WritePage), (44 bytes).
    Removing eeprom_spi.o(.rev16_text), (4 bytes).
    Removing eeprom_spi.o(.revsh_text), (4 bytes).
    Removing eeprom_spi.o(.rrx_text), (6 bytes).
    Removing eeprom_spi.o(i.EEPROM_ReadStatusRegister), (40 bytes).
    Removing eeprom_spi.o(i.EEPROM_SendByte), (68 bytes).
    Removing eeprom_spi.o(i.EEPROM_WriteStatusRegister), (60 bytes).
    Removing flash.o(.rev16_text), (4 bytes).
    Removing flash.o(.revsh_text), (4 bytes).
    Removing flash.o(.rrx_text), (6 bytes).
    Removing flash.o(i.bsp_ReadCpuFlash), (44 bytes).
    Removing flash.o(i.bsp_ReadWordFlash), (6 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing my_crc.o(.rev16_text), (4 bytes).
    Removing my_crc.o(.revsh_text), (4 bytes).
    Removing my_crc.o(.rrx_text), (6 bytes).
    Removing rtc.o(.rev16_text), (4 bytes).
    Removing rtc.o(.revsh_text), (4 bytes).
    Removing rtc.o(.rrx_text), (6 bytes).
    Removing rtc.o(i.rtc_register_set), (48 bytes).
    Removing rtc.o(.data), (2 bytes).
    Removing spi.o(.rev16_text), (4 bytes).
    Removing spi.o(.revsh_text), (4 bytes).
    Removing spi.o(.rrx_text), (6 bytes).
    Removing time.o(.rev16_text), (4 bytes).
    Removing time.o(.revsh_text), (4 bytes).
    Removing time.o(.rrx_text), (6 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.ESP32_IO_Init), (36 bytes).
    Removing usart.o(i.FML_USART_Register), (80 bytes).
    Removing usart.o(i.InitBuffer), (46 bytes).
    Removing usart.o(i.UARTx_SendBuffer), (64 bytes).
    Removing usart.o(i.USART2_Init), (176 bytes).
    Removing user_step.o(.rev16_text), (4 bytes).
    Removing user_step.o(.revsh_text), (4 bytes).
    Removing user_step.o(.rrx_text), (6 bytes).
    Removing user_step.o(i.Float2char), (28 bytes).
    Removing gd32f470v_start.o(.rev16_text), (4 bytes).
    Removing gd32f470v_start.o(.revsh_text), (4 bytes).
    Removing gd32f470v_start.o(.rrx_text), (6 bytes).
    Removing gd32f470v_start.o(i.gd_eval_BEEP_init), (68 bytes).
    Removing gd32f470v_start.o(i.gd_eval_BEEP_off), (24 bytes).
    Removing gd32f470v_start.o(i.gd_eval_BEEP_on), (24 bytes).
    Removing gd32f470v_start.o(i.gd_eval_key_init), (128 bytes).
    Removing gd32f470v_start.o(i.gd_eval_key_state_get), (32 bytes).
    Removing gd32f470v_start.o(i.gd_eval_led_init), (84 bytes).
    Removing gd32f470v_start.o(i.gd_eval_led_off), (24 bytes).
    Removing gd32f470v_start.o(i.gd_eval_led_on), (24 bytes).
    Removing gd32f470v_start.o(i.gd_eval_led_toggle), (24 bytes).
    Removing gd32f470v_start.o(.data), (62 bytes).
    Removing api_lan_data_process .o(.rev16_text), (4 bytes).
    Removing api_lan_data_process .o(.revsh_text), (4 bytes).
    Removing api_lan_data_process .o(.rrx_text), (6 bytes).
    Removing api_lan_data_process .o(i.API_AnalysisIpPortAddr), (122 bytes).
    Removing api_lan_data_process .o(i.API_Erase_Flash), (116 bytes).
    Removing api_lan_data_process .o(i.API_Itoi), (108 bytes).
    Removing api_lan_data_process .o(i.API_LAN_Info_To_String), (512 bytes).
    Removing api_lan_data_process .o(i.API_LanSock1Send_CRC), (96 bytes).
    Removing api_lan_data_process .o(i.HEXArrayToStringArray), (80 bytes).
    Removing api_tnrg.o(.rev16_text), (4 bytes).
    Removing api_tnrg.o(.revsh_text), (4 bytes).
    Removing api_tnrg.o(.rrx_text), (6 bytes).
    Removing api_w5500.o(.rev16_text), (4 bytes).
    Removing api_w5500.o(.revsh_text), (4 bytes).
    Removing api_w5500.o(.rrx_text), (6 bytes).
    Removing socket.o(.rev16_text), (4 bytes).
    Removing socket.o(.revsh_text), (4 bytes).
    Removing socket.o(.rrx_text), (6 bytes).
    Removing socket.o(i.listen), (62 bytes).
    Removing utility.o(i.atoi16), (32 bytes).
    Removing utility.o(i.atoi32), (32 bytes).
    Removing utility.o(i.c2d), (54 bytes).
    Removing utility.o(i.checksum), (76 bytes).
    Removing utility.o(i.htonl), (12 bytes).
    Removing utility.o(i.htons), (12 bytes).
    Removing utility.o(i.inet_addr_), (96 bytes).
    Removing utility.o(i.inet_ntoa), (56 bytes).
    Removing utility.o(i.inet_ntoa_pad), (64 bytes).
    Removing utility.o(i.itoa), (64 bytes).
    Removing utility.o(i.mid), (68 bytes).
    Removing utility.o(i.ntohl), (12 bytes).
    Removing utility.o(i.ntohs), (12 bytes).
    Removing utility.o(i.replacetochar), (26 bytes).
    Removing utility.o(i.swapl), (28 bytes).
    Removing utility.o(i.swaps), (14 bytes).
    Removing utility.o(i.validatoi), (72 bytes).
    Removing utility.o(i.verify_ip_address), (120 bytes).
    Removing utility.o(.bss), (48 bytes).
    Removing w5500.o(.rev16_text), (4 bytes).
    Removing w5500.o(.revsh_text), (4 bytes).
    Removing w5500.o(.rrx_text), (6 bytes).
    Removing w5500.o(i.clearIR), (26 bytes).
    Removing w5500.o(i.getGAR), (16 bytes).
    Removing w5500.o(i.getIINCHIP_RxMAX), (16 bytes).
    Removing w5500.o(i.getIR), (12 bytes).
    Removing w5500.o(i.getSIPR), (18 bytes).
    Removing w5500.o(i.getSUBR), (18 bytes).
    Removing w5500.o(i.iinchip_init), (10 bytes).
    Removing w5500.o(i.setRCR), (16 bytes).
    Removing w5500.o(i.setRTR), (26 bytes).
    Removing w5500.o(i.setSn_MSS), (36 bytes).
    Removing w5500.o(i.setSn_TTL), (22 bytes).
    Removing w5500_conf.o(.rev16_text), (4 bytes).
    Removing w5500_conf.o(.revsh_text), (4 bytes).
    Removing w5500_conf.o(.rrx_text), (6 bytes).
    Removing w5500_conf.o(i.dhcp_timer_init), (8 bytes).
    Removing w5500_conf.o(i.ntp_timer_init), (8 bytes).
    Removing w5500_conf.o(i.reboot), (2 bytes).
    Removing w5500_conf.o(i.reset_break_gpio_init), (2 bytes).
    Removing w5500_conf.o(i.setKPALVTR), (22 bytes).
    Removing w5500_conf.o(i.set_w5500_netinfo), (272 bytes).
    Removing w5500_conf.o(i.timer2_init), (2 bytes).
    Removing w5500_conf.o(i.timer2_isr), (60 bytes).
    Removing w5500_conf.o(.bss), (1024 bytes).
    Removing w5500_conf.o(.data), (9 bytes).
    Removing dhcp.o(.rev16_text), (4 bytes).
    Removing dhcp.o(.revsh_text), (4 bytes).
    Removing dhcp.o(.rrx_text), (6 bytes).
    Removing dhcp.o(i.DHCP_timer_handler), (44 bytes).
    Removing dhcp.o(i.check_DHCP_Timeout), (132 bytes).
    Removing tcp_client.o(.rev16_text), (4 bytes).
    Removing tcp_client.o(.revsh_text), (4 bytes).
    Removing tcp_client.o(.rrx_text), (6 bytes).
    Removing tcp_client.o(i.do_tcp_client), (164 bytes).

1022 unused section(s) (total 58211 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/../cmprslib/zerorunl2.c          0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isalnum_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isblank_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isdigit_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isalpha_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isprint_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  islower_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isgraph_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  iscntrl_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ispunct_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isspace_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isupper_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isxdigit_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ctype_o.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  malloc.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  mallocra.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  malloca.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  mallocr.o ABSOLUTE
    ../clib/microlib/malloc/mvars.c          0x00000000   Number         0  mvars.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strchr.c         0x00000000   Number         0  strchr.o ABSOLUTE
    ../clib/microlib/string/strcpy.c         0x00000000   Number         0  strcpy.o ABSOLUTE
    ../clib/microlib/string/strlen.c         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/microlib/string/strncpy.c        0x00000000   Number         0  strncpy.o ABSOLUTE
    ../clib/microlib/string/strstr.c         0x00000000   Number         0  strstr.o ABSOLUTE
    ../clib/microlib/string/strtok.c         0x00000000   Number         0  strtok.o ABSOLUTE
    ../clib/microlib/string/strtok.c         0x00000000   Number         0  strtok_r.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _chval.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _strtoul.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtol.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  atoi.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ..\Ethernet\APP\dhcp.c                   0x00000000   Number         0  dhcp.o ABSOLUTE
    ..\Ethernet\APP\tcp_client.c             0x00000000   Number         0  tcp_client.o ABSOLUTE
    ..\Ethernet\W5500\socket.c               0x00000000   Number         0  socket.o ABSOLUTE
    ..\Ethernet\W5500\utility.c              0x00000000   Number         0  utility.o ABSOLUTE
    ..\Ethernet\W5500\w5500.c                0x00000000   Number         0  w5500.o ABSOLUTE
    ..\Ethernet\W5500\w5500_conf.c           0x00000000   Number         0  w5500_conf.o ABSOLUTE
    ..\\Ethernet\\APP\\dhcp.c                0x00000000   Number         0  dhcp.o ABSOLUTE
    ..\\Ethernet\\APP\\tcp_client.c          0x00000000   Number         0  tcp_client.o ABSOLUTE
    ..\\Ethernet\\W5500\\socket.c            0x00000000   Number         0  socket.o ABSOLUTE
    ..\\Ethernet\\W5500\\w5500.c             0x00000000   Number         0  w5500.o ABSOLUTE
    ..\\Ethernet\\W5500\\w5500_conf.c        0x00000000   Number         0  w5500_conf.o ABSOLUTE
    Firmware\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s 0x00000000   Number         0  startup_gd32f450_470.o ABSOLUTE
    Firmware\CMSIS\GD\GD32F4xx\Source\system_gd32f4xx.c 0x00000000   Number         0  system_gd32f4xx.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_adc.c 0x00000000   Number         0  gd32f4xx_adc.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_can.c 0x00000000   Number         0  gd32f4xx_can.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_crc.c 0x00000000   Number         0  gd32f4xx_crc.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_ctc.c 0x00000000   Number         0  gd32f4xx_ctc.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_dac.c 0x00000000   Number         0  gd32f4xx_dac.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_dbg.c 0x00000000   Number         0  gd32f4xx_dbg.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_dci.c 0x00000000   Number         0  gd32f4xx_dci.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_dma.c 0x00000000   Number         0  gd32f4xx_dma.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_enet.c 0x00000000   Number         0  gd32f4xx_enet.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_exmc.c 0x00000000   Number         0  gd32f4xx_exmc.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_exti.c 0x00000000   Number         0  gd32f4xx_exti.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_fmc.c 0x00000000   Number         0  gd32f4xx_fmc.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_fwdgt.c 0x00000000   Number         0  gd32f4xx_fwdgt.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_gpio.c 0x00000000   Number         0  gd32f4xx_gpio.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_i2c.c 0x00000000   Number         0  gd32f4xx_i2c.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_ipa.c 0x00000000   Number         0  gd32f4xx_ipa.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_iref.c 0x00000000   Number         0  gd32f4xx_iref.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_misc.c 0x00000000   Number         0  gd32f4xx_misc.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_pmu.c 0x00000000   Number         0  gd32f4xx_pmu.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_rcu.c 0x00000000   Number         0  gd32f4xx_rcu.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_rtc.c 0x00000000   Number         0  gd32f4xx_rtc.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_sdio.c 0x00000000   Number         0  gd32f4xx_sdio.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_spi.c 0x00000000   Number         0  gd32f4xx_spi.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_syscfg.c 0x00000000   Number         0  gd32f4xx_syscfg.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_timer.c 0x00000000   Number         0  gd32f4xx_timer.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_tli.c 0x00000000   Number         0  gd32f4xx_tli.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_trng.c 0x00000000   Number         0  gd32f4xx_trng.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_usart.c 0x00000000   Number         0  gd32f4xx_usart.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_wwdgt.c 0x00000000   Number         0  gd32f4xx_wwdgt.o ABSOLUTE
    Firmware\\CMSIS\\GD\\GD32F4xx\\Source\\system_gd32f4xx.c 0x00000000   Number         0  system_gd32f4xx.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_adc.c 0x00000000   Number         0  gd32f4xx_adc.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_can.c 0x00000000   Number         0  gd32f4xx_can.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_crc.c 0x00000000   Number         0  gd32f4xx_crc.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_ctc.c 0x00000000   Number         0  gd32f4xx_ctc.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dac.c 0x00000000   Number         0  gd32f4xx_dac.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dbg.c 0x00000000   Number         0  gd32f4xx_dbg.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dci.c 0x00000000   Number         0  gd32f4xx_dci.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dma.c 0x00000000   Number         0  gd32f4xx_dma.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_enet.c 0x00000000   Number         0  gd32f4xx_enet.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_exmc.c 0x00000000   Number         0  gd32f4xx_exmc.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_exti.c 0x00000000   Number         0  gd32f4xx_exti.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_fmc.c 0x00000000   Number         0  gd32f4xx_fmc.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_fwdgt.c 0x00000000   Number         0  gd32f4xx_fwdgt.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_gpio.c 0x00000000   Number         0  gd32f4xx_gpio.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_i2c.c 0x00000000   Number         0  gd32f4xx_i2c.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_ipa.c 0x00000000   Number         0  gd32f4xx_ipa.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_iref.c 0x00000000   Number         0  gd32f4xx_iref.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_misc.c 0x00000000   Number         0  gd32f4xx_misc.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_pmu.c 0x00000000   Number         0  gd32f4xx_pmu.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_rcu.c 0x00000000   Number         0  gd32f4xx_rcu.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_rtc.c 0x00000000   Number         0  gd32f4xx_rtc.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_sdio.c 0x00000000   Number         0  gd32f4xx_sdio.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_spi.c 0x00000000   Number         0  gd32f4xx_spi.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_syscfg.c 0x00000000   Number         0  gd32f4xx_syscfg.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_timer.c 0x00000000   Number         0  gd32f4xx_timer.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_tli.c 0x00000000   Number         0  gd32f4xx_tli.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_trng.c 0x00000000   Number         0  gd32f4xx_trng.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_usart.c 0x00000000   Number         0  gd32f4xx_usart.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_wwdgt.c 0x00000000   Number         0  gd32f4xx_wwdgt.o ABSOLUTE
    USER\\gd32f4xx_it.c                      0x00000000   Number         0  gd32f4xx_it.o ABSOLUTE
    USER\\main.c                             0x00000000   Number         0  main.o ABSOLUTE
    USER\\systick.c                          0x00000000   Number         0  systick.o ABSOLUTE
    USER\gd32f4xx_it.c                       0x00000000   Number         0  gd32f4xx_it.o ABSOLUTE
    USER\main.c                              0x00000000   Number         0  main.o ABSOLUTE
    USER\systick.c                           0x00000000   Number         0  systick.o ABSOLUTE
    Utilities\25LC080A.c                     0x00000000   Number         0  25lc080a.o ABSOLUTE
    Utilities\API_LAN_DATA_Process .c        0x00000000   Number         0  api_lan_data_process .o ABSOLUTE
    Utilities\API_TNRG.c                     0x00000000   Number         0  api_tnrg.o ABSOLUTE
    Utilities\API_W5500.c                    0x00000000   Number         0  api_w5500.o ABSOLUTE
    Utilities\My_CRC.c                       0x00000000   Number         0  my_crc.o ABSOLUTE
    Utilities\\25LC080A.c                    0x00000000   Number         0  25lc080a.o ABSOLUTE
    Utilities\\API_LAN_DATA_Process .c       0x00000000   Number         0  api_lan_data_process .o ABSOLUTE
    Utilities\\API_TNRG.c                    0x00000000   Number         0  api_tnrg.o ABSOLUTE
    Utilities\\API_W5500.c                   0x00000000   Number         0  api_w5500.o ABSOLUTE
    Utilities\\My_CRC.c                      0x00000000   Number         0  my_crc.o ABSOLUTE
    Utilities\\eeprom_spi.c                  0x00000000   Number         0  eeprom_spi.o ABSOLUTE
    Utilities\\flash.c                       0x00000000   Number         0  flash.o ABSOLUTE
    Utilities\\gd32f470v_start.c             0x00000000   Number         0  gd32f470v_start.o ABSOLUTE
    Utilities\\gpio.c                        0x00000000   Number         0  gpio.o ABSOLUTE
    Utilities\\rtc.c                         0x00000000   Number         0  rtc.o ABSOLUTE
    Utilities\\spi.c                         0x00000000   Number         0  spi.o ABSOLUTE
    Utilities\\time.c                        0x00000000   Number         0  time.o ABSOLUTE
    Utilities\\usart.c                       0x00000000   Number         0  usart.o ABSOLUTE
    Utilities\\user_step.c                   0x00000000   Number         0  user_step.o ABSOLUTE
    Utilities\eeprom_spi.c                   0x00000000   Number         0  eeprom_spi.o ABSOLUTE
    Utilities\flash.c                        0x00000000   Number         0  flash.o ABSOLUTE
    Utilities\gd32f470v_start.c              0x00000000   Number         0  gd32f470v_start.o ABSOLUTE
    Utilities\gpio.c                         0x00000000   Number         0  gpio.o ABSOLUTE
    Utilities\rtc.c                          0x00000000   Number         0  rtc.o ABSOLUTE
    Utilities\spi.c                          0x00000000   Number         0  spi.o ABSOLUTE
    Utilities\time.c                         0x00000000   Number         0  time.o ABSOLUTE
    Utilities\usart.c                        0x00000000   Number         0  usart.o ABSOLUTE
    Utilities\user_step.c                    0x00000000   Number         0  user_step.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    RESET                                    0x08000000   Section      428  startup_gd32f450_470.o(RESET)
    .ARM.Collect$$$$00000000                 0x080001ac   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x080001ac   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x080001b0   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x080001b4   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x080001b4   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x080001b4   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000E                 0x080001bc   Section        4  entry12b.o(.ARM.Collect$$$$0000000E)
    .ARM.Collect$$$$0000000F                 0x080001c0   Section        0  entry10a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00000011                 0x080001c0   Section        0  entry11a.o(.ARM.Collect$$$$00000011)
    .ARM.Collect$$$$00002712                 0x080001c0   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x080001c0   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x080001c4   Section       36  startup_gd32f450_470.o(.text)
    $v0                                      0x080001c4   Number         0  startup_gd32f450_470.o(.text)
    .text                                    0x080001e8   Section        0  memcpya.o(.text)
    .text                                    0x0800020c   Section        0  memseta.o(.text)
    .text                                    0x08000230   Section        0  strlen.o(.text)
    .text                                    0x0800023e   Section        0  strcpy.o(.text)
    .text                                    0x08000250   Section        0  uidiv.o(.text)
    .text                                    0x0800027c   Section        0  uldiv.o(.text)
    .text                                    0x080002de   Section        0  dadd.o(.text)
    .text                                    0x080002de   Section        0  iusefp.o(.text)
    .text                                    0x0800042c   Section        0  dmul.o(.text)
    .text                                    0x08000510   Section        0  ddiv.o(.text)
    .text                                    0x080005ee   Section        0  dfixul.o(.text)
    .text                                    0x08000620   Section       48  cdrcmple.o(.text)
    .text                                    0x08000650   Section       36  init.o(.text)
    .text                                    0x08000674   Section        0  llshl.o(.text)
    .text                                    0x08000692   Section        0  llushr.o(.text)
    .text                                    0x080006b2   Section        0  llsshr.o(.text)
    .text                                    0x080006d6   Section        0  depilogue.o(.text)
    .text                                    0x08000790   Section        0  __dczerorl2.o(.text)
    i.API_ACK_20CMD                          0x080007e6   Section        0  api_lan_data_process .o(i.API_ACK_20CMD)
    i.API_ACK_21CMD                          0x080007e8   Section        0  api_lan_data_process .o(i.API_ACK_21CMD)
    i.API_ACK_22CMD                          0x08000854   Section        0  api_lan_data_process .o(i.API_ACK_22CMD)
    i.API_ACK_23CMD                          0x08000938   Section        0  api_lan_data_process .o(i.API_ACK_23CMD)
    i.API_ACK_24CMD                          0x08000a24   Section        0  api_lan_data_process .o(i.API_ACK_24CMD)
    i.API_ACK_25CMD                          0x08000ad4   Section        0  api_lan_data_process .o(i.API_ACK_25CMD)
    i.API_ACK_26CMD                          0x08000ad8   Section        0  api_lan_data_process .o(i.API_ACK_26CMD)
    i.API_ACK_27CMD                          0x08000b94   Section        0  api_lan_data_process .o(i.API_ACK_27CMD)
    i.API_ACK_28CMD                          0x08000b98   Section        0  api_lan_data_process .o(i.API_ACK_28CMD)
    i.API_ACK_29CMD                          0x08000c10   Section        0  api_lan_data_process .o(i.API_ACK_29CMD)
    i.API_Chose_TS5A3359_GAIN                0x08000cd8   Section        0  gpio.o(i.API_Chose_TS5A3359_GAIN)
    i.API_ExecuteSeverCMD                    0x08000d40   Section        0  api_lan_data_process .o(i.API_ExecuteSeverCMD)
    i.API_Init_LAN                           0x08000e48   Section        0  api_w5500.o(i.API_Init_LAN)
    i.API_Init_Net_Parameters                0x08000e58   Section        0  api_w5500.o(i.API_Init_Net_Parameters)
    i.API_LED_GPIO                           0x08001144   Section        0  gpio.o(i.API_LED_GPIO)
    i.API_PHY_Check                          0x08001194   Section        0  api_w5500.o(i.API_PHY_Check)
    i.API_Printf_Hex                         0x080012c0   Section        0  usart.o(i.API_Printf_Hex)
    i.API_Process_Lan_Data                   0x080012f8   Section        0  api_lan_data_process .o(i.API_Process_Lan_Data)
    i.API_RNG_Init                           0x080015bc   Section        0  api_tnrg.o(i.API_RNG_Init)
    i.API_SPI0_Send_Read_Byte                0x08001700   Section        0  api_w5500.o(i.API_SPI0_Send_Read_Byte)
    i.API_W5500_GPIO_Init                    0x08001738   Section        0  api_w5500.o(i.API_W5500_GPIO_Init)
    i.API_W5500_Reset                        0x08001760   Section        0  api_w5500.o(i.API_W5500_Reset)
    i.API_W5500_SPI0_Init                    0x080018f0   Section        0  api_w5500.o(i.API_W5500_SPI0_Init)
    i.API_Write_SeverInfo                    0x08001990   Section        0  api_lan_data_process .o(i.API_Write_SeverInfo)
    i.API_do_tcp_client                      0x08001ad0   Section        0  usart.o(i.API_do_tcp_client)
    i.AddByteToBuffer                        0x08001cf0   Section        0  usart.o(i.AddByteToBuffer)
    AddByteToBuffer                          0x08001cf1   Thumb Code   110  usart.o(i.AddByteToBuffer)
    i.BusFault_Handler                       0x08001d5e   Section        0  gd32f4xx_it.o(i.BusFault_Handler)
    i.CRC16_USB                              0x08001d62   Section        0  my_crc.o(i.CRC16_USB)
    i.DHCP_run                               0x08001de0   Section        0  dhcp.o(i.DHCP_run)
    i.DMA1_Channel0_IRQHandler               0x0800209c   Section        0  gd32f4xx_it.o(i.DMA1_Channel0_IRQHandler)
    i.DMA1_Channel1_IRQHandler               0x0800209e   Section        0  gd32f4xx_it.o(i.DMA1_Channel1_IRQHandler)
    i.DMA1_Channel2_IRQHandler               0x080020a0   Section        0  gd32f4xx_it.o(i.DMA1_Channel2_IRQHandler)
    i.DRV_SPI_SwapByte                       0x080020a4   Section        0  spi.o(i.DRV_SPI_SwapByte)
    i.DebugMon_Handler                       0x080020dc   Section        0  gd32f4xx_it.o(i.DebugMon_Handler)
    i.EEPROM_SPI_ReadBuffer                  0x080020e0   Section        0  eeprom_spi.o(i.EEPROM_SPI_ReadBuffer)
    i.EEPROM_SPI_SendInstruction             0x0800214c   Section        0  eeprom_spi.o(i.EEPROM_SPI_SendInstruction)
    i.EEPROM_SPI_WaitStandbyState            0x08002180   Section        0  eeprom_spi.o(i.EEPROM_SPI_WaitStandbyState)
    i.EEPROM_SPI_WriteBuffer                 0x080021cc   Section        0  eeprom_spi.o(i.EEPROM_SPI_WriteBuffer)
    i.EEPROM_SPI_WritePage                   0x08002358   Section        0  eeprom_spi.o(i.EEPROM_SPI_WritePage)
    i.EEPROM_WriteDisable                    0x0800240c   Section        0  eeprom_spi.o(i.EEPROM_WriteDisable)
    i.EEPROM_WriteEnable                     0x0800243c   Section        0  eeprom_spi.o(i.EEPROM_WriteEnable)
    i.EXTI10_15_IRQHandler                   0x0800246c   Section        0  gd32f4xx_it.o(i.EXTI10_15_IRQHandler)
    i.FLASH_If_Init                          0x08002470   Section        0  flash.o(i.FLASH_If_Init)
    i.FML_USART_RecvTask                     0x08002480   Section        0  usart.o(i.FML_USART_RecvTask)
    i.GPIO_Init                              0x08002538   Section        0  gpio.o(i.GPIO_Init)
    i.HardFault_Handler                      0x080025a0   Section        0  gd32f4xx_it.o(i.HardFault_Handler)
    i.IINCHIP_READ                           0x080025b0   Section        0  w5500_conf.o(i.IINCHIP_READ)
    i.IINCHIP_SpiSendData                    0x080025e2   Section        0  w5500_conf.o(i.IINCHIP_SpiSendData)
    i.IINCHIP_WRITE                          0x080025ee   Section        0  w5500_conf.o(i.IINCHIP_WRITE)
    i.Init_GPIO_TS5A339                      0x08002620   Section        0  gpio.o(i.Init_GPIO_TS5A339)
    i.InvertUint16                           0x08002648   Section        0  my_crc.o(i.InvertUint16)
    InvertUint16                             0x08002649   Thumb Code    58  my_crc.o(i.InvertUint16)
    i.InvertUint8                            0x08002682   Section        0  my_crc.o(i.InvertUint8)
    InvertUint8                              0x08002683   Thumb Code    58  my_crc.o(i.InvertUint8)
    i.MemManage_Handler                      0x080026bc   Section        0  gd32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x080026c0   Section        0  gd32f4xx_it.o(i.NMI_Handler)
    i.PHY_check                              0x080026c4   Section        0  w5500_conf.o(i.PHY_check)
    i.PendSV_Handler                         0x08002734   Section        0  gd32f4xx_it.o(i.PendSV_Handler)
    i.RTC_Init                               0x08002738   Section        0  rtc.o(i.RTC_Init)
    i.ReadBytesToBuffer                      0x0800279c   Section        0  usart.o(i.ReadBytesToBuffer)
    ReadBytesToBuffer                        0x0800279d   Thumb Code   142  usart.o(i.ReadBytesToBuffer)
    i.RecvDataHandler                        0x0800282a   Section        0  usart.o(i.RecvDataHandler)
    RecvDataHandler                          0x0800282b   Thumb Code    30  usart.o(i.RecvDataHandler)
    i.SPI1_Init                              0x08002848   Section        0  spi.o(i.SPI1_Init)
    i.SVC_Handler                            0x080028e4   Section        0  gd32f4xx_it.o(i.SVC_Handler)
    i.SysReset_Condition                     0x080028e8   Section        0  user_step.o(i.SysReset_Condition)
    i.SysTick_Handler                        0x08002920   Section        0  gd32f4xx_it.o(i.SysTick_Handler)
    i.SystemInit                             0x08002928   Section        0  system_gd32f4xx.o(i.SystemInit)
    i.TIMER1_Init                            0x080029f0   Section        0  time.o(i.TIMER1_Init)
    i.TIMER2_IRQHandler                      0x08002a66   Section        0  gd32f4xx_it.o(i.TIMER2_IRQHandler)
    i.TIMER3_IRQHandler                      0x08002a68   Section        0  gd32f4xx_it.o(i.TIMER3_IRQHandler)
    i.TIMER3_Init                            0x08002a80   Section        0  time.o(i.TIMER3_Init)
    i.TIMER6_IRQHandler                      0x08002ad4   Section        0  gd32f4xx_it.o(i.TIMER6_IRQHandler)
    i.TIMER6_Init                            0x08002aec   Section        0  time.o(i.TIMER6_Init)
    i.TIM_PeriodElapsedCallback              0x08002b40   Section        0  time.o(i.TIM_PeriodElapsedCallback)
    i.UART_IDLECallBack                      0x08002cd0   Section        0  usart.o(i.UART_IDLECallBack)
    i.UART_RxCpltCallback                    0x08002cf8   Section        0  usart.o(i.UART_RxCpltCallback)
    i.USART0_Init                            0x08002d24   Section        0  usart.o(i.USART0_Init)
    i.USART2_IRQHandler                      0x08002e5c   Section        0  gd32f4xx_it.o(i.USART2_IRQHandler)
    i.USER_EEPROM_RedeByte                   0x08002e8c   Section        0  25lc080a.o(i.USER_EEPROM_RedeByte)
    i.USER_EEPROM_WriteByte                  0x08002ed4   Section        0  25lc080a.o(i.USER_EEPROM_WriteByte)
    i.UsageFault_Handler                     0x08003100   Section        0  gd32f4xx_it.o(i.UsageFault_Handler)
    i.__0printf                              0x08003104   Section        0  printfa.o(i.__0printf)
    i.__0sprintf                             0x08003124   Section        0  printfa.o(i.__0sprintf)
    i.__NVIC_SetPriority                     0x0800314c   Section        0  systick.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x0800314d   Thumb Code    32  systick.o(i.__NVIC_SetPriority)
    i.__scatterload_copy                     0x08003174   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x08003182   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08003184   Section       14  handlers.o(i.__scatterload_zeroinit)
    i._fp_digits                             0x08003194   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x08003195   Thumb Code   366  printfa.o(i._fp_digits)
    i._printf_core                           0x08003318   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x08003319   Thumb Code  1704  printfa.o(i._printf_core)
    i._printf_post_padding                   0x080039cc   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x080039cd   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x080039f0   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x080039f1   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i._sputc                                 0x08003a1e   Section        0  printfa.o(i._sputc)
    _sputc                                   0x08003a1f   Thumb Code    10  printfa.o(i._sputc)
    i.bsp_CmpCpuFlash                        0x08003a28   Section        0  flash.o(i.bsp_CmpCpuFlash)
    i.bsp_EraseCpuFlash                      0x08003a6c   Section        0  flash.o(i.bsp_EraseCpuFlash)
    i.bsp_GetSector                          0x08003a94   Section        0  flash.o(i.bsp_GetSector)
    i.bsp_WriteCpuFlash                      0x08003cc4   Section        0  flash.o(i.bsp_WriteCpuFlash)
    i.check_DHCP_leasedIP                    0x08003d64   Section        0  dhcp.o(i.check_DHCP_leasedIP)
    i.check_DHCP_timeout                     0x08003db4   Section        0  dhcp.o(i.check_DHCP_timeout)
    i.close                                  0x08003e5c   Section        0  socket.o(i.close)
    i.connect                                0x08003e90   Section        0  socket.o(i.connect)
    i.default_ip_assign                      0x0800403c   Section        0  dhcp.o(i.default_ip_assign)
    i.default_ip_conflict                    0x08004060   Section        0  dhcp.o(i.default_ip_conflict)
    i.default_ip_update                      0x08004074   Section        0  dhcp.o(i.default_ip_update)
    i.delay_1ms                              0x0800408c   Section        0  systick.o(i.delay_1ms)
    i.delay_decrement                        0x080040a0   Section        0  systick.o(i.delay_decrement)
    i.disconnect                             0x080040b8   Section        0  socket.o(i.disconnect)
    i.fmc_lock                               0x080040e0   Section        0  gd32f4xx_fmc.o(i.fmc_lock)
    i.fmc_ready_wait                         0x080040f4   Section        0  gd32f4xx_fmc.o(i.fmc_ready_wait)
    i.fmc_sector_erase                       0x08004114   Section        0  gd32f4xx_fmc.o(i.fmc_sector_erase)
    i.fmc_state_get                          0x08004174   Section        0  gd32f4xx_fmc.o(i.fmc_state_get)
    i.fmc_unlock                             0x080041c0   Section        0  gd32f4xx_fmc.o(i.fmc_unlock)
    i.fmc_word_program                       0x080041e4   Section        0  gd32f4xx_fmc.o(i.fmc_word_program)
    i.fputc                                  0x08004238   Section        0  usart.o(i.fputc)
    i.free                                   0x0800425c   Section        0  malloc.o(i.free)
    i.getIINCHIP_TxMAX                       0x080042ac   Section        0  w5500.o(i.getIINCHIP_TxMAX)
    i.getPHYStatus                           0x080042bc   Section        0  w5500_conf.o(i.getPHYStatus)
    i.getSHAR                                0x080042c8   Section        0  w5500.o(i.getSHAR)
    i.getSn_IR                               0x080042da   Section        0  w5500.o(i.getSn_IR)
    i.getSn_RX_RSR                           0x080042ec   Section        0  w5500.o(i.getSn_RX_RSR)
    i.getSn_SR                               0x08004340   Section        0  w5500.o(i.getSn_SR)
    i.getSn_TX_FSR                           0x08004352   Section        0  w5500.o(i.getSn_TX_FSR)
    i.get_hard_rand_data                     0x080043a6   Section        0  api_tnrg.o(i.get_hard_rand_data)
    i.gpio_af_set                            0x080043ba   Section        0  gd32f4xx_gpio.o(i.gpio_af_set)
    i.gpio_bit_reset                         0x08004418   Section        0  gd32f4xx_gpio.o(i.gpio_bit_reset)
    i.gpio_bit_set                           0x0800441c   Section        0  gd32f4xx_gpio.o(i.gpio_bit_set)
    i.gpio_mode_set                          0x08004420   Section        0  gd32f4xx_gpio.o(i.gpio_mode_set)
    i.gpio_output_options_set                0x0800446e   Section        0  gd32f4xx_gpio.o(i.gpio_output_options_set)
    i.iinchip_csoff                          0x080044b0   Section        0  w5500_conf.o(i.iinchip_csoff)
    i.iinchip_cson                           0x080044c4   Section        0  w5500_conf.o(i.iinchip_cson)
    i.init_dhcp_client                       0x080044d8   Section        0  dhcp.o(i.init_dhcp_client)
    i.main                                   0x0800451c   Section        0  main.o(i.main)
    i.makeDHCPMSG                            0x080047d0   Section        0  dhcp.o(i.makeDHCPMSG)
    i.malloc                                 0x08004958   Section        0  malloc.o(i.malloc)
    i.nvic_irq_enable                        0x080049c4   Section        0  gd32f4xx_misc.o(i.nvic_irq_enable)
    i.nvic_priority_group_set                0x08004a88   Section        0  gd32f4xx_misc.o(i.nvic_priority_group_set)
    i.nvic_vector_table_set                  0x08004a9c   Section        0  gd32f4xx_misc.o(i.nvic_vector_table_set)
    i.parseDHCPMSG                           0x08004ab4   Section        0  dhcp.o(i.parseDHCPMSG)
    i.pmu_backup_write_enable                0x08004c98   Section        0  gd32f4xx_pmu.o(i.pmu_backup_write_enable)
    i.print_system_status                    0x08004cac   Section        0  main.o(i.print_system_status)
    i.rcu_clock_freq_get                     0x08004e80   Section        0  gd32f4xx_rcu.o(i.rcu_clock_freq_get)
    i.rcu_flag_get                           0x08004fa4   Section        0  gd32f4xx_rcu.o(i.rcu_flag_get)
    i.rcu_osci_on                            0x08004fc8   Section        0  gd32f4xx_rcu.o(i.rcu_osci_on)
    i.rcu_osci_stab_wait                     0x08004fec   Section        0  gd32f4xx_rcu.o(i.rcu_osci_stab_wait)
    i.rcu_periph_clock_enable                0x08005148   Section        0  gd32f4xx_rcu.o(i.rcu_periph_clock_enable)
    i.rcu_periph_reset_disable               0x0800516c   Section        0  gd32f4xx_rcu.o(i.rcu_periph_reset_disable)
    i.rcu_periph_reset_enable                0x08005190   Section        0  gd32f4xx_rcu.o(i.rcu_periph_reset_enable)
    i.rcu_rtc_clock_config                   0x080051b4   Section        0  gd32f4xx_rcu.o(i.rcu_rtc_clock_config)
    i.recv                                   0x080051cc   Section        0  socket.o(i.recv)
    i.recv_data_processing                   0x08005210   Section        0  w5500.o(i.recv_data_processing)
    i.recvfrom                               0x080052a4   Section        0  socket.o(i.recvfrom)
    i.reset_DHCP_timeout                     0x080054a4   Section        0  dhcp.o(i.reset_DHCP_timeout)
    i.reset_w5500                            0x080054c4   Section        0  w5500_conf.o(i.reset_w5500)
    i.rtc_init                               0x08005508   Section        0  gd32f4xx_rtc.o(i.rtc_init)
    i.rtc_init_mode_enter                    0x080055cc   Section        0  gd32f4xx_rtc.o(i.rtc_init_mode_enter)
    i.rtc_init_mode_exit                     0x08005614   Section        0  gd32f4xx_rtc.o(i.rtc_init_mode_exit)
    i.rtc_register_sync_wait                 0x08005628   Section        0  gd32f4xx_rtc.o(i.rtc_register_sync_wait)
    i.send                                   0x08005688   Section        0  socket.o(i.send)
    i.send_DHCP_DECLINE                      0x080057c4   Section        0  dhcp.o(i.send_DHCP_DECLINE)
    i.send_DHCP_DISCOVER                     0x080059ac   Section        0  dhcp.o(i.send_DHCP_DISCOVER)
    i.send_DHCP_REQUEST                      0x08005ba0   Section        0  dhcp.o(i.send_DHCP_REQUEST)
    i.send_data_processing                   0x08005f14   Section        0  w5500.o(i.send_data_processing)
    i.sendto                                 0x08005fa8   Section        0  socket.o(i.sendto)
    i.setGAR                                 0x080060b4   Section        0  w5500.o(i.setGAR)
    i.setMR                                  0x080060c4   Section        0  w5500.o(i.setMR)
    i.setSHAR                                0x080060d2   Section        0  w5500.o(i.setSHAR)
    i.setSIPR                                0x080060e4   Section        0  w5500.o(i.setSIPR)
    i.setSUBR                                0x080060f6   Section        0  w5500.o(i.setSUBR)
    i.setSn_IR                               0x08006108   Section        0  w5500.o(i.setSn_IR)
    i.set_w5500_mac                          0x08006120   Section        0  w5500_conf.o(i.set_w5500_mac)
    i.socket                                 0x08006188   Section        0  socket.o(i.socket)
    i.socket_buf_init                        0x0800624c   Section        0  w5500.o(i.socket_buf_init)
    i.spi_enable                             0x080062d4   Section        0  gd32f4xx_spi.o(i.spi_enable)
    i.spi_i2s_data_receive                   0x080062de   Section        0  gd32f4xx_spi.o(i.spi_i2s_data_receive)
    i.spi_i2s_data_transmit                  0x080062e6   Section        0  gd32f4xx_spi.o(i.spi_i2s_data_transmit)
    i.spi_i2s_flag_get                       0x080062ea   Section        0  gd32f4xx_spi.o(i.spi_i2s_flag_get)
    i.spi_init                               0x080062fa   Section        0  gd32f4xx_spi.o(i.spi_init)
    i.system_clock_168m_25m_hxtal            0x0800632c   Section        0  system_gd32f4xx.o(i.system_clock_168m_25m_hxtal)
    system_clock_168m_25m_hxtal              0x0800632d   Thumb Code   240  system_gd32f4xx.o(i.system_clock_168m_25m_hxtal)
    i.system_clock_config                    0x08006428   Section        0  system_gd32f4xx.o(i.system_clock_config)
    system_clock_config                      0x08006429   Thumb Code     8  system_gd32f4xx.o(i.system_clock_config)
    i.systick_config                         0x08006430   Section        0  systick.o(i.systick_config)
    i.timer_channel_output_config            0x08006480   Section        0  gd32f4xx_timer.o(i.timer_channel_output_config)
    i.timer_channel_output_mode_config       0x0800666c   Section        0  gd32f4xx_timer.o(i.timer_channel_output_mode_config)
    i.timer_channel_output_pulse_value_config 0x080066c6   Section        0  gd32f4xx_timer.o(i.timer_channel_output_pulse_value_config)
    i.timer_channel_output_shadow_config     0x080066ec   Section        0  gd32f4xx_timer.o(i.timer_channel_output_shadow_config)
    i.timer_deinit                           0x08006748   Section        0  gd32f4xx_timer.o(i.timer_deinit)
    i.timer_enable                           0x080068cc   Section        0  gd32f4xx_timer.o(i.timer_enable)
    i.timer_init                             0x080068d8   Section        0  gd32f4xx_timer.o(i.timer_init)
    i.timer_interrupt_enable                 0x08006970   Section        0  gd32f4xx_timer.o(i.timer_interrupt_enable)
    i.timer_interrupt_flag_clear             0x08006978   Section        0  gd32f4xx_timer.o(i.timer_interrupt_flag_clear)
    i.timer_interrupt_flag_get               0x0800697e   Section        0  gd32f4xx_timer.o(i.timer_interrupt_flag_get)
    i.trng_deinit                            0x08006996   Section        0  gd32f4xx_trng.o(i.trng_deinit)
    i.trng_enable                            0x080069ac   Section        0  gd32f4xx_trng.o(i.trng_enable)
    i.trng_flag_get                          0x080069c0   Section        0  gd32f4xx_trng.o(i.trng_flag_get)
    i.trng_get_true_random_data              0x080069d8   Section        0  gd32f4xx_trng.o(i.trng_get_true_random_data)
    i.usart_baudrate_set                     0x080069e4   Section        0  gd32f4xx_usart.o(i.usart_baudrate_set)
    i.usart_data_receive                     0x08006acc   Section        0  gd32f4xx_usart.o(i.usart_data_receive)
    i.usart_data_transmit                    0x08006ad6   Section        0  gd32f4xx_usart.o(i.usart_data_transmit)
    i.usart_deinit                           0x08006ae0   Section        0  gd32f4xx_usart.o(i.usart_deinit)
    i.usart_enable                           0x08006bbc   Section        0  gd32f4xx_usart.o(i.usart_enable)
    i.usart_flag_get                         0x08006bc6   Section        0  gd32f4xx_usart.o(i.usart_flag_get)
    i.usart_hardware_flow_cts_config         0x08006be4   Section        0  gd32f4xx_usart.o(i.usart_hardware_flow_cts_config)
    i.usart_hardware_flow_rts_config         0x08006bf4   Section        0  gd32f4xx_usart.o(i.usart_hardware_flow_rts_config)
    i.usart_interrupt_flag_clear             0x08006c04   Section        0  gd32f4xx_usart.o(i.usart_interrupt_flag_clear)
    i.usart_interrupt_flag_get               0x08006c1e   Section        0  gd32f4xx_usart.o(i.usart_interrupt_flag_get)
    i.usart_parity_config                    0x08006c56   Section        0  gd32f4xx_usart.o(i.usart_parity_config)
    i.usart_receive_config                   0x08006c66   Section        0  gd32f4xx_usart.o(i.usart_receive_config)
    i.usart_stop_bit_set                     0x08006c76   Section        0  gd32f4xx_usart.o(i.usart_stop_bit_set)
    i.usart_transmit_config                  0x08006c86   Section        0  gd32f4xx_usart.o(i.usart_transmit_config)
    i.usart_word_length_set                  0x08006c96   Section        0  gd32f4xx_usart.o(i.usart_word_length_set)
    i.wiz_read_buf                           0x08006ca8   Section        0  w5500_conf.o(i.wiz_read_buf)
    i.wiz_write_buf                          0x08006d10   Section        0  w5500_conf.o(i.wiz_write_buf)
    .NoInit                                  0x20000000   Section        4  user_step.o(.NoInit)
    .data                                    0x20000004   Section        4  system_gd32f4xx.o(.data)
    .data                                    0x20000008   Section       12  main.o(.data)
    status_counter                           0x20000010   Data           4  main.o(.data)
    .data                                    0x20000014   Section        4  systick.o(.data)
    delay                                    0x20000014   Data           4  systick.o(.data)
    .data                                    0x20000018   Section        6  time.o(.data)
    .data                                    0x2000001e   Section        1  usart.o(.data)
    last_state                               0x2000001e   Data           1  usart.o(.data)
    .data                                    0x20000020   Section        2  api_lan_data_process .o(.data)
    Cnt_20ms                                 0x20000020   Data           2  api_lan_data_process .o(.data)
    .data                                    0x20000022   Section        1  api_w5500.o(.data)
    i                                        0x20000022   Data           1  api_w5500.o(.data)
    .data                                    0x20000023   Section       16  w5500.o(.data)
    .data                                    0x20000034   Section       76  dhcp.o(.data)
    .data                                    0x20000080   Section        4  stdout.o(.data)
    .data                                    0x20000084   Section        4  mvars.o(.data)
    .data                                    0x20000088   Section        4  mvars.o(.data)
    .bss                                     0x2000008c   Section       20  rtc.o(.bss)
    .bss                                     0x200000a0   Section       20  time.o(.bss)
    .bss                                     0x200000b4   Section       32  w5500.o(.bss)
    .bss                                     0x200000d4   Section     1024  dhcp.o(.bss)
    HEAP                                     0x200004d8   Section    12288  startup_gd32f450_470.o(HEAP)
    STACK                                    0x200034d8   Section    20480  startup_gd32f450_470.o(STACK)
    .RAM_D3                                  0x20020000   Section     6167  usart.o(.RAM_D3)
    .RAM_D3                                  0x20021818   Section     2120  api_w5500.o(.RAM_D3)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __arm_fini_                               - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x000001ac   Number         0  startup_gd32f450_470.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_gd32f450_470.o(RESET)
    __Vectors_End                            0x080001ac   Data           0  startup_gd32f450_470.o(RESET)
    __main                                   0x080001ad   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x080001ad   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x080001b1   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x080001b5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x080001b5   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x080001b5   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x080001b5   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_lib_shutdown_fini                   0x080001bd   Thumb Code     0  entry12b.o(.ARM.Collect$$$$0000000E)
    __rt_final_cpp                           0x080001c1   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000F)
    __rt_final_exit                          0x080001c1   Thumb Code     0  entry11a.o(.ARM.Collect$$$$00000011)
    Reset_Handler                            0x080001c5   Thumb Code     8  startup_gd32f450_470.o(.text)
    ADC_IRQHandler                           0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_EWMC_IRQHandler                     0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_RX0_IRQHandler                      0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_RX1_IRQHandler                      0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_TX_IRQHandler                       0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_EWMC_IRQHandler                     0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_RX0_IRQHandler                      0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_RX1_IRQHandler                      0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_TX_IRQHandler                       0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    DCI_IRQHandler                           0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel0_IRQHandler                 0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel1_IRQHandler                 0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel2_IRQHandler                 0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel3_IRQHandler                 0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel4_IRQHandler                 0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel5_IRQHandler                 0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel6_IRQHandler                 0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel7_IRQHandler                 0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel3_IRQHandler                 0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel4_IRQHandler                 0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel5_IRQHandler                 0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel6_IRQHandler                 0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel7_IRQHandler                 0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    ENET_IRQHandler                          0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    ENET_WKUP_IRQHandler                     0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXMC_IRQHandler                          0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI0_IRQHandler                         0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI1_IRQHandler                         0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI2_IRQHandler                         0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI3_IRQHandler                         0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI4_IRQHandler                         0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI5_9_IRQHandler                       0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    FMC_IRQHandler                           0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    FPU_IRQHandler                           0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C0_ER_IRQHandler                       0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C0_EV_IRQHandler                       0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C1_ER_IRQHandler                       0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C1_EV_IRQHandler                       0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C2_ER_IRQHandler                       0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C2_EV_IRQHandler                       0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    IPA_IRQHandler                           0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    LVD_IRQHandler                           0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    RCU_CTC_IRQHandler                       0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    RTC_Alarm_IRQHandler                     0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    RTC_WKUP_IRQHandler                      0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    SDIO_IRQHandler                          0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI0_IRQHandler                          0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI1_IRQHandler                          0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI2_IRQHandler                          0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI3_IRQHandler                          0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI4_IRQHandler                          0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI5_IRQHandler                          0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    TAMPER_STAMP_IRQHandler                  0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_BRK_TIMER8_IRQHandler             0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_Channel_IRQHandler                0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_TRG_CMT_TIMER10_IRQHandler        0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_UP_TIMER9_IRQHandler              0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER1_IRQHandler                        0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER4_IRQHandler                        0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER5_DAC_IRQHandler                    0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_BRK_TIMER11_IRQHandler            0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_Channel_IRQHandler                0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_TRG_CMT_TIMER13_IRQHandler        0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_UP_TIMER12_IRQHandler             0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    TLI_ER_IRQHandler                        0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    TLI_IRQHandler                           0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    TRNG_IRQHandler                          0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART3_IRQHandler                         0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART4_IRQHandler                         0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART6_IRQHandler                         0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART7_IRQHandler                         0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    USART0_IRQHandler                        0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    USART1_IRQHandler                        0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    USART5_IRQHandler                        0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBFS_IRQHandler                         0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBFS_WKUP_IRQHandler                    0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_EP1_In_IRQHandler                  0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_EP1_Out_IRQHandler                 0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_IRQHandler                         0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_WKUP_IRQHandler                    0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    WWDGT_IRQHandler                         0x080001df   Thumb Code     0  startup_gd32f450_470.o(.text)
    __aeabi_memcpy                           0x080001e9   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x080001e9   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x080001e9   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x0800020d   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x0800020d   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x0800020d   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x0800021b   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x0800021b   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x0800021b   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x0800021f   Thumb Code    18  memseta.o(.text)
    strlen                                   0x08000231   Thumb Code    14  strlen.o(.text)
    strcpy                                   0x0800023f   Thumb Code    18  strcpy.o(.text)
    __aeabi_uidiv                            0x08000251   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x08000251   Thumb Code    44  uidiv.o(.text)
    __aeabi_uldivmod                         0x0800027d   Thumb Code    98  uldiv.o(.text)
    __I$use$fp                               0x080002df   Thumb Code     0  iusefp.o(.text)
    __aeabi_dadd                             0x080002df   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x08000421   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x08000427   Thumb Code     6  dadd.o(.text)
    __aeabi_dmul                             0x0800042d   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x08000511   Thumb Code   222  ddiv.o(.text)
    __aeabi_d2ulz                            0x080005ef   Thumb Code    48  dfixul.o(.text)
    __aeabi_cdrcmple                         0x08000621   Thumb Code    48  cdrcmple.o(.text)
    __scatterload                            0x08000651   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08000651   Thumb Code     0  init.o(.text)
    __aeabi_llsl                             0x08000675   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x08000675   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x08000693   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x08000693   Thumb Code     0  llushr.o(.text)
    __aeabi_lasr                             0x080006b3   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x080006b3   Thumb Code     0  llsshr.o(.text)
    _double_round                            0x080006d7   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x080006f5   Thumb Code   156  depilogue.o(.text)
    __decompress                             0x08000791   Thumb Code     0  __dczerorl2.o(.text)
    __decompress1                            0x08000791   Thumb Code    86  __dczerorl2.o(.text)
    API_ACK_20CMD                            0x080007e7   Thumb Code     2  api_lan_data_process .o(i.API_ACK_20CMD)
    API_ACK_21CMD                            0x080007e9   Thumb Code    96  api_lan_data_process .o(i.API_ACK_21CMD)
    API_ACK_22CMD                            0x08000855   Thumb Code   184  api_lan_data_process .o(i.API_ACK_22CMD)
    API_ACK_23CMD                            0x08000939   Thumb Code   218  api_lan_data_process .o(i.API_ACK_23CMD)
    API_ACK_24CMD                            0x08000a25   Thumb Code   154  api_lan_data_process .o(i.API_ACK_24CMD)
    API_ACK_25CMD                            0x08000ad5   Thumb Code     2  api_lan_data_process .o(i.API_ACK_25CMD)
    API_ACK_26CMD                            0x08000ad9   Thumb Code   164  api_lan_data_process .o(i.API_ACK_26CMD)
    API_ACK_27CMD                            0x08000b95   Thumb Code     2  api_lan_data_process .o(i.API_ACK_27CMD)
    API_ACK_28CMD                            0x08000b99   Thumb Code   108  api_lan_data_process .o(i.API_ACK_28CMD)
    API_ACK_29CMD                            0x08000c11   Thumb Code   182  api_lan_data_process .o(i.API_ACK_29CMD)
    API_Chose_TS5A3359_GAIN                  0x08000cd9   Thumb Code    98  gpio.o(i.API_Chose_TS5A3359_GAIN)
    API_ExecuteSeverCMD                      0x08000d41   Thumb Code   260  api_lan_data_process .o(i.API_ExecuteSeverCMD)
    API_Init_LAN                             0x08000e49   Thumb Code    16  api_w5500.o(i.API_Init_LAN)
    API_Init_Net_Parameters                  0x08000e59   Thumb Code   496  api_w5500.o(i.API_Init_Net_Parameters)
    API_LED_GPIO                             0x08001145   Thumb Code    72  gpio.o(i.API_LED_GPIO)
    API_PHY_Check                            0x08001195   Thumb Code   174  api_w5500.o(i.API_PHY_Check)
    API_Printf_Hex                           0x080012c1   Thumb Code    40  usart.o(i.API_Printf_Hex)
    API_Process_Lan_Data                     0x080012f9   Thumb Code   270  api_lan_data_process .o(i.API_Process_Lan_Data)
    API_RNG_Init                             0x080015bd   Thumb Code   266  api_tnrg.o(i.API_RNG_Init)
    API_SPI0_Send_Read_Byte                  0x08001701   Thumb Code    50  api_w5500.o(i.API_SPI0_Send_Read_Byte)
    API_W5500_GPIO_Init                      0x08001739   Thumb Code    36  api_w5500.o(i.API_W5500_GPIO_Init)
    API_W5500_Reset                          0x08001761   Thumb Code   106  api_w5500.o(i.API_W5500_Reset)
    API_W5500_SPI0_Init                      0x080018f1   Thumb Code   148  api_w5500.o(i.API_W5500_SPI0_Init)
    API_Write_SeverInfo                      0x08001991   Thumb Code   244  api_lan_data_process .o(i.API_Write_SeverInfo)
    API_do_tcp_client                        0x08001ad1   Thumb Code   296  usart.o(i.API_do_tcp_client)
    BusFault_Handler                         0x08001d5f   Thumb Code     4  gd32f4xx_it.o(i.BusFault_Handler)
    CRC16_USB                                0x08001d63   Thumb Code   126  my_crc.o(i.CRC16_USB)
    DHCP_run                                 0x08001de1   Thumb Code   532  dhcp.o(i.DHCP_run)
    DMA1_Channel0_IRQHandler                 0x0800209d   Thumb Code     2  gd32f4xx_it.o(i.DMA1_Channel0_IRQHandler)
    DMA1_Channel1_IRQHandler                 0x0800209f   Thumb Code     2  gd32f4xx_it.o(i.DMA1_Channel1_IRQHandler)
    DMA1_Channel2_IRQHandler                 0x080020a1   Thumb Code     2  gd32f4xx_it.o(i.DMA1_Channel2_IRQHandler)
    DRV_SPI_SwapByte                         0x080020a5   Thumb Code    50  spi.o(i.DRV_SPI_SwapByte)
    DebugMon_Handler                         0x080020dd   Thumb Code     2  gd32f4xx_it.o(i.DebugMon_Handler)
    EEPROM_SPI_ReadBuffer                    0x080020e1   Thumb Code    98  eeprom_spi.o(i.EEPROM_SPI_ReadBuffer)
    EEPROM_SPI_SendInstruction               0x0800214d   Thumb Code    46  eeprom_spi.o(i.EEPROM_SPI_SendInstruction)
    EEPROM_SPI_WaitStandbyState              0x08002181   Thumb Code    66  eeprom_spi.o(i.EEPROM_SPI_WaitStandbyState)
    EEPROM_SPI_WriteBuffer                   0x080021cd   Thumb Code   394  eeprom_spi.o(i.EEPROM_SPI_WriteBuffer)
    EEPROM_SPI_WritePage                     0x08002359   Thumb Code   170  eeprom_spi.o(i.EEPROM_SPI_WritePage)
    EEPROM_WriteDisable                      0x0800240d   Thumb Code    38  eeprom_spi.o(i.EEPROM_WriteDisable)
    EEPROM_WriteEnable                       0x0800243d   Thumb Code    38  eeprom_spi.o(i.EEPROM_WriteEnable)
    EXTI10_15_IRQHandler                     0x0800246d   Thumb Code     2  gd32f4xx_it.o(i.EXTI10_15_IRQHandler)
    FLASH_If_Init                            0x08002471   Thumb Code    10  flash.o(i.FLASH_If_Init)
    FML_USART_RecvTask                       0x08002481   Thumb Code   174  usart.o(i.FML_USART_RecvTask)
    GPIO_Init                                0x08002539   Thumb Code    90  gpio.o(i.GPIO_Init)
    HardFault_Handler                        0x080025a1   Thumb Code    10  gd32f4xx_it.o(i.HardFault_Handler)
    IINCHIP_READ                             0x080025b1   Thumb Code    50  w5500_conf.o(i.IINCHIP_READ)
    IINCHIP_SpiSendData                      0x080025e3   Thumb Code    12  w5500_conf.o(i.IINCHIP_SpiSendData)
    IINCHIP_WRITE                            0x080025ef   Thumb Code    50  w5500_conf.o(i.IINCHIP_WRITE)
    Init_GPIO_TS5A339                        0x08002621   Thumb Code    36  gpio.o(i.Init_GPIO_TS5A339)
    MemManage_Handler                        0x080026bd   Thumb Code     4  gd32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x080026c1   Thumb Code     2  gd32f4xx_it.o(i.NMI_Handler)
    PHY_check                                0x080026c5   Thumb Code    76  w5500_conf.o(i.PHY_check)
    PendSV_Handler                           0x08002735   Thumb Code     2  gd32f4xx_it.o(i.PendSV_Handler)
    RTC_Init                                 0x08002739   Thumb Code    96  rtc.o(i.RTC_Init)
    SPI1_Init                                0x08002849   Thumb Code   146  spi.o(i.SPI1_Init)
    SVC_Handler                              0x080028e5   Thumb Code     2  gd32f4xx_it.o(i.SVC_Handler)
    SysReset_Condition                       0x080028e9   Thumb Code    42  user_step.o(i.SysReset_Condition)
    SysTick_Handler                          0x08002921   Thumb Code     8  gd32f4xx_it.o(i.SysTick_Handler)
    SystemInit                               0x08002929   Thumb Code   184  system_gd32f4xx.o(i.SystemInit)
    TIMER1_Init                              0x080029f1   Thumb Code   118  time.o(i.TIMER1_Init)
    TIMER2_IRQHandler                        0x08002a67   Thumb Code     2  gd32f4xx_it.o(i.TIMER2_IRQHandler)
    TIMER3_IRQHandler                        0x08002a69   Thumb Code    20  gd32f4xx_it.o(i.TIMER3_IRQHandler)
    TIMER3_Init                              0x08002a81   Thumb Code    80  time.o(i.TIMER3_Init)
    TIMER6_IRQHandler                        0x08002ad5   Thumb Code    20  gd32f4xx_it.o(i.TIMER6_IRQHandler)
    TIMER6_Init                              0x08002aed   Thumb Code    80  time.o(i.TIMER6_Init)
    TIM_PeriodElapsedCallback                0x08002b41   Thumb Code   374  time.o(i.TIM_PeriodElapsedCallback)
    UART_IDLECallBack                        0x08002cd1   Thumb Code    30  usart.o(i.UART_IDLECallBack)
    UART_RxCpltCallback                      0x08002cf9   Thumb Code    30  usart.o(i.UART_RxCpltCallback)
    USART0_Init                              0x08002d25   Thumb Code   210  usart.o(i.USART0_Init)
    USART2_IRQHandler                        0x08002e5d   Thumb Code    36  gd32f4xx_it.o(i.USART2_IRQHandler)
    USER_EEPROM_RedeByte                     0x08002e8d   Thumb Code    72  25lc080a.o(i.USER_EEPROM_RedeByte)
    USER_EEPROM_WriteByte                    0x08002ed5   Thumb Code   556  25lc080a.o(i.USER_EEPROM_WriteByte)
    UsageFault_Handler                       0x08003101   Thumb Code     4  gd32f4xx_it.o(i.UsageFault_Handler)
    __0printf                                0x08003105   Thumb Code    22  printfa.o(i.__0printf)
    __1printf                                0x08003105   Thumb Code     0  printfa.o(i.__0printf)
    __2printf                                0x08003105   Thumb Code     0  printfa.o(i.__0printf)
    __c89printf                              0x08003105   Thumb Code     0  printfa.o(i.__0printf)
    printf                                   0x08003105   Thumb Code     0  printfa.o(i.__0printf)
    __0sprintf                               0x08003125   Thumb Code    34  printfa.o(i.__0sprintf)
    __1sprintf                               0x08003125   Thumb Code     0  printfa.o(i.__0sprintf)
    __2sprintf                               0x08003125   Thumb Code     0  printfa.o(i.__0sprintf)
    __c89sprintf                             0x08003125   Thumb Code     0  printfa.o(i.__0sprintf)
    sprintf                                  0x08003125   Thumb Code     0  printfa.o(i.__0sprintf)
    __scatterload_copy                       0x08003175   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x08003183   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08003185   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    bsp_CmpCpuFlash                          0x08003a29   Thumb Code    68  flash.o(i.bsp_CmpCpuFlash)
    bsp_EraseCpuFlash                        0x08003a6d   Thumb Code    38  flash.o(i.bsp_EraseCpuFlash)
    bsp_GetSector                            0x08003a95   Thumb Code   454  flash.o(i.bsp_GetSector)
    bsp_WriteCpuFlash                        0x08003cc5   Thumb Code   160  flash.o(i.bsp_WriteCpuFlash)
    check_DHCP_leasedIP                      0x08003d65   Thumb Code    52  dhcp.o(i.check_DHCP_leasedIP)
    check_DHCP_timeout                       0x08003db5   Thumb Code   152  dhcp.o(i.check_DHCP_timeout)
    close                                    0x08003e5d   Thumb Code    52  socket.o(i.close)
    connect                                  0x08003e91   Thumb Code   320  socket.o(i.connect)
    default_ip_assign                        0x0800403d   Thumb Code    22  dhcp.o(i.default_ip_assign)
    default_ip_conflict                      0x08004061   Thumb Code    16  dhcp.o(i.default_ip_conflict)
    default_ip_update                        0x08004075   Thumb Code    20  dhcp.o(i.default_ip_update)
    delay_1ms                                0x0800408d   Thumb Code    16  systick.o(i.delay_1ms)
    delay_decrement                          0x080040a1   Thumb Code    18  systick.o(i.delay_decrement)
    disconnect                               0x080040b9   Thumb Code    38  socket.o(i.disconnect)
    fmc_lock                                 0x080040e1   Thumb Code    14  gd32f4xx_fmc.o(i.fmc_lock)
    fmc_ready_wait                           0x080040f5   Thumb Code    32  gd32f4xx_fmc.o(i.fmc_ready_wait)
    fmc_sector_erase                         0x08004115   Thumb Code    90  gd32f4xx_fmc.o(i.fmc_sector_erase)
    fmc_state_get                            0x08004175   Thumb Code    70  gd32f4xx_fmc.o(i.fmc_state_get)
    fmc_unlock                               0x080041c1   Thumb Code    24  gd32f4xx_fmc.o(i.fmc_unlock)
    fmc_word_program                         0x080041e5   Thumb Code    80  gd32f4xx_fmc.o(i.fmc_word_program)
    fputc                                    0x08004239   Thumb Code    32  usart.o(i.fputc)
    free                                     0x0800425d   Thumb Code    76  malloc.o(i.free)
    getIINCHIP_TxMAX                         0x080042ad   Thumb Code    10  w5500.o(i.getIINCHIP_TxMAX)
    getPHYStatus                             0x080042bd   Thumb Code    12  w5500_conf.o(i.getPHYStatus)
    getSHAR                                  0x080042c9   Thumb Code    18  w5500.o(i.getSHAR)
    getSn_IR                                 0x080042db   Thumb Code    18  w5500.o(i.getSn_IR)
    getSn_RX_RSR                             0x080042ed   Thumb Code    84  w5500.o(i.getSn_RX_RSR)
    getSn_SR                                 0x08004341   Thumb Code    18  w5500.o(i.getSn_SR)
    getSn_TX_FSR                             0x08004353   Thumb Code    84  w5500.o(i.getSn_TX_FSR)
    get_hard_rand_data                       0x080043a7   Thumb Code    20  api_tnrg.o(i.get_hard_rand_data)
    gpio_af_set                              0x080043bb   Thumb Code    94  gd32f4xx_gpio.o(i.gpio_af_set)
    gpio_bit_reset                           0x08004419   Thumb Code     4  gd32f4xx_gpio.o(i.gpio_bit_reset)
    gpio_bit_set                             0x0800441d   Thumb Code     4  gd32f4xx_gpio.o(i.gpio_bit_set)
    gpio_mode_set                            0x08004421   Thumb Code    78  gd32f4xx_gpio.o(i.gpio_mode_set)
    gpio_output_options_set                  0x0800446f   Thumb Code    66  gd32f4xx_gpio.o(i.gpio_output_options_set)
    iinchip_csoff                            0x080044b1   Thumb Code    14  w5500_conf.o(i.iinchip_csoff)
    iinchip_cson                             0x080044c5   Thumb Code    14  w5500_conf.o(i.iinchip_cson)
    init_dhcp_client                         0x080044d9   Thumb Code    40  dhcp.o(i.init_dhcp_client)
    main                                     0x0800451d   Thumb Code   252  main.o(i.main)
    makeDHCPMSG                              0x080047d1   Thumb Code   380  dhcp.o(i.makeDHCPMSG)
    malloc                                   0x08004959   Thumb Code    92  malloc.o(i.malloc)
    nvic_irq_enable                          0x080049c5   Thumb Code   186  gd32f4xx_misc.o(i.nvic_irq_enable)
    nvic_priority_group_set                  0x08004a89   Thumb Code    10  gd32f4xx_misc.o(i.nvic_priority_group_set)
    nvic_vector_table_set                    0x08004a9d   Thumb Code    16  gd32f4xx_misc.o(i.nvic_vector_table_set)
    parseDHCPMSG                             0x08004ab5   Thumb Code   458  dhcp.o(i.parseDHCPMSG)
    pmu_backup_write_enable                  0x08004c99   Thumb Code    14  gd32f4xx_pmu.o(i.pmu_backup_write_enable)
    print_system_status                      0x08004cad   Thumb Code   160  main.o(i.print_system_status)
    rcu_clock_freq_get                       0x08004e81   Thumb Code   264  gd32f4xx_rcu.o(i.rcu_clock_freq_get)
    rcu_flag_get                             0x08004fa5   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_flag_get)
    rcu_osci_on                              0x08004fc9   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_osci_on)
    rcu_osci_stab_wait                       0x08004fed   Thumb Code   342  gd32f4xx_rcu.o(i.rcu_osci_stab_wait)
    rcu_periph_clock_enable                  0x08005149   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_periph_clock_enable)
    rcu_periph_reset_disable                 0x0800516d   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_periph_reset_disable)
    rcu_periph_reset_enable                  0x08005191   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_periph_reset_enable)
    rcu_rtc_clock_config                     0x080051b5   Thumb Code    18  gd32f4xx_rcu.o(i.rcu_rtc_clock_config)
    recv                                     0x080051cd   Thumb Code    66  socket.o(i.recv)
    recv_data_processing                     0x08005211   Thumb Code   116  w5500.o(i.recv_data_processing)
    recvfrom                                 0x080052a5   Thumb Code   486  socket.o(i.recvfrom)
    reset_DHCP_timeout                       0x080054a5   Thumb Code    20  dhcp.o(i.reset_DHCP_timeout)
    reset_w5500                              0x080054c5   Thumb Code    46  w5500_conf.o(i.reset_w5500)
    rtc_init                                 0x08005509   Thumb Code   190  gd32f4xx_rtc.o(i.rtc_init)
    rtc_init_mode_enter                      0x080055cd   Thumb Code    66  gd32f4xx_rtc.o(i.rtc_init_mode_enter)
    rtc_init_mode_exit                       0x08005615   Thumb Code    14  gd32f4xx_rtc.o(i.rtc_init_mode_exit)
    rtc_register_sync_wait                   0x08005629   Thumb Code    92  gd32f4xx_rtc.o(i.rtc_register_sync_wait)
    send                                     0x08005689   Thumb Code   252  socket.o(i.send)
    send_DHCP_DECLINE                        0x080057c5   Thumb Code   476  dhcp.o(i.send_DHCP_DECLINE)
    send_DHCP_DISCOVER                       0x080059ad   Thumb Code   462  dhcp.o(i.send_DHCP_DISCOVER)
    send_DHCP_REQUEST                        0x08005ba1   Thumb Code   838  dhcp.o(i.send_DHCP_REQUEST)
    send_data_processing                     0x08005f15   Thumb Code   116  w5500.o(i.send_data_processing)
    sendto                                   0x08005fa9   Thumb Code   268  socket.o(i.sendto)
    setGAR                                   0x080060b5   Thumb Code    16  w5500.o(i.setGAR)
    setMR                                    0x080060c5   Thumb Code    14  w5500.o(i.setMR)
    setSHAR                                  0x080060d3   Thumb Code    18  w5500.o(i.setSHAR)
    setSIPR                                  0x080060e5   Thumb Code    18  w5500.o(i.setSIPR)
    setSUBR                                  0x080060f7   Thumb Code    18  w5500.o(i.setSUBR)
    setSn_IR                                 0x08006109   Thumb Code    22  w5500.o(i.setSn_IR)
    set_w5500_mac                            0x08006121   Thumb Code    48  w5500_conf.o(i.set_w5500_mac)
    socket                                   0x08006189   Thumb Code   192  socket.o(i.socket)
    socket_buf_init                          0x0800624d   Thumb Code   128  w5500.o(i.socket_buf_init)
    spi_enable                               0x080062d5   Thumb Code    10  gd32f4xx_spi.o(i.spi_enable)
    spi_i2s_data_receive                     0x080062df   Thumb Code     8  gd32f4xx_spi.o(i.spi_i2s_data_receive)
    spi_i2s_data_transmit                    0x080062e7   Thumb Code     4  gd32f4xx_spi.o(i.spi_i2s_data_transmit)
    spi_i2s_flag_get                         0x080062eb   Thumb Code    16  gd32f4xx_spi.o(i.spi_i2s_flag_get)
    spi_init                                 0x080062fb   Thumb Code    50  gd32f4xx_spi.o(i.spi_init)
    systick_config                           0x08006431   Thumb Code    74  systick.o(i.systick_config)
    timer_channel_output_config              0x08006481   Thumb Code   484  gd32f4xx_timer.o(i.timer_channel_output_config)
    timer_channel_output_mode_config         0x0800666d   Thumb Code    90  gd32f4xx_timer.o(i.timer_channel_output_mode_config)
    timer_channel_output_pulse_value_config  0x080066c7   Thumb Code    38  gd32f4xx_timer.o(i.timer_channel_output_pulse_value_config)
    timer_channel_output_shadow_config       0x080066ed   Thumb Code    90  gd32f4xx_timer.o(i.timer_channel_output_shadow_config)
    timer_deinit                             0x08006749   Thumb Code   374  gd32f4xx_timer.o(i.timer_deinit)
    timer_enable                             0x080068cd   Thumb Code    10  gd32f4xx_timer.o(i.timer_enable)
    timer_init                               0x080068d9   Thumb Code   122  gd32f4xx_timer.o(i.timer_init)
    timer_interrupt_enable                   0x08006971   Thumb Code     8  gd32f4xx_timer.o(i.timer_interrupt_enable)
    timer_interrupt_flag_clear               0x08006979   Thumb Code     6  gd32f4xx_timer.o(i.timer_interrupt_flag_clear)
    timer_interrupt_flag_get                 0x0800697f   Thumb Code    24  gd32f4xx_timer.o(i.timer_interrupt_flag_get)
    trng_deinit                              0x08006997   Thumb Code    20  gd32f4xx_trng.o(i.trng_deinit)
    trng_enable                              0x080069ad   Thumb Code    14  gd32f4xx_trng.o(i.trng_enable)
    trng_flag_get                            0x080069c1   Thumb Code    18  gd32f4xx_trng.o(i.trng_flag_get)
    trng_get_true_random_data                0x080069d9   Thumb Code     6  gd32f4xx_trng.o(i.trng_get_true_random_data)
    usart_baudrate_set                       0x080069e5   Thumb Code   224  gd32f4xx_usart.o(i.usart_baudrate_set)
    usart_data_receive                       0x08006acd   Thumb Code    10  gd32f4xx_usart.o(i.usart_data_receive)
    usart_data_transmit                      0x08006ad7   Thumb Code     8  gd32f4xx_usart.o(i.usart_data_transmit)
    usart_deinit                             0x08006ae1   Thumb Code   210  gd32f4xx_usart.o(i.usart_deinit)
    usart_enable                             0x08006bbd   Thumb Code    10  gd32f4xx_usart.o(i.usart_enable)
    usart_flag_get                           0x08006bc7   Thumb Code    30  gd32f4xx_usart.o(i.usart_flag_get)
    usart_hardware_flow_cts_config           0x08006be5   Thumb Code    16  gd32f4xx_usart.o(i.usart_hardware_flow_cts_config)
    usart_hardware_flow_rts_config           0x08006bf5   Thumb Code    16  gd32f4xx_usart.o(i.usart_hardware_flow_rts_config)
    usart_interrupt_flag_clear               0x08006c05   Thumb Code    26  gd32f4xx_usart.o(i.usart_interrupt_flag_clear)
    usart_interrupt_flag_get                 0x08006c1f   Thumb Code    56  gd32f4xx_usart.o(i.usart_interrupt_flag_get)
    usart_parity_config                      0x08006c57   Thumb Code    16  gd32f4xx_usart.o(i.usart_parity_config)
    usart_receive_config                     0x08006c67   Thumb Code    16  gd32f4xx_usart.o(i.usart_receive_config)
    usart_stop_bit_set                       0x08006c77   Thumb Code    16  gd32f4xx_usart.o(i.usart_stop_bit_set)
    usart_transmit_config                    0x08006c87   Thumb Code    16  gd32f4xx_usart.o(i.usart_transmit_config)
    usart_word_length_set                    0x08006c97   Thumb Code    16  gd32f4xx_usart.o(i.usart_word_length_set)
    wiz_read_buf                             0x08006ca9   Thumb Code    78  w5500_conf.o(i.wiz_read_buf)
    wiz_write_buf                            0x08006d11   Thumb Code    80  w5500_conf.o(i.wiz_write_buf)
    Region$$Table$$Base                      0x08006d78   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08006da8   Number         0  anon$$obj.o(Region$$Table)
    g_JumpInit                               0x20000000   Data           4  user_step.o(.NoInit)
    SystemCoreClock                          0x20000004   Data           4  system_gd32f4xx.o(.data)
    Soft_Ver                                 0x20000008   Data           3  main.o(.data)
    HardWare_Ver                             0x2000000b   Data           2  main.o(.data)
    dhcp_ok                                  0x2000000d   Data           1  main.o(.data)
    tim6_msTic                               0x20000018   Data           4  time.o(.data)
    TempTestTimer                            0x2000001c   Data           2  time.o(.data)
    txsize                                   0x20000023   Data           8  w5500.o(.data)
    rxsize                                   0x2000002b   Data           8  w5500.o(.data)
    dhcp_tick_next                           0x20000034   Data           4  dhcp.o(.data)
    OLD_allocated_ip                         0x20000038   Data           4  dhcp.o(.data)
    DHCP_allocated_ip                        0x2000003c   Data           4  dhcp.o(.data)
    DHCP_allocated_gw                        0x20000040   Data           4  dhcp.o(.data)
    DHCP_allocated_sn                        0x20000044   Data           4  dhcp.o(.data)
    DHCP_allocated_dns                       0x20000048   Data           4  dhcp.o(.data)
    HOST_NAME                                0x2000004c   Data           8  dhcp.o(.data)
    dhcp_state                               0x20000054   Data           1  dhcp.o(.data)
    dhcp_retry_count                         0x20000055   Data           1  dhcp.o(.data)
    DHCP_timeout                             0x20000056   Data           1  dhcp.o(.data)
    dhcp_lease_time                          0x20000058   Data           4  dhcp.o(.data)
    dhcp_time                                0x2000005c   Data           4  dhcp.o(.data)
    next_dhcp_time                           0x20000060   Data           4  dhcp.o(.data)
    dhcp_tick_cnt                            0x20000064   Data           4  dhcp.o(.data)
    DHCP_timer                               0x20000068   Data           1  dhcp.o(.data)
    Conflict_flag                            0x20000069   Data           1  dhcp.o(.data)
    DHCP_XID                                 0x2000006c   Data           4  dhcp.o(.data)
    pDHCPMSG                                 0x20000070   Data           4  dhcp.o(.data)
    dhcp_ip_assign                           0x20000074   Data           4  dhcp.o(.data)
    dhcp_ip_update                           0x20000078   Data           4  dhcp.o(.data)
    dhcp_ip_conflict                         0x2000007c   Data           4  dhcp.o(.data)
    __stdout                                 0x20000080   Data           4  stdout.o(.data)
    __microlib_freelist                      0x20000084   Data           4  mvars.o(.data)
    __microlib_freelist_initialised          0x20000088   Data           4  mvars.o(.data)
    rtc_initpara                             0x2000008c   Data          20  rtc.o(.bss)
    g_tTimeSign                              0x200000a0   Data           9  time.o(.bss)
    my_key                                   0x200000aa   Data          10  time.o(.bss)
    SSIZE                                    0x200000b4   Data          16  w5500.o(.bss)
    RSIZE                                    0x200000c4   Data          16  w5500.o(.bss)
    EXTERN_DHCPBUF                           0x200000d4   Data        1024  dhcp.o(.bss)
    __heap_base                              0x200004d8   Data           0  startup_gd32f450_470.o(HEAP)
    __heap_limit                             0x200034d8   Data           0  startup_gd32f450_470.o(HEAP)
    __initial_sp                             0x200084d8   Data           0  startup_gd32f450_470.o(STACK)
    sg_tUsartDriveHandle                     0x20020000   Data        2068  usart.o(.RAM_D3)
    sg_arrUasrt2RecvBuf                      0x20020814   Data        2049  usart.o(.RAM_D3)
    tmpBuf_test                              0x20021015   Data        2049  usart.o(.RAM_D3)
    usart2_buf                               0x20021816   Data           1  usart.o(.RAM_D3)
    Lan_Para                                 0x20021818   Data        2120  api_w5500.o(.RAM_D3)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080001ad

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00008e90, Max: 0x00200000, ABSOLUTE, COMPRESSED[0x00006e74])

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00006da8, Max: 0x00200000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000001ac   Data   RO            3    RESET               startup_gd32f450_470.o
    0x080001ac   0x080001ac   0x00000000   Code   RO         7831  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x080001ac   0x080001ac   0x00000004   Code   RO         8147    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x080001b0   0x080001b0   0x00000004   Code   RO         8150    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x080001b4   0x080001b4   0x00000000   Code   RO         8152    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x080001b4   0x080001b4   0x00000000   Code   RO         8154    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x080001b4   0x080001b4   0x00000008   Code   RO         8155    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x080001bc   0x080001bc   0x00000004   Code   RO         8162    .ARM.Collect$$$$0000000E  mc_w.l(entry12b.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO         8157    .ARM.Collect$$$$0000000F  mc_w.l(entry10a.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO         8159    .ARM.Collect$$$$00000011  mc_w.l(entry11a.o)
    0x080001c0   0x080001c0   0x00000004   Code   RO         8148    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x080001c4   0x080001c4   0x00000024   Code   RO            4    .text               startup_gd32f450_470.o
    0x080001e8   0x080001e8   0x00000024   Code   RO         7834    .text               mc_w.l(memcpya.o)
    0x0800020c   0x0800020c   0x00000024   Code   RO         7836    .text               mc_w.l(memseta.o)
    0x08000230   0x08000230   0x0000000e   Code   RO         7844    .text               mc_w.l(strlen.o)
    0x0800023e   0x0800023e   0x00000012   Code   RO         7846    .text               mc_w.l(strcpy.o)
    0x08000250   0x08000250   0x0000002c   Code   RO         8164    .text               mc_w.l(uidiv.o)
    0x0800027c   0x0800027c   0x00000062   Code   RO         8166    .text               mc_w.l(uldiv.o)
    0x080002de   0x080002de   0x00000000   Code   RO         8179    .text               mc_w.l(iusefp.o)
    0x080002de   0x080002de   0x0000014e   Code   RO         8180    .text               mf_w.l(dadd.o)
    0x0800042c   0x0800042c   0x000000e4   Code   RO         8182    .text               mf_w.l(dmul.o)
    0x08000510   0x08000510   0x000000de   Code   RO         8184    .text               mf_w.l(ddiv.o)
    0x080005ee   0x080005ee   0x00000030   Code   RO         8186    .text               mf_w.l(dfixul.o)
    0x0800061e   0x0800061e   0x00000002   PAD
    0x08000620   0x08000620   0x00000030   Code   RO         8188    .text               mf_w.l(cdrcmple.o)
    0x08000650   0x08000650   0x00000024   Code   RO         8190    .text               mc_w.l(init.o)
    0x08000674   0x08000674   0x0000001e   Code   RO         8192    .text               mc_w.l(llshl.o)
    0x08000692   0x08000692   0x00000020   Code   RO         8194    .text               mc_w.l(llushr.o)
    0x080006b2   0x080006b2   0x00000024   Code   RO         8196    .text               mc_w.l(llsshr.o)
    0x080006d6   0x080006d6   0x000000ba   Code   RO         8228    .text               mf_w.l(depilogue.o)
    0x08000790   0x08000790   0x00000056   Code   RO         8240    .text               mc_w.l(__dczerorl2.o)
    0x080007e6   0x080007e6   0x00000002   Code   RO         6813    i.API_ACK_20CMD     api_lan_data_process .o
    0x080007e8   0x080007e8   0x0000006c   Code   RO         6814    i.API_ACK_21CMD     api_lan_data_process .o
    0x08000854   0x08000854   0x000000e4   Code   RO         6815    i.API_ACK_22CMD     api_lan_data_process .o
    0x08000938   0x08000938   0x000000ec   Code   RO         6816    i.API_ACK_23CMD     api_lan_data_process .o
    0x08000a24   0x08000a24   0x000000b0   Code   RO         6817    i.API_ACK_24CMD     api_lan_data_process .o
    0x08000ad4   0x08000ad4   0x00000002   Code   RO         6818    i.API_ACK_25CMD     api_lan_data_process .o
    0x08000ad6   0x08000ad6   0x00000002   PAD
    0x08000ad8   0x08000ad8   0x000000bc   Code   RO         6819    i.API_ACK_26CMD     api_lan_data_process .o
    0x08000b94   0x08000b94   0x00000002   Code   RO         6820    i.API_ACK_27CMD     api_lan_data_process .o
    0x08000b96   0x08000b96   0x00000002   PAD
    0x08000b98   0x08000b98   0x00000078   Code   RO         6821    i.API_ACK_28CMD     api_lan_data_process .o
    0x08000c10   0x08000c10   0x000000c8   Code   RO         6822    i.API_ACK_29CMD     api_lan_data_process .o
    0x08000cd8   0x08000cd8   0x00000068   Code   RO         6344    i.API_Chose_TS5A3359_GAIN  gpio.o
    0x08000d40   0x08000d40   0x00000108   Code   RO         6825    i.API_ExecuteSeverCMD  api_lan_data_process .o
    0x08000e48   0x08000e48   0x00000010   Code   RO         6989    i.API_Init_LAN      api_w5500.o
    0x08000e58   0x08000e58   0x000002ec   Code   RO         6990    i.API_Init_Net_Parameters  api_w5500.o
    0x08001144   0x08001144   0x00000050   Code   RO         6345    i.API_LED_GPIO      gpio.o
    0x08001194   0x08001194   0x0000012c   Code   RO         6991    i.API_PHY_Check     api_w5500.o
    0x080012c0   0x080012c0   0x00000038   Code   RO         6582    i.API_Printf_Hex    usart.o
    0x080012f8   0x080012f8   0x000002c4   Code   RO         6829    i.API_Process_Lan_Data  api_lan_data_process .o
    0x080015bc   0x080015bc   0x00000144   Code   RO         6951    i.API_RNG_Init      api_tnrg.o
    0x08001700   0x08001700   0x00000038   Code   RO         6992    i.API_SPI0_Send_Read_Byte  api_w5500.o
    0x08001738   0x08001738   0x00000028   Code   RO         6993    i.API_W5500_GPIO_Init  api_w5500.o
    0x08001760   0x08001760   0x00000190   Code   RO         6994    i.API_W5500_Reset   api_w5500.o
    0x080018f0   0x080018f0   0x000000a0   Code   RO         6995    i.API_W5500_SPI0_Init  api_w5500.o
    0x08001990   0x08001990   0x00000140   Code   RO         6830    i.API_Write_SeverInfo  api_lan_data_process .o
    0x08001ad0   0x08001ad0   0x00000220   Code   RO         6583    i.API_do_tcp_client  usart.o
    0x08001cf0   0x08001cf0   0x0000006e   Code   RO         6584    i.AddByteToBuffer   usart.o
    0x08001d5e   0x08001d5e   0x00000004   Code   RO         5781    i.BusFault_Handler  gd32f4xx_it.o
    0x08001d62   0x08001d62   0x0000007e   Code   RO         6389    i.CRC16_USB         my_crc.o
    0x08001de0   0x08001de0   0x000002bc   Code   RO         7681    i.DHCP_run          dhcp.o
    0x0800209c   0x0800209c   0x00000002   Code   RO         5782    i.DMA1_Channel0_IRQHandler  gd32f4xx_it.o
    0x0800209e   0x0800209e   0x00000002   Code   RO         5783    i.DMA1_Channel1_IRQHandler  gd32f4xx_it.o
    0x080020a0   0x080020a0   0x00000002   Code   RO         5784    i.DMA1_Channel2_IRQHandler  gd32f4xx_it.o
    0x080020a2   0x080020a2   0x00000002   PAD
    0x080020a4   0x080020a4   0x00000038   Code   RO         6465    i.DRV_SPI_SwapByte  spi.o
    0x080020dc   0x080020dc   0x00000002   Code   RO         5785    i.DebugMon_Handler  gd32f4xx_it.o
    0x080020de   0x080020de   0x00000002   PAD
    0x080020e0   0x080020e0   0x0000006c   Code   RO         6194    i.EEPROM_SPI_ReadBuffer  eeprom_spi.o
    0x0800214c   0x0800214c   0x00000034   Code   RO         6195    i.EEPROM_SPI_SendInstruction  eeprom_spi.o
    0x08002180   0x08002180   0x0000004c   Code   RO         6196    i.EEPROM_SPI_WaitStandbyState  eeprom_spi.o
    0x080021cc   0x080021cc   0x0000018a   Code   RO         6197    i.EEPROM_SPI_WriteBuffer  eeprom_spi.o
    0x08002356   0x08002356   0x00000002   PAD
    0x08002358   0x08002358   0x000000b4   Code   RO         6198    i.EEPROM_SPI_WritePage  eeprom_spi.o
    0x0800240c   0x0800240c   0x00000030   Code   RO         6200    i.EEPROM_WriteDisable  eeprom_spi.o
    0x0800243c   0x0800243c   0x00000030   Code   RO         6201    i.EEPROM_WriteEnable  eeprom_spi.o
    0x0800246c   0x0800246c   0x00000002   Code   RO         5786    i.EXTI10_15_IRQHandler  gd32f4xx_it.o
    0x0800246e   0x0800246e   0x00000002   PAD
    0x08002470   0x08002470   0x00000010   Code   RO         6274    i.FLASH_If_Init     flash.o
    0x08002480   0x08002480   0x000000b8   Code   RO         6586    i.FML_USART_RecvTask  usart.o
    0x08002538   0x08002538   0x00000068   Code   RO         6346    i.GPIO_Init         gpio.o
    0x080025a0   0x080025a0   0x00000010   Code   RO         5787    i.HardFault_Handler  gd32f4xx_it.o
    0x080025b0   0x080025b0   0x00000032   Code   RO         7529    i.IINCHIP_READ      w5500_conf.o
    0x080025e2   0x080025e2   0x0000000c   Code   RO         7530    i.IINCHIP_SpiSendData  w5500_conf.o
    0x080025ee   0x080025ee   0x00000032   Code   RO         7531    i.IINCHIP_WRITE     w5500_conf.o
    0x08002620   0x08002620   0x00000028   Code   RO         6347    i.Init_GPIO_TS5A339  gpio.o
    0x08002648   0x08002648   0x0000003a   Code   RO         6390    i.InvertUint16      my_crc.o
    0x08002682   0x08002682   0x0000003a   Code   RO         6391    i.InvertUint8       my_crc.o
    0x080026bc   0x080026bc   0x00000004   Code   RO         5788    i.MemManage_Handler  gd32f4xx_it.o
    0x080026c0   0x080026c0   0x00000002   Code   RO         5789    i.NMI_Handler       gd32f4xx_it.o
    0x080026c2   0x080026c2   0x00000002   PAD
    0x080026c4   0x080026c4   0x00000070   Code   RO         7532    i.PHY_check         w5500_conf.o
    0x08002734   0x08002734   0x00000002   Code   RO         5790    i.PendSV_Handler    gd32f4xx_it.o
    0x08002736   0x08002736   0x00000002   PAD
    0x08002738   0x08002738   0x00000064   Code   RO         6423    i.RTC_Init          rtc.o
    0x0800279c   0x0800279c   0x0000008e   Code   RO         6589    i.ReadBytesToBuffer  usart.o
    0x0800282a   0x0800282a   0x0000001e   Code   RO         6590    i.RecvDataHandler   usart.o
    0x08002848   0x08002848   0x0000009c   Code   RO         6466    i.SPI1_Init         spi.o
    0x080028e4   0x080028e4   0x00000002   Code   RO         5791    i.SVC_Handler       gd32f4xx_it.o
    0x080028e6   0x080028e6   0x00000002   PAD
    0x080028e8   0x080028e8   0x00000038   Code   RO         6699    i.SysReset_Condition  user_step.o
    0x08002920   0x08002920   0x00000008   Code   RO         5792    i.SysTick_Handler   gd32f4xx_it.o
    0x08002928   0x08002928   0x000000c8   Code   RO         5736    i.SystemInit        system_gd32f4xx.o
    0x080029f0   0x080029f0   0x00000076   Code   RO         6495    i.TIMER1_Init       time.o
    0x08002a66   0x08002a66   0x00000002   Code   RO         5793    i.TIMER2_IRQHandler  gd32f4xx_it.o
    0x08002a68   0x08002a68   0x00000018   Code   RO         5794    i.TIMER3_IRQHandler  gd32f4xx_it.o
    0x08002a80   0x08002a80   0x00000054   Code   RO         6496    i.TIMER3_Init       time.o
    0x08002ad4   0x08002ad4   0x00000018   Code   RO         5795    i.TIMER6_IRQHandler  gd32f4xx_it.o
    0x08002aec   0x08002aec   0x00000054   Code   RO         6497    i.TIMER6_Init       time.o
    0x08002b40   0x08002b40   0x00000190   Code   RO         6498    i.TIM_PeriodElapsedCallback  time.o
    0x08002cd0   0x08002cd0   0x00000028   Code   RO         6591    i.UART_IDLECallBack  usart.o
    0x08002cf8   0x08002cf8   0x0000002c   Code   RO         6592    i.UART_RxCpltCallback  usart.o
    0x08002d24   0x08002d24   0x00000138   Code   RO         6594    i.USART0_Init       usart.o
    0x08002e5c   0x08002e5c   0x00000030   Code   RO         5796    i.USART2_IRQHandler  gd32f4xx_it.o
    0x08002e8c   0x08002e8c   0x00000048   Code   RO         6128    i.USER_EEPROM_RedeByte  25lc080a.o
    0x08002ed4   0x08002ed4   0x0000022c   Code   RO         6130    i.USER_EEPROM_WriteByte  25lc080a.o
    0x08003100   0x08003100   0x00000004   Code   RO         5797    i.UsageFault_Handler  gd32f4xx_it.o
    0x08003104   0x08003104   0x00000020   Code   RO         8089    i.__0printf         mc_w.l(printfa.o)
    0x08003124   0x08003124   0x00000028   Code   RO         8091    i.__0sprintf        mc_w.l(printfa.o)
    0x0800314c   0x0800314c   0x00000028   Code   RO         6080    i.__NVIC_SetPriority  systick.o
    0x08003174   0x08003174   0x0000000e   Code   RO         8234    i.__scatterload_copy  mc_w.l(handlers.o)
    0x08003182   0x08003182   0x00000002   Code   RO         8235    i.__scatterload_null  mc_w.l(handlers.o)
    0x08003184   0x08003184   0x0000000e   Code   RO         8236    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x08003192   0x08003192   0x00000002   PAD
    0x08003194   0x08003194   0x00000184   Code   RO         8096    i._fp_digits        mc_w.l(printfa.o)
    0x08003318   0x08003318   0x000006b4   Code   RO         8097    i._printf_core      mc_w.l(printfa.o)
    0x080039cc   0x080039cc   0x00000024   Code   RO         8098    i._printf_post_padding  mc_w.l(printfa.o)
    0x080039f0   0x080039f0   0x0000002e   Code   RO         8099    i._printf_pre_padding  mc_w.l(printfa.o)
    0x08003a1e   0x08003a1e   0x0000000a   Code   RO         8101    i._sputc            mc_w.l(printfa.o)
    0x08003a28   0x08003a28   0x00000044   Code   RO         6275    i.bsp_CmpCpuFlash   flash.o
    0x08003a6c   0x08003a6c   0x00000026   Code   RO         6276    i.bsp_EraseCpuFlash  flash.o
    0x08003a92   0x08003a92   0x00000002   PAD
    0x08003a94   0x08003a94   0x00000230   Code   RO         6277    i.bsp_GetSector     flash.o
    0x08003cc4   0x08003cc4   0x000000a0   Code   RO         6280    i.bsp_WriteCpuFlash  flash.o
    0x08003d64   0x08003d64   0x00000050   Code   RO         7684    i.check_DHCP_leasedIP  dhcp.o
    0x08003db4   0x08003db4   0x000000a8   Code   RO         7685    i.check_DHCP_timeout  dhcp.o
    0x08003e5c   0x08003e5c   0x00000034   Code   RO         7058    i.close             socket.o
    0x08003e90   0x08003e90   0x000001ac   Code   RO         7059    i.connect           socket.o
    0x0800403c   0x0800403c   0x00000024   Code   RO         7686    i.default_ip_assign  dhcp.o
    0x08004060   0x08004060   0x00000014   Code   RO         7687    i.default_ip_conflict  dhcp.o
    0x08004074   0x08004074   0x00000018   Code   RO         7688    i.default_ip_update  dhcp.o
    0x0800408c   0x0800408c   0x00000014   Code   RO         6081    i.delay_1ms         systick.o
    0x080040a0   0x080040a0   0x00000018   Code   RO         6082    i.delay_decrement   systick.o
    0x080040b8   0x080040b8   0x00000026   Code   RO         7060    i.disconnect        socket.o
    0x080040de   0x080040de   0x00000002   PAD
    0x080040e0   0x080040e0   0x00000014   Code   RO         2366    i.fmc_lock          gd32f4xx_fmc.o
    0x080040f4   0x080040f4   0x00000020   Code   RO         2369    i.fmc_ready_wait    gd32f4xx_fmc.o
    0x08004114   0x08004114   0x00000060   Code   RO         2370    i.fmc_sector_erase  gd32f4xx_fmc.o
    0x08004174   0x08004174   0x0000004c   Code   RO         2371    i.fmc_state_get     gd32f4xx_fmc.o
    0x080041c0   0x080041c0   0x00000024   Code   RO         2372    i.fmc_unlock        gd32f4xx_fmc.o
    0x080041e4   0x080041e4   0x00000054   Code   RO         2373    i.fmc_word_program  gd32f4xx_fmc.o
    0x08004238   0x08004238   0x00000024   Code   RO         6596    i.fputc             usart.o
    0x0800425c   0x0800425c   0x00000050   Code   RO         8117    i.free              mc_w.l(malloc.o)
    0x080042ac   0x080042ac   0x00000010   Code   RO         7308    i.getIINCHIP_TxMAX  w5500.o
    0x080042bc   0x080042bc   0x0000000c   Code   RO         7534    i.getPHYStatus      w5500_conf.o
    0x080042c8   0x080042c8   0x00000012   Code   RO         7310    i.getSHAR           w5500.o
    0x080042da   0x080042da   0x00000012   Code   RO         7313    i.getSn_IR          w5500.o
    0x080042ec   0x080042ec   0x00000054   Code   RO         7314    i.getSn_RX_RSR      w5500.o
    0x08004340   0x08004340   0x00000012   Code   RO         7315    i.getSn_SR          w5500.o
    0x08004352   0x08004352   0x00000054   Code   RO         7316    i.getSn_TX_FSR      w5500.o
    0x080043a6   0x080043a6   0x00000014   Code   RO         6952    i.get_hard_rand_data  api_tnrg.o
    0x080043ba   0x080043ba   0x0000005e   Code   RO         2656    i.gpio_af_set       gd32f4xx_gpio.o
    0x08004418   0x08004418   0x00000004   Code   RO         2657    i.gpio_bit_reset    gd32f4xx_gpio.o
    0x0800441c   0x0800441c   0x00000004   Code   RO         2658    i.gpio_bit_set      gd32f4xx_gpio.o
    0x08004420   0x08004420   0x0000004e   Code   RO         2664    i.gpio_mode_set     gd32f4xx_gpio.o
    0x0800446e   0x0800446e   0x00000042   Code   RO         2666    i.gpio_output_options_set  gd32f4xx_gpio.o
    0x080044b0   0x080044b0   0x00000014   Code   RO         7535    i.iinchip_csoff     w5500_conf.o
    0x080044c4   0x080044c4   0x00000014   Code   RO         7536    i.iinchip_cson      w5500_conf.o
    0x080044d8   0x080044d8   0x00000044   Code   RO         7689    i.init_dhcp_client  dhcp.o
    0x0800451c   0x0800451c   0x000002b4   Code   RO         6007    i.main              main.o
    0x080047d0   0x080047d0   0x00000188   Code   RO         7690    i.makeDHCPMSG       dhcp.o
    0x08004958   0x08004958   0x0000006c   Code   RO         8118    i.malloc            mc_w.l(malloc.o)
    0x080049c4   0x080049c4   0x000000c4   Code   RO         3245    i.nvic_irq_enable   gd32f4xx_misc.o
    0x08004a88   0x08004a88   0x00000014   Code   RO         3246    i.nvic_priority_group_set  gd32f4xx_misc.o
    0x08004a9c   0x08004a9c   0x00000018   Code   RO         3247    i.nvic_vector_table_set  gd32f4xx_misc.o
    0x08004ab4   0x08004ab4   0x000001e4   Code   RO         7691    i.parseDHCPMSG      dhcp.o
    0x08004c98   0x08004c98   0x00000014   Code   RO         3306    i.pmu_backup_write_enable  gd32f4xx_pmu.o
    0x08004cac   0x08004cac   0x000001d4   Code   RO         6008    i.print_system_status  main.o
    0x08004e80   0x08004e80   0x00000124   Code   RO         3458    i.rcu_clock_freq_get  gd32f4xx_rcu.o
    0x08004fa4   0x08004fa4   0x00000024   Code   RO         3461    i.rcu_flag_get      gd32f4xx_rcu.o
    0x08004fc8   0x08004fc8   0x00000024   Code   RO         3474    i.rcu_osci_on       gd32f4xx_rcu.o
    0x08004fec   0x08004fec   0x0000015c   Code   RO         3475    i.rcu_osci_stab_wait  gd32f4xx_rcu.o
    0x08005148   0x08005148   0x00000024   Code   RO         3477    i.rcu_periph_clock_enable  gd32f4xx_rcu.o
    0x0800516c   0x0800516c   0x00000024   Code   RO         3480    i.rcu_periph_reset_disable  gd32f4xx_rcu.o
    0x08005190   0x08005190   0x00000024   Code   RO         3481    i.rcu_periph_reset_enable  gd32f4xx_rcu.o
    0x080051b4   0x080051b4   0x00000018   Code   RO         3486    i.rcu_rtc_clock_config  gd32f4xx_rcu.o
    0x080051cc   0x080051cc   0x00000042   Code   RO         7062    i.recv              socket.o
    0x0800520e   0x0800520e   0x00000002   PAD
    0x08005210   0x08005210   0x00000094   Code   RO         7318    i.recv_data_processing  w5500.o
    0x080052a4   0x080052a4   0x00000200   Code   RO         7063    i.recvfrom          socket.o
    0x080054a4   0x080054a4   0x00000020   Code   RO         7692    i.reset_DHCP_timeout  dhcp.o
    0x080054c4   0x080054c4   0x00000044   Code   RO         7540    i.reset_w5500       w5500_conf.o
    0x08005508   0x08005508   0x000000c4   Code   RO         3767    i.rtc_init          gd32f4xx_rtc.o
    0x080055cc   0x080055cc   0x00000048   Code   RO         3768    i.rtc_init_mode_enter  gd32f4xx_rtc.o
    0x08005614   0x08005614   0x00000014   Code   RO         3769    i.rtc_init_mode_exit  gd32f4xx_rtc.o
    0x08005628   0x08005628   0x00000060   Code   RO         3774    i.rtc_register_sync_wait  gd32f4xx_rtc.o
    0x08005688   0x08005688   0x0000013c   Code   RO         7064    i.send              socket.o
    0x080057c4   0x080057c4   0x000001e8   Code   RO         7693    i.send_DHCP_DECLINE  dhcp.o
    0x080059ac   0x080059ac   0x000001f4   Code   RO         7694    i.send_DHCP_DISCOVER  dhcp.o
    0x08005ba0   0x08005ba0   0x00000374   Code   RO         7695    i.send_DHCP_REQUEST  dhcp.o
    0x08005f14   0x08005f14   0x00000094   Code   RO         7319    i.send_data_processing  w5500.o
    0x08005fa8   0x08005fa8   0x0000010c   Code   RO         7065    i.sendto            socket.o
    0x080060b4   0x080060b4   0x00000010   Code   RO         7320    i.setGAR            w5500.o
    0x080060c4   0x080060c4   0x0000000e   Code   RO         7321    i.setMR             w5500.o
    0x080060d2   0x080060d2   0x00000012   Code   RO         7324    i.setSHAR           w5500.o
    0x080060e4   0x080060e4   0x00000012   Code   RO         7325    i.setSIPR           w5500.o
    0x080060f6   0x080060f6   0x00000012   Code   RO         7326    i.setSUBR           w5500.o
    0x08006108   0x08006108   0x00000016   Code   RO         7327    i.setSn_IR          w5500.o
    0x0800611e   0x0800611e   0x00000002   PAD
    0x08006120   0x08006120   0x00000068   Code   RO         7542    i.set_w5500_mac     w5500_conf.o
    0x08006188   0x08006188   0x000000c4   Code   RO         7066    i.socket            socket.o
    0x0800624c   0x0800624c   0x00000088   Code   RO         7330    i.socket_buf_init   w5500.o
    0x080062d4   0x080062d4   0x0000000a   Code   RO         4335    i.spi_enable        gd32f4xx_spi.o
    0x080062de   0x080062de   0x00000008   Code   RO         4337    i.spi_i2s_data_receive  gd32f4xx_spi.o
    0x080062e6   0x080062e6   0x00000004   Code   RO         4338    i.spi_i2s_data_transmit  gd32f4xx_spi.o
    0x080062ea   0x080062ea   0x00000010   Code   RO         4340    i.spi_i2s_flag_get  gd32f4xx_spi.o
    0x080062fa   0x080062fa   0x00000032   Code   RO         4344    i.spi_init          gd32f4xx_spi.o
    0x0800632c   0x0800632c   0x000000fc   Code   RO         5737    i.system_clock_168m_25m_hxtal  system_gd32f4xx.o
    0x08006428   0x08006428   0x00000008   Code   RO         5738    i.system_clock_config  system_gd32f4xx.o
    0x08006430   0x08006430   0x00000050   Code   RO         6083    i.systick_config    systick.o
    0x08006480   0x08006480   0x000001ec   Code   RO         4649    i.timer_channel_output_config  gd32f4xx_timer.o
    0x0800666c   0x0800666c   0x0000005a   Code   RO         4651    i.timer_channel_output_mode_config  gd32f4xx_timer.o
    0x080066c6   0x080066c6   0x00000026   Code   RO         4653    i.timer_channel_output_pulse_value_config  gd32f4xx_timer.o
    0x080066ec   0x080066ec   0x0000005a   Code   RO         4654    i.timer_channel_output_shadow_config  gd32f4xx_timer.o
    0x08006746   0x08006746   0x00000002   PAD
    0x08006748   0x08006748   0x00000184   Code   RO         4663    i.timer_deinit      gd32f4xx_timer.o
    0x080068cc   0x080068cc   0x0000000a   Code   RO         4668    i.timer_enable      gd32f4xx_timer.o
    0x080068d6   0x080068d6   0x00000002   PAD
    0x080068d8   0x080068d8   0x00000098   Code   RO         4678    i.timer_init        gd32f4xx_timer.o
    0x08006970   0x08006970   0x00000008   Code   RO         4685    i.timer_interrupt_enable  gd32f4xx_timer.o
    0x08006978   0x08006978   0x00000006   Code   RO         4686    i.timer_interrupt_flag_clear  gd32f4xx_timer.o
    0x0800697e   0x0800697e   0x00000018   Code   RO         4687    i.timer_interrupt_flag_get  gd32f4xx_timer.o
    0x08006996   0x08006996   0x00000014   Code   RO         5255    i.trng_deinit       gd32f4xx_trng.o
    0x080069aa   0x080069aa   0x00000002   PAD
    0x080069ac   0x080069ac   0x00000014   Code   RO         5257    i.trng_enable       gd32f4xx_trng.o
    0x080069c0   0x080069c0   0x00000018   Code   RO         5258    i.trng_flag_get     gd32f4xx_trng.o
    0x080069d8   0x080069d8   0x0000000c   Code   RO         5259    i.trng_get_true_random_data  gd32f4xx_trng.o
    0x080069e4   0x080069e4   0x000000e8   Code   RO         5328    i.usart_baudrate_set  gd32f4xx_usart.o
    0x08006acc   0x08006acc   0x0000000a   Code   RO         5332    i.usart_data_receive  gd32f4xx_usart.o
    0x08006ad6   0x08006ad6   0x00000008   Code   RO         5333    i.usart_data_transmit  gd32f4xx_usart.o
    0x08006ade   0x08006ade   0x00000002   PAD
    0x08006ae0   0x08006ae0   0x000000dc   Code   RO         5334    i.usart_deinit      gd32f4xx_usart.o
    0x08006bbc   0x08006bbc   0x0000000a   Code   RO         5338    i.usart_enable      gd32f4xx_usart.o
    0x08006bc6   0x08006bc6   0x0000001e   Code   RO         5340    i.usart_flag_get    gd32f4xx_usart.o
    0x08006be4   0x08006be4   0x00000010   Code   RO         5345    i.usart_hardware_flow_cts_config  gd32f4xx_usart.o
    0x08006bf4   0x08006bf4   0x00000010   Code   RO         5346    i.usart_hardware_flow_rts_config  gd32f4xx_usart.o
    0x08006c04   0x08006c04   0x0000001a   Code   RO         5349    i.usart_interrupt_flag_clear  gd32f4xx_usart.o
    0x08006c1e   0x08006c1e   0x00000038   Code   RO         5350    i.usart_interrupt_flag_get  gd32f4xx_usart.o
    0x08006c56   0x08006c56   0x00000010   Code   RO         5363    i.usart_parity_config  gd32f4xx_usart.o
    0x08006c66   0x08006c66   0x00000010   Code   RO         5365    i.usart_receive_config  gd32f4xx_usart.o
    0x08006c76   0x08006c76   0x00000010   Code   RO         5376    i.usart_stop_bit_set  gd32f4xx_usart.o
    0x08006c86   0x08006c86   0x00000010   Code   RO         5380    i.usart_transmit_config  gd32f4xx_usart.o
    0x08006c96   0x08006c96   0x00000010   Code   RO         5381    i.usart_word_length_set  gd32f4xx_usart.o
    0x08006ca6   0x08006ca6   0x00000002   PAD
    0x08006ca8   0x08006ca8   0x00000068   Code   RO         7546    i.wiz_read_buf      w5500_conf.o
    0x08006d10   0x08006d10   0x00000068   Code   RO         7547    i.wiz_write_buf     w5500_conf.o
    0x08006d78   0x08006d78   0x00000030   Data   RO         8232    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM2 (Exec base: 0x20000000, Load base: 0x08006da8, Size: 0x00000004, Max: 0x00000004, ABSOLUTE, UNINIT)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000        -       0x00000004   Zero   RW         6700    .NoInit             user_step.o


    Execution Region RW_IRAM1 (Exec base: 0x20000004, Load base: 0x08006da8, Size: 0x000084d4, Max: 0x0001fffc, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000004   0x08006da8   0x00000004   Data   RW         5739    .data               system_gd32f4xx.o
    0x20000008   0x08006dac   0x0000000c   Data   RW         6009    .data               main.o
    0x20000014   0x08006db8   0x00000004   Data   RW         6084    .data               systick.o
    0x20000018   0x08006dbc   0x00000006   Data   RW         6500    .data               time.o
    0x2000001e   0x08006dc2   0x00000001   Data   RW         6598    .data               usart.o
    0x2000001f   0x08006dc3   0x00000001   PAD
    0x20000020   0x08006dc4   0x00000002   Data   RW         6832    .data               api_lan_data_process .o
    0x20000022   0x08006dc6   0x00000001   Data   RW         6997    .data               api_w5500.o
    0x20000023   0x08006dc7   0x00000010   Data   RW         7332    .data               w5500.o
    0x20000033   0x08006dd7   0x00000001   PAD
    0x20000034   0x08006dd8   0x0000004c   Data   RW         7697    .data               dhcp.o
    0x20000080   0x08006e24   0x00000004   Data   RW         8163    .data               mc_w.l(stdout.o)
    0x20000084   0x08006e28   0x00000004   Data   RW         8168    .data               mc_w.l(mvars.o)
    0x20000088   0x08006e2c   0x00000004   Data   RW         8169    .data               mc_w.l(mvars.o)
    0x2000008c        -       0x00000014   Zero   RW         6425    .bss                rtc.o
    0x200000a0        -       0x00000014   Zero   RW         6499    .bss                time.o
    0x200000b4        -       0x00000020   Zero   RW         7331    .bss                w5500.o
    0x200000d4        -       0x00000400   Zero   RW         7696    .bss                dhcp.o
    0x200004d4   0x08006e30   0x00000004   PAD
    0x200004d8        -       0x00003000   Zero   RW            2    HEAP                startup_gd32f450_470.o
    0x200034d8        -       0x00005000   Zero   RW            1    STACK               startup_gd32f450_470.o


    Execution Region RW_IRAM3 (Exec base: 0x20020000, Load base: 0x08006e30, Size: 0x00002060, Max: 0x00030000, ABSOLUTE, COMPRESSED[0x00000044])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20020000   COMPRESSED   0x00001817   Data   RW         6597    .RAM_D3             usart.o
    0x20021817   COMPRESSED   0x00000001   PAD
    0x20021818   COMPRESSED   0x00000848   Data   RW         6996    .RAM_D3             api_w5500.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       628          0          0          0          0       3751   25lc080a.o
      2554        678          0          2          0       8699   api_lan_data_process .o
       344         58          0          0          0       1424   api_tnrg.o
      1720        694          0       2121          0       4904   api_w5500.o
      3876        414          0         76       1024      14123   dhcp.o
       906         56          0          0          0       6146   eeprom_spi.o
       842        112          0          0          0      10214   flash.o
         0          0          0          0          0      86372   gd32f4xx_adc.o
       344         34          0          0          0       4131   gd32f4xx_fmc.o
       246          0          0          0          0       3936   gd32f4xx_gpio.o
       150         26          0          0          0       8550   gd32f4xx_it.o
       240         28          0          0          0       2288   gd32f4xx_misc.o
        20          6          0          0          0        594   gd32f4xx_pmu.o
       844         60          0          0          0       6917   gd32f4xx_rcu.o
       384         22          0          0          0       3807   gd32f4xx_rtc.o
        88          0          0          0          0       4214   gd32f4xx_spi.o
      1298         52          0          0          0       8360   gd32f4xx_timer.o
        76         18          0          0          0       2472   gd32f4xx_trng.o
       704         18          0          0          0      11019   gd32f4xx_usart.o
       328         32          0          0          0       2249   gpio.o
      1160        748          0         12          0       4964   main.o
       242          0          0          0          0       2552   my_crc.o
       100          4          0          0         20        877   rtc.o
      1876        202          0          0          0      13397   socket.o
       212         16          0          0          0       1223   spi.o
        36          8        428          0      32768        952   startup_gd32f450_470.o
       460         28          0          4          0       2243   system_gd32f4xx.o
       164         24          0          4          0      32342   systick.o
       686         34          0          6         20       6429   time.o
      1498        404          0       6168          0       9455   usart.o
        56         14          0          0          4      30829   user_step.o
       776         78          0         16         32      16694   w5500.o
       656        176          0          0          0       7547   w5500_conf.o

    ----------------------------------------------------------------------
     23550       <USER>        <GROUP>       8412      33872     323674   Object Totals
         0          0         48          0          0          0   (incl. Generated)
        36          0          0          3          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        86          0          0          0          0          0   __dczerorl2.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         4          0          0          0          0          0   entry12b.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
       188         20          0          0          0        160   malloc.o
        36          0          0          0          0         68   memcpya.o
        36          0          0          0          0        108   memseta.o
         0          0          0          8          0          0   mvars.o
      2268         96          0          0          0        616   printfa.o
         0          0          0          4          0          0   stdout.o
        18          0          0          0          0         68   strcpy.o
        14          0          0          0          0         68   strlen.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdrcmple.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        48          0          0          0          0         68   dfixul.o
       228          0          0          0          0         96   dmul.o

    ----------------------------------------------------------------------
      4046        <USER>          <GROUP>         12          0       2188   Library Totals
         4          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      2976        132          0         12          0       1532   mc_w.l
      1066          0          0          0          0        656   mf_w.l

    ----------------------------------------------------------------------
      4046        <USER>          <GROUP>         12          0       2188   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     27596       4176        476       8424      33872     306102   Grand Totals
     27596       4176        476        204      33872     306102   ELF Image Totals (compressed)
     27596       4176        476        204          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                28072 (  27.41kB)
    Total RW  Size (RW Data + ZI Data)             42296 (  41.30kB)
    Total ROM Size (Code + RO Data + RW Data)      28276 (  27.61kB)

==============================================================================

