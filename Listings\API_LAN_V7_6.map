Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_gd32f450_470.o(RESET) refers to startup_gd32f450_470.o(STACK) for __initial_sp
    startup_gd32f450_470.o(RESET) refers to startup_gd32f450_470.o(.text) for Reset_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.TIMER2_IRQHandler) for TIMER2_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.TIMER3_IRQHandler) for TIMER3_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.EXTI10_15_IRQHandler) for EXTI10_15_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.TIMER6_IRQHandler) for TIMER6_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.DMA1_Channel0_IRQHandler) for DMA1_Channel0_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.DMA1_Channel1_IRQHandler) for DMA1_Channel1_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.DMA1_Channel2_IRQHandler) for DMA1_Channel2_IRQHandler
    startup_gd32f450_470.o(.text) refers to system_gd32f4xx.o(i.SystemInit) for SystemInit
    startup_gd32f450_470.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    gd32f4xx_adc.o(i.adc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_adc.o(i.adc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_can.o(i.can_debug_freeze_disable) refers to gd32f4xx_dbg.o(i.dbg_periph_disable) for dbg_periph_disable
    gd32f4xx_can.o(i.can_debug_freeze_enable) refers to gd32f4xx_dbg.o(i.dbg_periph_enable) for dbg_periph_enable
    gd32f4xx_can.o(i.can_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_can.o(i.can_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_can.o(i.can_interrupt_flag_get) refers to gd32f4xx_can.o(i.can_receive_message_length_get) for can_receive_message_length_get
    gd32f4xx_can.o(i.can_interrupt_flag_get) refers to gd32f4xx_can.o(i.can_error_get) for can_error_get
    gd32f4xx_ctc.o(i.ctc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_ctc.o(i.ctc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_dac.o(i.dac_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_dac.o(i.dac_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_dci.o(i.dci_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_dci.o(i.dci_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_enet.o(i.enet_initpara_reset) for enet_initpara_reset
    gd32f4xx_enet.o(i.enet_descriptors_chain_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_descriptors_chain_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_descriptors_ring_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_descriptors_ring_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_disable) refers to gd32f4xx_enet.o(i.enet_tx_disable) for enet_tx_disable
    gd32f4xx_enet.o(i.enet_disable) refers to gd32f4xx_enet.o(i.enet_rx_disable) for enet_rx_disable
    gd32f4xx_enet.o(i.enet_enable) refers to gd32f4xx_enet.o(i.enet_tx_enable) for enet_tx_enable
    gd32f4xx_enet.o(i.enet_enable) refers to gd32f4xx_enet.o(i.enet_rx_enable) for enet_rx_enable
    gd32f4xx_enet.o(i.enet_frame_receive) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_frame_transmit) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_phy_config) for enet_phy_config
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_delay) for enet_delay
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_default_init) for enet_default_init
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_initpara_config) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_initpara_reset) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_enet.o(i.enet_delay) for enet_delay
    gd32f4xx_enet.o(i.enet_phyloopback_disable) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_phyloopback_enable) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_ptpframe_receive_normal_mode) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_ptpframe_transmit_normal_mode) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_registers_get) refers to gd32f4xx_enet.o(.constdata) for enet_reg_tab
    gd32f4xx_enet.o(i.enet_rxframe_drop) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_rxframe_size_get) refers to gd32f4xx_enet.o(i.enet_rxframe_drop) for enet_rxframe_drop
    gd32f4xx_enet.o(i.enet_rxframe_size_get) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_rxprocess_check_recovery) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_tx_disable) refers to gd32f4xx_enet.o(i.enet_txfifo_flush) for enet_txfifo_flush
    gd32f4xx_enet.o(i.enet_tx_enable) refers to gd32f4xx_enet.o(i.enet_txfifo_flush) for enet_txfifo_flush
    gd32f4xx_fmc.o(i.fmc_bank0_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_bank1_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_byte_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_halfword_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_mass_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_page_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_ready_wait) refers to gd32f4xx_fmc.o(i.fmc_state_get) for fmc_state_get
    gd32f4xx_fmc.o(i.fmc_sector_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_word_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_drp_disable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_drp_enable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_security_protection_config) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_user_write) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_write_protection_disable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_write_protection_enable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_gpio.o(i.gpio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_gpio.o(i.gpio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_i2c.o(i.i2c_clock_config) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_i2c.o(i.i2c_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_i2c.o(i.i2c_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_ipa.o(i.ipa_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_ipa.o(i.ipa_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_iref.o(i.iref_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_iref.o(i.iref_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_misc.o(i.nvic_irq_enable) refers to gd32f4xx_misc.o(i.nvic_priority_group_set) for nvic_priority_group_set
    gd32f4xx_pmu.o(i.pmu_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_pmu.o(i.pmu_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_pmu.o(i.pmu_highdriver_switch_select) refers to gd32f4xx_pmu.o(i.pmu_flag_get) for pmu_flag_get
    gd32f4xx_pmu.o(i.pmu_to_deepsleepmode) refers to gd32f4xx_pmu.o(.bss) for reg_snap
    gd32f4xx_rcu.o(i.rcu_deinit) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    gd32f4xx_rcu.o(i.rcu_osci_stab_wait) refers to gd32f4xx_rcu.o(i.rcu_flag_get) for rcu_flag_get
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_config) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_config) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_deinit) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_deinit) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_rtc.o(i.rtc_refclock_detection_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_refclock_detection_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_refclock_detection_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_refclock_detection_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_second_adjust) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_sdio.o(i.sdio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_sdio.o(i.sdio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_osci_on) for rcu_osci_on
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_i2s_clock_config) for rcu_i2s_clock_config
    gd32f4xx_spi.o(i.spi_i2s_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_spi.o(i.spi_i2s_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_syscfg.o(i.syscfg_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_syscfg.o(i.syscfg_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_timer.o(i.timer_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_timer.o(i.timer_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_timer.o(i.timer_external_clock_mode0_config) refers to gd32f4xx_timer.o(i.timer_external_trigger_config) for timer_external_trigger_config
    gd32f4xx_timer.o(i.timer_external_clock_mode1_config) refers to gd32f4xx_timer.o(i.timer_external_trigger_config) for timer_external_trigger_config
    gd32f4xx_timer.o(i.timer_external_trigger_as_external_clock_config) refers to gd32f4xx_timer.o(i.timer_input_trigger_source_select) for timer_input_trigger_source_select
    gd32f4xx_timer.o(i.timer_input_capture_config) refers to gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config) for timer_channel_input_capture_prescaler_config
    gd32f4xx_timer.o(i.timer_input_pwm_capture_config) refers to gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config) for timer_channel_input_capture_prescaler_config
    gd32f4xx_timer.o(i.timer_internal_trigger_as_external_clock_config) refers to gd32f4xx_timer.o(i.timer_input_trigger_source_select) for timer_input_trigger_source_select
    gd32f4xx_tli.o(i.tli_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_tli.o(i.tli_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_trng.o(i.trng_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_trng.o(i.trng_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_usart.o(i.usart_baudrate_set) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_usart.o(i.usart_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_usart.o(i.usart_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_wwdgt.o(i.wwdgt_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_wwdgt.o(i.wwdgt_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    system_gd32f4xx.o(i.SystemCoreClockUpdate) refers to system_gd32f4xx.o(.data) for SystemCoreClock
    system_gd32f4xx.o(i.SystemInit) refers to system_gd32f4xx.o(i.system_clock_config) for system_clock_config
    system_gd32f4xx.o(i.system_clock_config) refers to system_gd32f4xx.o(i.system_clock_168m_25m_hxtal) for system_clock_168m_25m_hxtal
    gd32f4xx_it.o(i.HardFault_Handler) refers to user_step.o(i.SysReset_Condition) for SysReset_Condition
    gd32f4xx_it.o(i.SysTick_Handler) refers to systick.o(i.delay_decrement) for delay_decrement
    gd32f4xx_it.o(i.TIMER3_IRQHandler) refers to gd32f4xx_timer.o(i.timer_interrupt_flag_get) for timer_interrupt_flag_get
    gd32f4xx_it.o(i.TIMER3_IRQHandler) refers to time.o(i.TIM_PeriodElapsedCallback) for TIM_PeriodElapsedCallback
    gd32f4xx_it.o(i.TIMER6_IRQHandler) refers to gd32f4xx_timer.o(i.timer_interrupt_flag_get) for timer_interrupt_flag_get
    gd32f4xx_it.o(i.TIMER6_IRQHandler) refers to time.o(i.TIM_PeriodElapsedCallback) for TIM_PeriodElapsedCallback
    gd32f4xx_it.o(i.USART2_IRQHandler) refers to gd32f4xx_usart.o(i.usart_interrupt_flag_get) for usart_interrupt_flag_get
    gd32f4xx_it.o(i.USART2_IRQHandler) refers to usart.o(i.UART_RxCpltCallback) for UART_RxCpltCallback
    gd32f4xx_it.o(i.USART2_IRQHandler) refers to usart.o(i.UART_IDLECallBack) for UART_IDLECallBack
    main.o(i.main) refers to systick.o(i.systick_config) for systick_config
    main.o(i.main) refers to gpio.o(i.GPIO_Init) for GPIO_Init
    main.o(i.main) refers to usart.o(i.USART0_Init) for USART0_Init
    systick.o(i.delay_1ms) refers to systick.o(.data) for delay
    systick.o(i.delay_decrement) refers to systick.o(.data) for delay
    systick.o(i.systick_config) refers to systick.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    systick.o(i.systick_config) refers to system_gd32f4xx.o(.data) for SystemCoreClock
    25lc080a.o(i.EEPROM_WritePage) refers to malloc.o(i.malloc) for malloc
    25lc080a.o(i.EEPROM_WritePage) refers to eeprom_spi.o(i.EEPROM_SPI_WriteBuffer) for EEPROM_SPI_WriteBuffer
    25lc080a.o(i.EEPROM_WritePage) refers to eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) for EEPROM_SPI_ReadBuffer
    25lc080a.o(i.EEPROM_WritePage) refers to malloc.o(i.free) for free
    25lc080a.o(i.EEPROM_WritePage) refers to my_crc.o(i.CRC16_USB) for CRC16_USB
    25lc080a.o(i.EEPROM_WritePage) refers to 25lc080a.o(i.USER_EEPROM_WriteByte) for USER_EEPROM_WriteByte
    25lc080a.o(i.EEPROM_WritePage) refers to 25lc080a.o(i.USER_EEPROM_RedeByte) for USER_EEPROM_RedeByte
    25lc080a.o(i.USER_EEPROM_RedeByte) refers to eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) for EEPROM_SPI_ReadBuffer
    25lc080a.o(i.USER_EEPROM_RedePage) refers to malloc.o(i.malloc) for malloc
    25lc080a.o(i.USER_EEPROM_RedePage) refers to eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) for EEPROM_SPI_ReadBuffer
    25lc080a.o(i.USER_EEPROM_RedePage) refers to my_crc.o(i.CRC16_USB) for CRC16_USB
    25lc080a.o(i.USER_EEPROM_RedePage) refers to malloc.o(i.free) for free
    25lc080a.o(i.USER_EEPROM_WriteByte) refers to malloc.o(i.malloc) for malloc
    25lc080a.o(i.USER_EEPROM_WriteByte) refers to eeprom_spi.o(i.EEPROM_SPI_WriteBuffer) for EEPROM_SPI_WriteBuffer
    25lc080a.o(i.USER_EEPROM_WriteByte) refers to eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) for EEPROM_SPI_ReadBuffer
    25lc080a.o(i.USER_EEPROM_WriteByte) refers to my_crc.o(i.CRC16_USB) for CRC16_USB
    25lc080a.o(i.USER_EEPROM_WriteByte) refers to 25lc080a.o(i.USER_EEPROM_RedeByte) for USER_EEPROM_RedeByte
    25lc080a.o(i.USER_EEPROM_WriteByte) refers to malloc.o(i.free) for free
    25lc080a.o(i.USER_EEPROM_WritePage) refers to 25lc080a.o(i.EEPROM_WritePage) for EEPROM_WritePage
    eeprom_spi.o(i.EEPROM_ReadStatusRegister) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    eeprom_spi.o(i.EEPROM_ReadStatusRegister) refers to eeprom_spi.o(i.EEPROM_SendByte) for EEPROM_SendByte
    eeprom_spi.o(i.EEPROM_ReadStatusRegister) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) refers to systick.o(i.delay_1ms) for delay_1ms
    eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) refers to eeprom_spi.o(i.EEPROM_SPI_SendInstruction) for EEPROM_SPI_SendInstruction
    eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) refers to spi.o(i.DRV_SPI_SwapByte) for DRV_SPI_SwapByte
    eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    eeprom_spi.o(i.EEPROM_SPI_SendInstruction) refers to systick.o(i.delay_1ms) for delay_1ms
    eeprom_spi.o(i.EEPROM_SPI_SendInstruction) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    eeprom_spi.o(i.EEPROM_SPI_SendInstruction) refers to spi.o(i.DRV_SPI_SwapByte) for DRV_SPI_SwapByte
    eeprom_spi.o(i.EEPROM_SPI_WaitStandbyState) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    eeprom_spi.o(i.EEPROM_SPI_WaitStandbyState) refers to eeprom_spi.o(i.EEPROM_SPI_SendInstruction) for EEPROM_SPI_SendInstruction
    eeprom_spi.o(i.EEPROM_SPI_WaitStandbyState) refers to spi.o(i.DRV_SPI_SwapByte) for DRV_SPI_SwapByte
    eeprom_spi.o(i.EEPROM_SPI_WaitStandbyState) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    eeprom_spi.o(i.EEPROM_SPI_WriteBuffer) refers to eeprom_spi.o(i.EEPROM_SPI_WritePage) for EEPROM_SPI_WritePage
    eeprom_spi.o(i.EEPROM_SPI_WritePage) refers to systick.o(i.delay_1ms) for delay_1ms
    eeprom_spi.o(i.EEPROM_SPI_WritePage) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    eeprom_spi.o(i.EEPROM_SPI_WritePage) refers to eeprom_spi.o(i.EEPROM_WriteEnable) for EEPROM_WriteEnable
    eeprom_spi.o(i.EEPROM_SPI_WritePage) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    eeprom_spi.o(i.EEPROM_SPI_WritePage) refers to eeprom_spi.o(i.EEPROM_SPI_SendInstruction) for EEPROM_SPI_SendInstruction
    eeprom_spi.o(i.EEPROM_SPI_WritePage) refers to spi.o(i.DRV_SPI_SwapByte) for DRV_SPI_SwapByte
    eeprom_spi.o(i.EEPROM_SPI_WritePage) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    eeprom_spi.o(i.EEPROM_SPI_WritePage) refers to eeprom_spi.o(i.EEPROM_SPI_WaitStandbyState) for EEPROM_SPI_WaitStandbyState
    eeprom_spi.o(i.EEPROM_SPI_WritePage) refers to eeprom_spi.o(i.EEPROM_WriteDisable) for EEPROM_WriteDisable
    eeprom_spi.o(i.EEPROM_SendByte) refers to systick.o(i.delay_1ms) for delay_1ms
    eeprom_spi.o(i.EEPROM_SendByte) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    eeprom_spi.o(i.EEPROM_SendByte) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    eeprom_spi.o(i.EEPROM_SendByte) refers to gd32f4xx_spi.o(i.spi_i2s_data_receive) for spi_i2s_data_receive
    eeprom_spi.o(i.EEPROM_WriteDisable) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    eeprom_spi.o(i.EEPROM_WriteDisable) refers to eeprom_spi.o(i.EEPROM_SPI_SendInstruction) for EEPROM_SPI_SendInstruction
    eeprom_spi.o(i.EEPROM_WriteDisable) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    eeprom_spi.o(i.EEPROM_WriteEnable) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    eeprom_spi.o(i.EEPROM_WriteEnable) refers to eeprom_spi.o(i.EEPROM_SPI_SendInstruction) for EEPROM_SPI_SendInstruction
    eeprom_spi.o(i.EEPROM_WriteEnable) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    eeprom_spi.o(i.EEPROM_WriteStatusRegister) refers to eeprom_spi.o(i.EEPROM_WriteEnable) for EEPROM_WriteEnable
    eeprom_spi.o(i.EEPROM_WriteStatusRegister) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    eeprom_spi.o(i.EEPROM_WriteStatusRegister) refers to eeprom_spi.o(i.EEPROM_SPI_SendInstruction) for EEPROM_SPI_SendInstruction
    eeprom_spi.o(i.EEPROM_WriteStatusRegister) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    eeprom_spi.o(i.EEPROM_WriteStatusRegister) refers to eeprom_spi.o(i.EEPROM_WriteDisable) for EEPROM_WriteDisable
    eeprom_spi.o(i.EEPROM_WriteStatusRegister) refers to eeprom_spi.o(i.EEPROM_SPI_WaitStandbyState) for EEPROM_SPI_WaitStandbyState
    flash.o(i.bsp_EraseCpuFlash) refers to gd32f4xx_fmc.o(i.fmc_unlock) for fmc_unlock
    flash.o(i.bsp_EraseCpuFlash) refers to flash.o(i.FLASH_If_Init) for FLASH_If_Init
    flash.o(i.bsp_EraseCpuFlash) refers to flash.o(i.bsp_GetSector) for bsp_GetSector
    flash.o(i.bsp_EraseCpuFlash) refers to gd32f4xx_fmc.o(i.fmc_sector_erase) for fmc_sector_erase
    flash.o(i.bsp_EraseCpuFlash) refers to gd32f4xx_fmc.o(i.fmc_lock) for fmc_lock
    flash.o(i.bsp_WriteCpuFlash) refers to flash.o(i.bsp_CmpCpuFlash) for bsp_CmpCpuFlash
    flash.o(i.bsp_WriteCpuFlash) refers to gd32f4xx_fmc.o(i.fmc_unlock) for fmc_unlock
    flash.o(i.bsp_WriteCpuFlash) refers to gd32f4xx_fmc.o(i.fmc_word_program) for fmc_word_program
    flash.o(i.bsp_WriteCpuFlash) refers to memcpya.o(.text) for __aeabi_memcpy
    flash.o(i.bsp_WriteCpuFlash) refers to gd32f4xx_fmc.o(i.fmc_lock) for fmc_lock
    gpio.o(i.API_Chose_TS5A3359_GAIN) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    gpio.o(i.API_Chose_TS5A3359_GAIN) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    gpio.o(i.API_LED_GPIO) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gpio.o(i.API_LED_GPIO) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    gpio.o(i.API_LED_GPIO) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    gpio.o(i.GPIO_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gpio.o(i.GPIO_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    gpio.o(i.GPIO_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    gpio.o(i.GPIO_Init) refers to gpio.o(i.Init_GPIO_TS5A339) for Init_GPIO_TS5A339
    gpio.o(i.GPIO_Init) refers to gpio.o(i.API_Chose_TS5A3359_GAIN) for API_Chose_TS5A3359_GAIN
    gpio.o(i.GPIO_Init) refers to gpio.o(i.API_LED_GPIO) for API_LED_GPIO
    gpio.o(i.GPIO_Init) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    gpio.o(i.Init_GPIO_TS5A339) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gpio.o(i.Init_GPIO_TS5A339) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    gpio.o(i.Init_GPIO_TS5A339) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    my_crc.o(i.CRC16_USB) refers to my_crc.o(i.InvertUint8) for InvertUint8
    my_crc.o(i.CRC16_USB) refers to my_crc.o(i.InvertUint16) for InvertUint16
    rtc.o(i.RTC_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    rtc.o(i.RTC_Init) refers to gd32f4xx_pmu.o(i.pmu_backup_write_enable) for pmu_backup_write_enable
    rtc.o(i.RTC_Init) refers to gd32f4xx_rcu.o(i.rcu_osci_on) for rcu_osci_on
    rtc.o(i.RTC_Init) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    rtc.o(i.RTC_Init) refers to gd32f4xx_rcu.o(i.rcu_rtc_clock_config) for rcu_rtc_clock_config
    rtc.o(i.RTC_Init) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    rtc.o(i.RTC_Init) refers to gd32f4xx_rtc.o(i.rtc_init) for rtc_init
    rtc.o(i.RTC_Init) refers to rtc.o(.bss) for rtc_initpara
    rtc.o(i.rtc_register_set) refers to gd32f4xx_rtc.o(i.rtc_init) for rtc_init
    rtc.o(i.rtc_register_set) refers to rtc.o(.bss) for rtc_initpara
    spi.o(i.DRV_SPI_SwapByte) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    spi.o(i.DRV_SPI_SwapByte) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    spi.o(i.DRV_SPI_SwapByte) refers to gd32f4xx_spi.o(i.spi_i2s_data_receive) for spi_i2s_data_receive
    spi.o(i.SPI1_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    spi.o(i.SPI1_Init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    spi.o(i.SPI1_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    spi.o(i.SPI1_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    spi.o(i.SPI1_Init) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    spi.o(i.SPI1_Init) refers to gd32f4xx_spi.o(i.spi_init) for spi_init
    spi.o(i.SPI1_Init) refers to gd32f4xx_spi.o(i.spi_enable) for spi_enable
    time.o(i.TIMER1_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    time.o(i.TIMER1_Init) refers to gd32f4xx_timer.o(i.timer_deinit) for timer_deinit
    time.o(i.TIMER1_Init) refers to gd32f4xx_timer.o(i.timer_init) for timer_init
    time.o(i.TIMER1_Init) refers to gd32f4xx_timer.o(i.timer_channel_output_config) for timer_channel_output_config
    time.o(i.TIMER1_Init) refers to gd32f4xx_timer.o(i.timer_channel_output_pulse_value_config) for timer_channel_output_pulse_value_config
    time.o(i.TIMER1_Init) refers to gd32f4xx_timer.o(i.timer_channel_output_mode_config) for timer_channel_output_mode_config
    time.o(i.TIMER1_Init) refers to gd32f4xx_timer.o(i.timer_channel_output_shadow_config) for timer_channel_output_shadow_config
    time.o(i.TIMER3_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    time.o(i.TIMER3_Init) refers to gd32f4xx_timer.o(i.timer_deinit) for timer_deinit
    time.o(i.TIMER3_Init) refers to gd32f4xx_timer.o(i.timer_init) for timer_init
    time.o(i.TIMER3_Init) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    time.o(i.TIMER3_Init) refers to gd32f4xx_timer.o(i.timer_interrupt_enable) for timer_interrupt_enable
    time.o(i.TIMER3_Init) refers to gd32f4xx_timer.o(i.timer_enable) for timer_enable
    time.o(i.TIMER6_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    time.o(i.TIMER6_Init) refers to gd32f4xx_timer.o(i.timer_deinit) for timer_deinit
    time.o(i.TIMER6_Init) refers to gd32f4xx_timer.o(i.timer_init) for timer_init
    time.o(i.TIMER6_Init) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    time.o(i.TIMER6_Init) refers to gd32f4xx_timer.o(i.timer_interrupt_enable) for timer_interrupt_enable
    time.o(i.TIMER6_Init) refers to gd32f4xx_timer.o(i.timer_enable) for timer_enable
    time.o(i.TIM_PeriodElapsedCallback) refers to gd32f4xx_timer.o(i.timer_interrupt_flag_clear) for timer_interrupt_flag_clear
    time.o(i.TIM_PeriodElapsedCallback) refers to user_step.o(i.SysReset_Condition) for SysReset_Condition
    time.o(i.TIM_PeriodElapsedCallback) refers to time.o(.data) for tim6_msTic
    time.o(i.TIM_PeriodElapsedCallback) refers to dhcp.o(.data) for dhcp_time
    time.o(i.TIM_PeriodElapsedCallback) refers to api_w5500.o(.RAM_D3) for Lan_Para
    time.o(i.TIM_PeriodElapsedCallback) refers to time.o(.bss) for g_tTimeSign
    usart.o(i.API_Printf_Hex) refers to printfa.o(i.__0printf) for __2printf
    usart.o(i.API_do_tcp_client) refers to w5500.o(i.getSn_SR) for getSn_SR
    usart.o(i.API_do_tcp_client) refers to socket.o(i.socket) for socket
    usart.o(i.API_do_tcp_client) refers to socket.o(i.connect) for connect
    usart.o(i.API_do_tcp_client) refers to w5500.o(i.getSn_IR) for getSn_IR
    usart.o(i.API_do_tcp_client) refers to w5500.o(i.setSn_IR) for setSn_IR
    usart.o(i.API_do_tcp_client) refers to w5500.o(i.getSn_RX_RSR) for getSn_RX_RSR
    usart.o(i.API_do_tcp_client) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    usart.o(i.API_do_tcp_client) refers to socket.o(i.recv) for recv
    usart.o(i.API_do_tcp_client) refers to printfa.o(i.__0printf) for __2printf
    usart.o(i.API_do_tcp_client) refers to usart.o(i.API_Printf_Hex) for API_Printf_Hex
    usart.o(i.API_do_tcp_client) refers to api_lan_data_process .o(i.API_Process_Lan_Data) for API_Process_Lan_Data
    usart.o(i.API_do_tcp_client) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    usart.o(i.API_do_tcp_client) refers to socket.o(i.disconnect) for disconnect
    usart.o(i.API_do_tcp_client) refers to socket.o(i.close) for close
    usart.o(i.API_do_tcp_client) refers to api_w5500.o(.RAM_D3) for Lan_Para
    usart.o(i.ESP32_IO_Init) refers to memseta.o(.text) for __aeabi_memclr4
    usart.o(i.ESP32_IO_Init) refers to usart.o(i.InitBuffer) for InitBuffer
    usart.o(i.ESP32_IO_Init) refers to usart.o(.RAM_D3) for sg_tUsartDriveHandle
    usart.o(i.FML_USART_RecvTask) refers to memseta.o(.text) for __aeabi_memclr4
    usart.o(i.FML_USART_RecvTask) refers to usart.o(i.ReadBytesToBuffer) for ReadBytesToBuffer
    usart.o(i.FML_USART_RecvTask) refers to memcpya.o(.text) for __aeabi_memcpy
    usart.o(i.FML_USART_RecvTask) refers to usart.o(.RAM_D3) for sg_tUsartDriveHandle
    usart.o(i.FML_USART_Register) refers to usart.o(.RAM_D3) for sg_tUsartDriveHandle
    usart.o(i.InitBuffer) refers to memseta.o(.text) for __aeabi_memclr
    usart.o(i.ReadBytesToBuffer) refers to memcpya.o(.text) for __aeabi_memcpy
    usart.o(i.RecvDataHandler) refers to usart.o(i.AddByteToBuffer) for AddByteToBuffer
    usart.o(i.UART_IDLECallBack) refers to gd32f4xx_usart.o(i.usart_interrupt_flag_clear) for usart_interrupt_flag_clear
    usart.o(i.UART_IDLECallBack) refers to gd32f4xx_usart.o(i.usart_data_receive) for usart_data_receive
    usart.o(i.UART_IDLECallBack) refers to usart.o(i.FML_USART_RecvTask) for FML_USART_RecvTask
    usart.o(i.UART_RxCpltCallback) refers to gd32f4xx_usart.o(i.usart_data_receive) for usart_data_receive
    usart.o(i.UART_RxCpltCallback) refers to usart.o(i.RecvDataHandler) for RecvDataHandler
    usart.o(i.UART_RxCpltCallback) refers to usart.o(.RAM_D3) for usart2_buf
    usart.o(i.UARTx_SendBuffer) refers to gd32f4xx_usart.o(i.usart_flag_get) for usart_flag_get
    usart.o(i.UARTx_SendBuffer) refers to gd32f4xx_usart.o(i.usart_data_transmit) for usart_data_transmit
    usart.o(i.USART0_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    usart.o(i.USART0_Init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    usart.o(i.USART0_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    usart.o(i.USART0_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    usart.o(i.USART0_Init) refers to gd32f4xx_usart.o(i.usart_deinit) for usart_deinit
    usart.o(i.USART0_Init) refers to gd32f4xx_usart.o(i.usart_baudrate_set) for usart_baudrate_set
    usart.o(i.USART0_Init) refers to gd32f4xx_usart.o(i.usart_receive_config) for usart_receive_config
    usart.o(i.USART0_Init) refers to gd32f4xx_usart.o(i.usart_transmit_config) for usart_transmit_config
    usart.o(i.USART0_Init) refers to gd32f4xx_usart.o(i.usart_enable) for usart_enable
    usart.o(i.USART2_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    usart.o(i.USART2_Init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    usart.o(i.USART2_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    usart.o(i.USART2_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    usart.o(i.USART2_Init) refers to gd32f4xx_usart.o(i.usart_deinit) for usart_deinit
    usart.o(i.USART2_Init) refers to gd32f4xx_usart.o(i.usart_baudrate_set) for usart_baudrate_set
    usart.o(i.USART2_Init) refers to gd32f4xx_usart.o(i.usart_receive_config) for usart_receive_config
    usart.o(i.USART2_Init) refers to gd32f4xx_usart.o(i.usart_transmit_config) for usart_transmit_config
    usart.o(i.USART2_Init) refers to gd32f4xx_usart.o(i.usart_enable) for usart_enable
    usart.o(i.USART2_Init) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    usart.o(i.USART2_Init) refers to gd32f4xx_usart.o(i.usart_interrupt_enable) for usart_interrupt_enable
    usart.o(i.fputc) refers to gd32f4xx_usart.o(i.usart_data_transmit) for usart_data_transmit
    usart.o(i.fputc) refers to gd32f4xx_usart.o(i.usart_flag_get) for usart_flag_get
    user_step.o(i.SysReset_Condition) refers to user_step.o(.NoInit) for g_JumpInit
    gd32f470v_start.o(i.gd_eval_BEEP_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f470v_start.o(i.gd_eval_BEEP_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    gd32f470v_start.o(i.gd_eval_BEEP_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    gd32f470v_start.o(i.gd_eval_BEEP_init) refers to gd32f470v_start.o(.data) for BEEP_CLK
    gd32f470v_start.o(i.gd_eval_BEEP_off) refers to gd32f470v_start.o(.data) for BEEP_GPIO_PIN
    gd32f470v_start.o(i.gd_eval_BEEP_on) refers to gd32f470v_start.o(.data) for BEEP_GPIO_PIN
    gd32f470v_start.o(i.gd_eval_key_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f470v_start.o(i.gd_eval_key_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    gd32f470v_start.o(i.gd_eval_key_init) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    gd32f470v_start.o(i.gd_eval_key_init) refers to gd32f4xx_syscfg.o(i.syscfg_exti_line_config) for syscfg_exti_line_config
    gd32f470v_start.o(i.gd_eval_key_init) refers to gd32f4xx_exti.o(i.exti_init) for exti_init
    gd32f470v_start.o(i.gd_eval_key_init) refers to gd32f4xx_exti.o(i.exti_interrupt_flag_clear) for exti_interrupt_flag_clear
    gd32f470v_start.o(i.gd_eval_key_init) refers to gd32f470v_start.o(.data) for KEY_CLK
    gd32f470v_start.o(i.gd_eval_key_state_get) refers to gd32f4xx_gpio.o(i.gpio_input_bit_get) for gpio_input_bit_get
    gd32f470v_start.o(i.gd_eval_key_state_get) refers to gd32f470v_start.o(.data) for KEY_PIN
    gd32f470v_start.o(i.gd_eval_led_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f470v_start.o(i.gd_eval_led_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    gd32f470v_start.o(i.gd_eval_led_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    gd32f470v_start.o(i.gd_eval_led_init) refers to gd32f470v_start.o(.data) for GPIO_CLK
    gd32f470v_start.o(i.gd_eval_led_off) refers to gd32f470v_start.o(.data) for GPIO_PIN
    gd32f470v_start.o(i.gd_eval_led_on) refers to gd32f470v_start.o(.data) for GPIO_PIN
    gd32f470v_start.o(i.gd_eval_led_toggle) refers to gd32f470v_start.o(.data) for GPIO_PIN
    api_lan_data_process .o(i.API_ACK_21CMD) refers to memseta.o(.text) for __aeabi_memclr4
    api_lan_data_process .o(i.API_ACK_21CMD) refers to my_crc.o(i.CRC16_USB) for CRC16_USB
    api_lan_data_process .o(i.API_ACK_21CMD) refers to socket.o(i.send) for send
    api_lan_data_process .o(i.API_ACK_21CMD) refers to printfa.o(i.__0printf) for __2printf
    api_lan_data_process .o(i.API_ACK_21CMD) refers to usart.o(i.API_Printf_Hex) for API_Printf_Hex
    api_lan_data_process .o(i.API_ACK_22CMD) refers to flash.o(i.bsp_EraseCpuFlash) for bsp_EraseCpuFlash
    api_lan_data_process .o(i.API_ACK_22CMD) refers to my_crc.o(i.CRC16_USB) for CRC16_USB
    api_lan_data_process .o(i.API_ACK_22CMD) refers to socket.o(i.send) for send
    api_lan_data_process .o(i.API_ACK_22CMD) refers to printfa.o(i.__0printf) for __2printf
    api_lan_data_process .o(i.API_ACK_22CMD) refers to usart.o(i.API_Printf_Hex) for API_Printf_Hex
    api_lan_data_process .o(i.API_ACK_22CMD) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_lan_data_process .o(i.API_ACK_23CMD) refers to memseta.o(.text) for __aeabi_memclr4
    api_lan_data_process .o(i.API_ACK_23CMD) refers to my_crc.o(i.CRC16_USB) for CRC16_USB
    api_lan_data_process .o(i.API_ACK_23CMD) refers to flash.o(i.bsp_WriteCpuFlash) for bsp_WriteCpuFlash
    api_lan_data_process .o(i.API_ACK_23CMD) refers to flash.o(i.bsp_CmpCpuFlash) for bsp_CmpCpuFlash
    api_lan_data_process .o(i.API_ACK_23CMD) refers to socket.o(i.send) for send
    api_lan_data_process .o(i.API_ACK_23CMD) refers to printfa.o(i.__0printf) for __2printf
    api_lan_data_process .o(i.API_ACK_23CMD) refers to usart.o(i.API_Printf_Hex) for API_Printf_Hex
    api_lan_data_process .o(i.API_ACK_23CMD) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_lan_data_process .o(i.API_ACK_24CMD) refers to memseta.o(.text) for __aeabi_memclr4
    api_lan_data_process .o(i.API_ACK_24CMD) refers to my_crc.o(i.CRC16_USB) for CRC16_USB
    api_lan_data_process .o(i.API_ACK_24CMD) refers to socket.o(i.send) for send
    api_lan_data_process .o(i.API_ACK_24CMD) refers to printfa.o(i.__0printf) for __2printf
    api_lan_data_process .o(i.API_ACK_24CMD) refers to usart.o(i.API_Printf_Hex) for API_Printf_Hex
    api_lan_data_process .o(i.API_ACK_24CMD) refers to 25lc080a.o(i.USER_EEPROM_WriteByte) for USER_EEPROM_WriteByte
    api_lan_data_process .o(i.API_ACK_24CMD) refers to user_step.o(i.SysReset_Condition) for SysReset_Condition
    api_lan_data_process .o(i.API_ACK_24CMD) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_lan_data_process .o(i.API_ACK_26CMD) refers to memseta.o(.text) for __aeabi_memclr4
    api_lan_data_process .o(i.API_ACK_26CMD) refers to my_crc.o(i.CRC16_USB) for CRC16_USB
    api_lan_data_process .o(i.API_ACK_26CMD) refers to socket.o(i.send) for send
    api_lan_data_process .o(i.API_ACK_26CMD) refers to printfa.o(i.__0printf) for __2printf
    api_lan_data_process .o(i.API_ACK_26CMD) refers to usart.o(i.API_Printf_Hex) for API_Printf_Hex
    api_lan_data_process .o(i.API_ACK_26CMD) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_lan_data_process .o(i.API_ACK_26CMD) refers to main.o(.data) for Soft_Ver
    api_lan_data_process .o(i.API_ACK_28CMD) refers to memseta.o(.text) for __aeabi_memclr4
    api_lan_data_process .o(i.API_ACK_28CMD) refers to my_crc.o(i.CRC16_USB) for CRC16_USB
    api_lan_data_process .o(i.API_ACK_28CMD) refers to socket.o(i.send) for send
    api_lan_data_process .o(i.API_ACK_28CMD) refers to printfa.o(i.__0printf) for __2printf
    api_lan_data_process .o(i.API_ACK_28CMD) refers to usart.o(i.API_Printf_Hex) for API_Printf_Hex
    api_lan_data_process .o(i.API_ACK_29CMD) refers to memseta.o(.text) for __aeabi_memclr4
    api_lan_data_process .o(i.API_ACK_29CMD) refers to my_crc.o(i.CRC16_USB) for CRC16_USB
    api_lan_data_process .o(i.API_ACK_29CMD) refers to socket.o(i.send) for send
    api_lan_data_process .o(i.API_ACK_29CMD) refers to printfa.o(i.__0printf) for __2printf
    api_lan_data_process .o(i.API_ACK_29CMD) refers to usart.o(i.API_Printf_Hex) for API_Printf_Hex
    api_lan_data_process .o(i.API_ACK_29CMD) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_lan_data_process .o(i.API_AnalysisIpPortAddr) refers to atoi.o(.text) for atoi
    api_lan_data_process .o(i.API_AnalysisIpPortAddr) refers to strchr.o(.text) for strchr
    api_lan_data_process .o(i.API_Erase_Flash) refers to eeprom_spi.o(i.EEPROM_SPI_WriteBuffer) for EEPROM_SPI_WriteBuffer
    api_lan_data_process .o(i.API_Erase_Flash) refers to printfa.o(i.__0printf) for __2printf
    api_lan_data_process .o(i.API_ExecuteSeverCMD) refers to api_lan_data_process .o(i.API_ACK_20CMD) for API_ACK_20CMD
    api_lan_data_process .o(i.API_ExecuteSeverCMD) refers to api_lan_data_process .o(i.API_ACK_21CMD) for API_ACK_21CMD
    api_lan_data_process .o(i.API_ExecuteSeverCMD) refers to api_lan_data_process .o(i.API_ACK_22CMD) for API_ACK_22CMD
    api_lan_data_process .o(i.API_ExecuteSeverCMD) refers to api_lan_data_process .o(i.API_ACK_23CMD) for API_ACK_23CMD
    api_lan_data_process .o(i.API_ExecuteSeverCMD) refers to api_lan_data_process .o(i.API_ACK_24CMD) for API_ACK_24CMD
    api_lan_data_process .o(i.API_ExecuteSeverCMD) refers to api_lan_data_process .o(i.API_ACK_26CMD) for API_ACK_26CMD
    api_lan_data_process .o(i.API_ExecuteSeverCMD) refers to api_lan_data_process .o(i.API_ACK_28CMD) for API_ACK_28CMD
    api_lan_data_process .o(i.API_ExecuteSeverCMD) refers to api_lan_data_process .o(i.API_ACK_25CMD) for API_ACK_25CMD
    api_lan_data_process .o(i.API_ExecuteSeverCMD) refers to api_lan_data_process .o(i.API_ACK_27CMD) for API_ACK_27CMD
    api_lan_data_process .o(i.API_ExecuteSeverCMD) refers to api_lan_data_process .o(i.API_ACK_29CMD) for API_ACK_29CMD
    api_lan_data_process .o(i.API_ExecuteSeverCMD) refers to api_w5500.o(i.API_W5500_Reset) for API_W5500_Reset
    api_lan_data_process .o(i.API_ExecuteSeverCMD) refers to api_lan_data_process .o(i.API_Write_SeverInfo) for API_Write_SeverInfo
    api_lan_data_process .o(i.API_ExecuteSeverCMD) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_lan_data_process .o(i.API_LAN_Info_To_String) refers to api_lan_data_process .o(i.HEXArrayToStringArray) for HEXArrayToStringArray
    api_lan_data_process .o(i.API_LAN_Info_To_String) refers to api_lan_data_process .o(i.API_Itoi) for API_Itoi
    api_lan_data_process .o(i.API_LAN_Info_To_String) refers to strcpy.o(.text) for strcpy
    api_lan_data_process .o(i.API_LAN_Info_To_String) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_lan_data_process .o(i.API_LAN_Info_To_String) refers to main.o(.data) for HardWare_Ver
    api_lan_data_process .o(i.API_LanSock1Send_CRC) refers to my_crc.o(i.CRC16_USB) for CRC16_USB
    api_lan_data_process .o(i.API_LanSock1Send_CRC) refers to socket.o(i.send) for send
    api_lan_data_process .o(i.API_LanSock1Send_CRC) refers to usart.o(i.API_Printf_Hex) for API_Printf_Hex
    api_lan_data_process .o(i.API_LanSock1Send_CRC) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_lan_data_process .o(i.API_Process_Lan_Data) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_lan_data_process .o(i.API_Write_SeverInfo) refers to eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) for EEPROM_SPI_ReadBuffer
    api_lan_data_process .o(i.API_Write_SeverInfo) refers to eeprom_spi.o(i.EEPROM_SPI_WriteBuffer) for EEPROM_SPI_WriteBuffer
    api_lan_data_process .o(i.API_Write_SeverInfo) refers to printfa.o(i.__0printf) for __2printf
    api_lan_data_process .o(i.API_Write_SeverInfo) refers to api_w5500.o(i.API_W5500_Reset) for API_W5500_Reset
    api_lan_data_process .o(i.API_Write_SeverInfo) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_lan_data_process .o(i.API_Write_SeverInfo) refers to api_lan_data_process .o(.data) for Cnt_20ms
    api_lan_data_process .o(i.HEXArrayToStringArray) refers to printfa.o(i.__0sprintf) for __2sprintf
    api_tnrg.o(i.API_RNG_Init) refers to eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) for EEPROM_SPI_ReadBuffer
    api_tnrg.o(i.API_RNG_Init) refers to printfa.o(i.__0printf) for __2printf
    api_tnrg.o(i.API_RNG_Init) refers to usart.o(i.API_Printf_Hex) for API_Printf_Hex
    api_tnrg.o(i.API_RNG_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    api_tnrg.o(i.API_RNG_Init) refers to gd32f4xx_trng.o(i.trng_deinit) for trng_deinit
    api_tnrg.o(i.API_RNG_Init) refers to gd32f4xx_trng.o(i.trng_enable) for trng_enable
    api_tnrg.o(i.API_RNG_Init) refers to gd32f4xx_trng.o(i.trng_flag_get) for trng_flag_get
    api_tnrg.o(i.API_RNG_Init) refers to api_tnrg.o(i.get_hard_rand_data) for get_hard_rand_data
    api_tnrg.o(i.API_RNG_Init) refers to eeprom_spi.o(i.EEPROM_SPI_WriteBuffer) for EEPROM_SPI_WriteBuffer
    api_tnrg.o(i.API_RNG_Init) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_tnrg.o(i.get_hard_rand_data) refers to gd32f4xx_trng.o(i.trng_flag_get) for trng_flag_get
    api_tnrg.o(i.get_hard_rand_data) refers to gd32f4xx_trng.o(i.trng_get_true_random_data) for trng_get_true_random_data
    api_w5500.o(i.API_Init_LAN) refers to api_w5500.o(i.API_W5500_SPI0_Init) for API_W5500_SPI0_Init
    api_w5500.o(i.API_Init_LAN) refers to api_w5500.o(i.API_W5500_GPIO_Init) for API_W5500_GPIO_Init
    api_w5500.o(i.API_Init_LAN) refers to api_w5500.o(i.API_Init_Net_Parameters) for API_Init_Net_Parameters
    api_w5500.o(i.API_Init_Net_Parameters) refers to eeprom_spi.o(i.EEPROM_SPI_ReadBuffer) for EEPROM_SPI_ReadBuffer
    api_w5500.o(i.API_Init_Net_Parameters) refers to printfa.o(i.__0printf) for __2printf
    api_w5500.o(i.API_Init_Net_Parameters) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_w5500.o(i.API_PHY_Check) refers to w5500_conf.o(i.getPHYStatus) for getPHYStatus
    api_w5500.o(i.API_PHY_Check) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    api_w5500.o(i.API_PHY_Check) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    api_w5500.o(i.API_PHY_Check) refers to user_step.o(i.SysReset_Condition) for SysReset_Condition
    api_w5500.o(i.API_PHY_Check) refers to printfa.o(i.__0printf) for __2printf
    api_w5500.o(i.API_PHY_Check) refers to api_w5500.o(.data) for i
    api_w5500.o(i.API_PHY_Check) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_w5500.o(i.API_SPI0_Send_Read_Byte) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    api_w5500.o(i.API_SPI0_Send_Read_Byte) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    api_w5500.o(i.API_SPI0_Send_Read_Byte) refers to gd32f4xx_spi.o(i.spi_i2s_data_receive) for spi_i2s_data_receive
    api_w5500.o(i.API_W5500_GPIO_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    api_w5500.o(i.API_W5500_GPIO_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    api_w5500.o(i.API_W5500_GPIO_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    api_w5500.o(i.API_W5500_Reset) refers to w5500_conf.o(i.reset_w5500) for reset_w5500
    api_w5500.o(i.API_W5500_Reset) refers to w5500_conf.o(i.PHY_check) for PHY_check
    api_w5500.o(i.API_W5500_Reset) refers to w5500_conf.o(i.set_w5500_mac) for set_w5500_mac
    api_w5500.o(i.API_W5500_Reset) refers to w5500.o(i.socket_buf_init) for socket_buf_init
    api_w5500.o(i.API_W5500_Reset) refers to dhcp.o(i.DHCP_run) for DHCP_run
    api_w5500.o(i.API_W5500_Reset) refers to main.o(.data) for dhcp_ok
    api_w5500.o(i.API_W5500_Reset) refers to dhcp.o(.data) for Conflict_flag
    api_w5500.o(i.API_W5500_Reset) refers to api_w5500.o(.RAM_D3) for Lan_Para
    api_w5500.o(i.API_W5500_Reset) refers to w5500.o(.data) for rxsize
    api_w5500.o(i.API_W5500_SPI0_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    api_w5500.o(i.API_W5500_SPI0_Init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    api_w5500.o(i.API_W5500_SPI0_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    api_w5500.o(i.API_W5500_SPI0_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    api_w5500.o(i.API_W5500_SPI0_Init) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    api_w5500.o(i.API_W5500_SPI0_Init) refers to gd32f4xx_spi.o(i.spi_init) for spi_init
    api_w5500.o(i.API_W5500_SPI0_Init) refers to gd32f4xx_spi.o(i.spi_enable) for spi_enable
    socket.o(i.close) refers to w5500_conf.o(i.IINCHIP_WRITE) for IINCHIP_WRITE
    socket.o(i.close) refers to w5500_conf.o(i.IINCHIP_READ) for IINCHIP_READ
    socket.o(i.connect) refers to w5500_conf.o(i.IINCHIP_WRITE) for IINCHIP_WRITE
    socket.o(i.connect) refers to w5500_conf.o(i.IINCHIP_READ) for IINCHIP_READ
    socket.o(i.connect) refers to w5500.o(i.getSn_IR) for getSn_IR
    socket.o(i.connect) refers to printfa.o(i.__0printf) for __2printf
    socket.o(i.connect) refers to api_w5500.o(.RAM_D3) for Lan_Para
    socket.o(i.disconnect) refers to w5500_conf.o(i.IINCHIP_WRITE) for IINCHIP_WRITE
    socket.o(i.disconnect) refers to w5500_conf.o(i.IINCHIP_READ) for IINCHIP_READ
    socket.o(i.listen) refers to w5500_conf.o(i.IINCHIP_READ) for IINCHIP_READ
    socket.o(i.listen) refers to w5500_conf.o(i.IINCHIP_WRITE) for IINCHIP_WRITE
    socket.o(i.recv) refers to w5500.o(i.recv_data_processing) for recv_data_processing
    socket.o(i.recv) refers to w5500_conf.o(i.IINCHIP_WRITE) for IINCHIP_WRITE
    socket.o(i.recv) refers to w5500_conf.o(i.IINCHIP_READ) for IINCHIP_READ
    socket.o(i.recvfrom) refers to w5500_conf.o(i.IINCHIP_READ) for IINCHIP_READ
    socket.o(i.recvfrom) refers to w5500_conf.o(i.wiz_read_buf) for wiz_read_buf
    socket.o(i.recvfrom) refers to w5500_conf.o(i.IINCHIP_WRITE) for IINCHIP_WRITE
    socket.o(i.recvfrom) refers to printfa.o(i.__0printf) for __2printf
    socket.o(i.send) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    socket.o(i.send) refers to w5500.o(i.getIINCHIP_TxMAX) for getIINCHIP_TxMAX
    socket.o(i.send) refers to w5500.o(i.getSn_TX_FSR) for getSn_TX_FSR
    socket.o(i.send) refers to w5500_conf.o(i.IINCHIP_READ) for IINCHIP_READ
    socket.o(i.send) refers to w5500.o(i.send_data_processing) for send_data_processing
    socket.o(i.send) refers to w5500_conf.o(i.IINCHIP_WRITE) for IINCHIP_WRITE
    socket.o(i.send) refers to printfa.o(i.__0printf) for __2printf
    socket.o(i.send) refers to socket.o(i.close) for close
    socket.o(i.send) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    socket.o(i.send) refers to api_w5500.o(.RAM_D3) for Lan_Para
    socket.o(i.sendto) refers to w5500.o(i.getIINCHIP_TxMAX) for getIINCHIP_TxMAX
    socket.o(i.sendto) refers to w5500_conf.o(i.IINCHIP_WRITE) for IINCHIP_WRITE
    socket.o(i.sendto) refers to w5500.o(i.send_data_processing) for send_data_processing
    socket.o(i.sendto) refers to w5500_conf.o(i.IINCHIP_READ) for IINCHIP_READ
    socket.o(i.socket) refers to socket.o(i.close) for close
    socket.o(i.socket) refers to w5500_conf.o(i.IINCHIP_WRITE) for IINCHIP_WRITE
    socket.o(i.socket) refers to w5500_conf.o(i.IINCHIP_READ) for IINCHIP_READ
    socket.o(i.socket) refers to api_w5500.o(.RAM_D3) for Lan_Para
    utility.o(i.atoi16) refers to utility.o(i.c2d) for c2d
    utility.o(i.atoi32) refers to utility.o(i.c2d) for c2d
    utility.o(i.htonl) refers to utility.o(i.swapl) for swapl
    utility.o(i.htons) refers to utility.o(i.swaps) for swaps
    utility.o(i.inet_addr_) refers to strcpy.o(.text) for strcpy
    utility.o(i.inet_addr_) refers to strtok.o(.text) for strtok
    utility.o(i.inet_addr_) refers to utility.o(i.atoi16) for atoi16
    utility.o(i.inet_ntoa) refers to memseta.o(.text) for __aeabi_memclr
    utility.o(i.inet_ntoa) refers to printfa.o(i.__0sprintf) for __2sprintf
    utility.o(i.inet_ntoa) refers to utility.o(.bss) for addr_str
    utility.o(i.inet_ntoa_pad) refers to memseta.o(.text) for __aeabi_memclr
    utility.o(i.inet_ntoa_pad) refers to printfa.o(i.__0printf) for printf
    utility.o(i.inet_ntoa_pad) refers to utility.o(.bss) for addr_str
    utility.o(i.itoa) refers to memseta.o(.text) for __aeabi_memset
    utility.o(i.mid) refers to strstr.o(.text) for strstr
    utility.o(i.mid) refers to strlen.o(.text) for strlen
    utility.o(i.mid) refers to strncpy.o(.text) for strncpy
    utility.o(i.ntohl) refers to utility.o(i.htonl) for htonl
    utility.o(i.ntohs) refers to utility.o(i.htons) for htons
    utility.o(i.validatoi) refers to utility.o(i.c2d) for c2d
    utility.o(i.validatoi) refers to utility.o(i.atoi16) for atoi16
    utility.o(i.verify_ip_address) refers to strcpy.o(.text) for strcpy
    utility.o(i.verify_ip_address) refers to strtok.o(.text) for strtok
    utility.o(i.verify_ip_address) refers to utility.o(i.validatoi) for validatoi
    w5500.o(i.clearIR) refers to w5500.o(i.getIR) for getIR
    w5500.o(i.clearIR) refers to w5500_conf.o(i.IINCHIP_WRITE) for IINCHIP_WRITE
    w5500.o(i.getGAR) refers to w5500_conf.o(i.wiz_read_buf) for wiz_read_buf
    w5500.o(i.getIINCHIP_RxMAX) refers to w5500.o(.bss) for RSIZE
    w5500.o(i.getIINCHIP_TxMAX) refers to w5500.o(.bss) for SSIZE
    w5500.o(i.getIR) refers to w5500_conf.o(i.IINCHIP_READ) for IINCHIP_READ
    w5500.o(i.getSHAR) refers to w5500_conf.o(i.wiz_read_buf) for wiz_read_buf
    w5500.o(i.getSIPR) refers to w5500_conf.o(i.wiz_read_buf) for wiz_read_buf
    w5500.o(i.getSUBR) refers to w5500_conf.o(i.wiz_read_buf) for wiz_read_buf
    w5500.o(i.getSn_IR) refers to w5500_conf.o(i.IINCHIP_READ) for IINCHIP_READ
    w5500.o(i.getSn_RX_RSR) refers to w5500_conf.o(i.IINCHIP_READ) for IINCHIP_READ
    w5500.o(i.getSn_SR) refers to w5500_conf.o(i.IINCHIP_READ) for IINCHIP_READ
    w5500.o(i.getSn_TX_FSR) refers to w5500_conf.o(i.IINCHIP_READ) for IINCHIP_READ
    w5500.o(i.iinchip_init) refers to w5500.o(i.setMR) for setMR
    w5500.o(i.recv_data_processing) refers to printfa.o(i.__0printf) for __2printf
    w5500.o(i.recv_data_processing) refers to w5500_conf.o(i.IINCHIP_READ) for IINCHIP_READ
    w5500.o(i.recv_data_processing) refers to w5500_conf.o(i.wiz_read_buf) for wiz_read_buf
    w5500.o(i.recv_data_processing) refers to w5500_conf.o(i.IINCHIP_WRITE) for IINCHIP_WRITE
    w5500.o(i.send_data_processing) refers to printfa.o(i.__0printf) for __2printf
    w5500.o(i.send_data_processing) refers to w5500_conf.o(i.IINCHIP_READ) for IINCHIP_READ
    w5500.o(i.send_data_processing) refers to w5500_conf.o(i.wiz_write_buf) for wiz_write_buf
    w5500.o(i.send_data_processing) refers to w5500_conf.o(i.IINCHIP_WRITE) for IINCHIP_WRITE
    w5500.o(i.setGAR) refers to w5500_conf.o(i.wiz_write_buf) for wiz_write_buf
    w5500.o(i.setMR) refers to w5500_conf.o(i.IINCHIP_WRITE) for IINCHIP_WRITE
    w5500.o(i.setRCR) refers to w5500_conf.o(i.IINCHIP_WRITE) for IINCHIP_WRITE
    w5500.o(i.setRTR) refers to w5500_conf.o(i.IINCHIP_WRITE) for IINCHIP_WRITE
    w5500.o(i.setSHAR) refers to w5500_conf.o(i.wiz_write_buf) for wiz_write_buf
    w5500.o(i.setSIPR) refers to w5500_conf.o(i.wiz_write_buf) for wiz_write_buf
    w5500.o(i.setSUBR) refers to w5500_conf.o(i.wiz_write_buf) for wiz_write_buf
    w5500.o(i.setSn_IR) refers to w5500_conf.o(i.IINCHIP_WRITE) for IINCHIP_WRITE
    w5500.o(i.setSn_MSS) refers to w5500_conf.o(i.IINCHIP_WRITE) for IINCHIP_WRITE
    w5500.o(i.setSn_TTL) refers to w5500_conf.o(i.IINCHIP_WRITE) for IINCHIP_WRITE
    w5500.o(i.socket_buf_init) refers to w5500_conf.o(i.IINCHIP_WRITE) for IINCHIP_WRITE
    w5500.o(i.socket_buf_init) refers to w5500.o(.bss) for SSIZE
    w5500_conf.o(i.IINCHIP_READ) refers to w5500_conf.o(i.iinchip_csoff) for iinchip_csoff
    w5500_conf.o(i.IINCHIP_READ) refers to w5500_conf.o(i.IINCHIP_SpiSendData) for IINCHIP_SpiSendData
    w5500_conf.o(i.IINCHIP_READ) refers to w5500_conf.o(i.iinchip_cson) for iinchip_cson
    w5500_conf.o(i.IINCHIP_SpiSendData) refers to api_w5500.o(i.API_SPI0_Send_Read_Byte) for API_SPI0_Send_Read_Byte
    w5500_conf.o(i.IINCHIP_WRITE) refers to w5500_conf.o(i.iinchip_csoff) for iinchip_csoff
    w5500_conf.o(i.IINCHIP_WRITE) refers to w5500_conf.o(i.IINCHIP_SpiSendData) for IINCHIP_SpiSendData
    w5500_conf.o(i.IINCHIP_WRITE) refers to w5500_conf.o(i.iinchip_cson) for iinchip_cson
    w5500_conf.o(i.PHY_check) refers to w5500_conf.o(i.getPHYStatus) for getPHYStatus
    w5500_conf.o(i.PHY_check) refers to printfa.o(i.__0printf) for __2printf
    w5500_conf.o(i.PHY_check) refers to socket.o(i.close) for close
    w5500_conf.o(i.PHY_check) refers to systick.o(i.delay_1ms) for delay_1ms
    w5500_conf.o(i.dhcp_timer_init) refers to w5500_conf.o(i.timer2_init) for timer2_init
    w5500_conf.o(i.getPHYStatus) refers to w5500_conf.o(i.IINCHIP_READ) for IINCHIP_READ
    w5500_conf.o(i.iinchip_csoff) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    w5500_conf.o(i.iinchip_cson) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    w5500_conf.o(i.ntp_timer_init) refers to w5500_conf.o(i.timer2_init) for timer2_init
    w5500_conf.o(i.reset_w5500) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    w5500_conf.o(i.reset_w5500) refers to systick.o(i.delay_1ms) for delay_1ms
    w5500_conf.o(i.reset_w5500) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    w5500_conf.o(i.reset_w5500) refers to printfa.o(i.__0printf) for __2printf
    w5500_conf.o(i.reset_w5500) refers to api_w5500.o(.RAM_D3) for Lan_Para
    w5500_conf.o(i.setKPALVTR) refers to w5500_conf.o(i.IINCHIP_WRITE) for IINCHIP_WRITE
    w5500_conf.o(i.set_w5500_mac) refers to w5500.o(i.setSHAR) for setSHAR
    w5500_conf.o(i.set_w5500_mac) refers to w5500.o(i.getSHAR) for getSHAR
    w5500_conf.o(i.set_w5500_mac) refers to printfa.o(i.__0printf) for __2printf
    w5500_conf.o(i.set_w5500_mac) refers to api_w5500.o(.RAM_D3) for Lan_Para
    w5500_conf.o(i.set_w5500_netinfo) refers to printfa.o(i.__0printf) for __2printf
    w5500_conf.o(i.set_w5500_netinfo) refers to w5500.o(i.setSUBR) for setSUBR
    w5500_conf.o(i.set_w5500_netinfo) refers to w5500.o(i.setGAR) for setGAR
    w5500_conf.o(i.set_w5500_netinfo) refers to w5500.o(i.setSIPR) for setSIPR
    w5500_conf.o(i.set_w5500_netinfo) refers to w5500.o(i.getSIPR) for getSIPR
    w5500_conf.o(i.set_w5500_netinfo) refers to w5500.o(i.getSUBR) for getSUBR
    w5500_conf.o(i.set_w5500_netinfo) refers to w5500.o(i.getGAR) for getGAR
    w5500_conf.o(i.set_w5500_netinfo) refers to w5500_conf.o(.data) for ip_from
    w5500_conf.o(i.set_w5500_netinfo) refers to api_w5500.o(.RAM_D3) for Lan_Para
    w5500_conf.o(i.timer2_isr) refers to w5500_conf.o(.data) for ms
    w5500_conf.o(i.timer2_isr) refers to dhcp.o(.data) for dhcp_time
    w5500_conf.o(i.wiz_read_buf) refers to printfa.o(i.__0printf) for __2printf
    w5500_conf.o(i.wiz_read_buf) refers to w5500_conf.o(i.iinchip_csoff) for iinchip_csoff
    w5500_conf.o(i.wiz_read_buf) refers to w5500_conf.o(i.IINCHIP_SpiSendData) for IINCHIP_SpiSendData
    w5500_conf.o(i.wiz_read_buf) refers to w5500_conf.o(i.iinchip_cson) for iinchip_cson
    w5500_conf.o(i.wiz_write_buf) refers to printfa.o(i.__0printf) for __2printf
    w5500_conf.o(i.wiz_write_buf) refers to w5500_conf.o(i.iinchip_csoff) for iinchip_csoff
    w5500_conf.o(i.wiz_write_buf) refers to w5500_conf.o(i.IINCHIP_SpiSendData) for IINCHIP_SpiSendData
    w5500_conf.o(i.wiz_write_buf) refers to w5500_conf.o(i.iinchip_cson) for iinchip_cson
    dhcp.o(i.DHCP_run) refers to dhcp.o(i.init_dhcp_client) for init_dhcp_client
    dhcp.o(i.DHCP_run) refers to w5500.o(i.getSn_SR) for getSn_SR
    dhcp.o(i.DHCP_run) refers to socket.o(i.socket) for socket
    dhcp.o(i.DHCP_run) refers to dhcp.o(i.parseDHCPMSG) for parseDHCPMSG
    dhcp.o(i.DHCP_run) refers to dhcp.o(i.send_DHCP_DISCOVER) for send_DHCP_DISCOVER
    dhcp.o(i.DHCP_run) refers to dhcp.o(i.send_DHCP_REQUEST) for send_DHCP_REQUEST
    dhcp.o(i.DHCP_run) refers to dhcp.o(i.check_DHCP_timeout) for check_DHCP_timeout
    dhcp.o(i.DHCP_run) refers to dhcp.o(i.check_DHCP_leasedIP) for check_DHCP_leasedIP
    dhcp.o(i.DHCP_run) refers to printfa.o(i.__0printf) for __2printf
    dhcp.o(i.DHCP_run) refers to dhcp.o(i.reset_DHCP_timeout) for reset_DHCP_timeout
    dhcp.o(i.DHCP_run) refers to api_w5500.o(.RAM_D3) for Lan_Para
    dhcp.o(i.DHCP_run) refers to dhcp.o(.data) for Conflict_flag
    dhcp.o(i.DHCP_run) refers to main.o(.data) for dhcp_ok
    dhcp.o(i.DHCP_timer_handler) refers to dhcp.o(.data) for dhcp_tick_cnt
    dhcp.o(i.check_DHCP_Timeout) refers to dhcp.o(i.send_DHCP_DISCOVER) for send_DHCP_DISCOVER
    dhcp.o(i.check_DHCP_Timeout) refers to dhcp.o(i.send_DHCP_REQUEST) for send_DHCP_REQUEST
    dhcp.o(i.check_DHCP_Timeout) refers to dhcp.o(i.reset_DHCP_timeout) for reset_DHCP_timeout
    dhcp.o(i.check_DHCP_Timeout) refers to dhcp.o(.data) for dhcp_retry_count
    dhcp.o(i.check_DHCP_leasedIP) refers to socket.o(i.sendto) for sendto
    dhcp.o(i.check_DHCP_leasedIP) refers to dhcp.o(i.send_DHCP_DECLINE) for send_DHCP_DECLINE
    dhcp.o(i.check_DHCP_leasedIP) refers to dhcp.o(.data) for DHCP_allocated_ip
    dhcp.o(i.check_DHCP_timeout) refers to dhcp.o(i.send_DHCP_DISCOVER) for send_DHCP_DISCOVER
    dhcp.o(i.check_DHCP_timeout) refers to dhcp.o(i.send_DHCP_REQUEST) for send_DHCP_REQUEST
    dhcp.o(i.check_DHCP_timeout) refers to dhcp.o(i.reset_DHCP_timeout) for reset_DHCP_timeout
    dhcp.o(i.check_DHCP_timeout) refers to dhcp.o(.data) for dhcp_retry_count
    dhcp.o(i.default_ip_assign) refers to w5500.o(i.setSIPR) for setSIPR
    dhcp.o(i.default_ip_assign) refers to w5500.o(i.setSUBR) for setSUBR
    dhcp.o(i.default_ip_assign) refers to w5500.o(i.setGAR) for setGAR
    dhcp.o(i.default_ip_assign) refers to dhcp.o(.data) for DHCP_allocated_ip
    dhcp.o(i.default_ip_conflict) refers to w5500.o(i.setMR) for setMR
    dhcp.o(i.default_ip_conflict) refers to w5500.o(i.setSHAR) for setSHAR
    dhcp.o(i.default_ip_conflict) refers to api_w5500.o(.RAM_D3) for Lan_Para
    dhcp.o(i.default_ip_update) refers to w5500.o(i.setMR) for setMR
    dhcp.o(i.default_ip_update) refers to dhcp.o(i.default_ip_assign) for default_ip_assign
    dhcp.o(i.default_ip_update) refers to w5500.o(i.setSHAR) for setSHAR
    dhcp.o(i.default_ip_update) refers to api_w5500.o(.RAM_D3) for Lan_Para
    dhcp.o(i.init_dhcp_client) refers to w5500.o(i.setSUBR) for setSUBR
    dhcp.o(i.init_dhcp_client) refers to w5500.o(i.setGAR) for setGAR
    dhcp.o(i.init_dhcp_client) refers to w5500.o(i.setSIPR) for setSIPR
    dhcp.o(i.init_dhcp_client) refers to printfa.o(i.__0printf) for __2printf
    dhcp.o(i.init_dhcp_client) refers to dhcp.o(.data) for dhcp_state
    dhcp.o(i.makeDHCPMSG) refers to w5500.o(i.getSHAR) for getSHAR
    dhcp.o(i.makeDHCPMSG) refers to dhcp.o(.data) for pDHCPMSG
    dhcp.o(i.makeDHCPMSG) refers to api_w5500.o(.RAM_D3) for Lan_Para
    dhcp.o(i.parseDHCPMSG) refers to w5500.o(i.getSn_RX_RSR) for getSn_RX_RSR
    dhcp.o(i.parseDHCPMSG) refers to socket.o(i.recvfrom) for recvfrom
    dhcp.o(i.parseDHCPMSG) refers to dhcp.o(.data) for pDHCPMSG
    dhcp.o(i.parseDHCPMSG) refers to api_w5500.o(.RAM_D3) for Lan_Para
    dhcp.o(i.reset_DHCP_timeout) refers to dhcp.o(.data) for dhcp_time
    dhcp.o(i.send_DHCP_DECLINE) refers to dhcp.o(i.makeDHCPMSG) for makeDHCPMSG
    dhcp.o(i.send_DHCP_DECLINE) refers to socket.o(i.sendto) for sendto
    dhcp.o(i.send_DHCP_DECLINE) refers to dhcp.o(.data) for pDHCPMSG
    dhcp.o(i.send_DHCP_DECLINE) refers to api_w5500.o(.RAM_D3) for Lan_Para
    dhcp.o(i.send_DHCP_DISCOVER) refers to dhcp.o(i.makeDHCPMSG) for makeDHCPMSG
    dhcp.o(i.send_DHCP_DISCOVER) refers to printfa.o(i.__0sprintf) for __2sprintf
    dhcp.o(i.send_DHCP_DISCOVER) refers to strlen.o(.text) for strlen
    dhcp.o(i.send_DHCP_DISCOVER) refers to strcpy.o(.text) for strcpy
    dhcp.o(i.send_DHCP_DISCOVER) refers to socket.o(i.sendto) for sendto
    dhcp.o(i.send_DHCP_DISCOVER) refers to dhcp.o(.data) for pDHCPMSG
    dhcp.o(i.send_DHCP_DISCOVER) refers to api_w5500.o(.RAM_D3) for Lan_Para
    dhcp.o(i.send_DHCP_REQUEST) refers to dhcp.o(i.makeDHCPMSG) for makeDHCPMSG
    dhcp.o(i.send_DHCP_REQUEST) refers to printfa.o(i.__0sprintf) for __2sprintf
    dhcp.o(i.send_DHCP_REQUEST) refers to strlen.o(.text) for strlen
    dhcp.o(i.send_DHCP_REQUEST) refers to strcpy.o(.text) for strcpy
    dhcp.o(i.send_DHCP_REQUEST) refers to socket.o(i.sendto) for sendto
    dhcp.o(i.send_DHCP_REQUEST) refers to dhcp.o(.data) for dhcp_state
    dhcp.o(i.send_DHCP_REQUEST) refers to api_w5500.o(.RAM_D3) for Lan_Para
    dhcp.o(.data) refers to dhcp.o(.bss) for EXTERN_DHCPBUF
    dhcp.o(.data) refers to dhcp.o(i.default_ip_assign) for default_ip_assign
    dhcp.o(.data) refers to dhcp.o(i.default_ip_update) for default_ip_update
    dhcp.o(.data) refers to dhcp.o(i.default_ip_conflict) for default_ip_conflict
    tcp_client.o(i.do_tcp_client) refers to w5500.o(i.getSn_SR) for getSn_SR
    tcp_client.o(i.do_tcp_client) refers to socket.o(i.socket) for socket
    tcp_client.o(i.do_tcp_client) refers to socket.o(i.connect) for connect
    tcp_client.o(i.do_tcp_client) refers to w5500.o(i.getSn_IR) for getSn_IR
    tcp_client.o(i.do_tcp_client) refers to w5500.o(i.setSn_IR) for setSn_IR
    tcp_client.o(i.do_tcp_client) refers to w5500.o(i.getSn_RX_RSR) for getSn_RX_RSR
    tcp_client.o(i.do_tcp_client) refers to socket.o(i.recv) for recv
    tcp_client.o(i.do_tcp_client) refers to printfa.o(i.__0printf) for __2printf
    tcp_client.o(i.do_tcp_client) refers to socket.o(i.disconnect) for disconnect
    tcp_client.o(i.do_tcp_client) refers to socket.o(i.close) for close
    tcp_client.o(i.do_tcp_client) refers to api_w5500.o(.RAM_D3) for Lan_Para
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    strtok.o(.text) refers to strtok.o(.data) for .data
    strtok_r.o(.text) refers to strtok_r.o(.data) for .data
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    malloc.o(i.free) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to startup_gd32f450_470.o(HEAP) for __heap_base
    mallocr.o(i.__free$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.__malloc$realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocr.o(i.__malloc$realloc) refers to startup_gd32f450_470.o(HEAP) for __heap_base
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocr.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.realloc) refers to mallocr.o(i.__free$realloc) for __free$realloc
    mallocr.o(i.realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.realloc) refers to mallocr.o(i.__malloc$realloc) for __malloc$realloc
    mallocr.o(i.realloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to startup_gd32f450_470.o(HEAP) for __heap_base
    malloca.o(i.__free$memalign) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__malloc$memalign) refers to malloca.o(i.__aligned_malloc) for __aligned_malloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocra.o(i.__aligned_malloc$realloc) refers to startup_gd32f450_470.o(HEAP) for __heap_base
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__free$realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__malloc$realloc$memalign) refers to mallocra.o(i.__aligned_malloc$realloc) for __aligned_malloc$realloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__free$realloc$memalign) for __free$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__malloc$realloc$memalign) for __malloc$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocra.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    atoi.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    atoi.o(.text) refers to strtol.o(.text) for strtol
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_gd32f450_470.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_gd32f450_470.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    strtol.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    strtol.o(.text) refers to _strtoul.o(.text) for _strtoul
    strtol.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    ctype_o.o(.text) refers to ctype_o.o(.constdata) for .constdata
    ctype_o.o(.constdata) refers to ctype_o.o(.constdata) for __ctype_table
    isalnum_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isalpha_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isblank_o.o(.text) refers to ctype_o.o(.constdata) for __ctype_table
    iscntrl_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isdigit_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isgraph_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    islower_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isprint_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    ispunct_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isspace_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isupper_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isxdigit_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    _strtoul.o(.text) refers to _chval.o(.text) for _chval
    _strtoul.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr


==============================================================================

Removing Unused input sections from the image.

    Removing startup_gd32f450_470.o(HEAP), (12288 bytes).
    Removing gd32f4xx_adc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_adc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_adc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_adc.o(i.adc_calibration_enable), (42 bytes).
    Removing gd32f4xx_adc.o(i.adc_channel_16_to_18), (96 bytes).
    Removing gd32f4xx_adc.o(i.adc_channel_length_config), (82 bytes).
    Removing gd32f4xx_adc.o(i.adc_clock_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_data_alignment_config), (22 bytes).
    Removing gd32f4xx_adc.o(i.adc_deinit), (20 bytes).
    Removing gd32f4xx_adc.o(i.adc_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_discontinuous_mode_config), (82 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_mode_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_mode_enable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_request_after_last_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_request_after_last_enable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_enable), (18 bytes).
    Removing gd32f4xx_adc.o(i.adc_end_of_conversion_config), (34 bytes).
    Removing gd32f4xx_adc.o(i.adc_external_trigger_config), (52 bytes).
    Removing gd32f4xx_adc.o(i.adc_external_trigger_source_config), (48 bytes).
    Removing gd32f4xx_adc.o(i.adc_flag_clear), (8 bytes).
    Removing gd32f4xx_adc.o(i.adc_flag_get), (14 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_channel_config), (124 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_channel_offset_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_data_read), (46 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_software_startconv_flag_get), (16 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_disable), (66 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_enable), (66 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_flag_clear), (8 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_flag_get), (112 bytes).
    Removing gd32f4xx_adc.o(i.adc_oversample_mode_config), (58 bytes).
    Removing gd32f4xx_adc.o(i.adc_oversample_mode_disable), (14 bytes).
    Removing gd32f4xx_adc.o(i.adc_oversample_mode_enable), (14 bytes).
    Removing gd32f4xx_adc.o(i.adc_regular_channel_config), (172 bytes).
    Removing gd32f4xx_adc.o(i.adc_regular_data_read), (8 bytes).
    Removing gd32f4xx_adc.o(i.adc_regular_software_startconv_flag_get), (16 bytes).
    Removing gd32f4xx_adc.o(i.adc_resolution_config), (16 bytes).
    Removing gd32f4xx_adc.o(i.adc_software_trigger_enable), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_special_function_config), (90 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_delay_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_dma_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_dma_request_after_last_disable), (24 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_dma_request_after_last_enable), (24 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_mode_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_regular_data_read), (12 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_disable), (50 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_group_channel_enable), (64 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_single_channel_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_single_channel_enable), (24 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_threshold_config), (14 bytes).
    Removing gd32f4xx_can.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_can.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_can.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_can.o(i.can1_filter_start_bank), (56 bytes).
    Removing gd32f4xx_can.o(i.can_debug_freeze_disable), (44 bytes).
    Removing gd32f4xx_can.o(i.can_debug_freeze_enable), (44 bytes).
    Removing gd32f4xx_can.o(i.can_deinit), (52 bytes).
    Removing gd32f4xx_can.o(i.can_error_get), (12 bytes).
    Removing gd32f4xx_can.o(i.can_fifo_release), (32 bytes).
    Removing gd32f4xx_can.o(i.can_filter_init), (272 bytes).
    Removing gd32f4xx_can.o(i.can_flag_clear), (16 bytes).
    Removing gd32f4xx_can.o(i.can_flag_get), (30 bytes).
    Removing gd32f4xx_can.o(i.can_init), (286 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_disable), (8 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_enable), (8 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_flag_get), (116 bytes).
    Removing gd32f4xx_can.o(i.can_message_receive), (228 bytes).
    Removing gd32f4xx_can.o(i.can_message_transmit), (336 bytes).
    Removing gd32f4xx_can.o(i.can_receive_error_number_get), (8 bytes).
    Removing gd32f4xx_can.o(i.can_receive_message_length_get), (26 bytes).
    Removing gd32f4xx_can.o(i.can_struct_para_init), (164 bytes).
    Removing gd32f4xx_can.o(i.can_time_trigger_mode_disable), (48 bytes).
    Removing gd32f4xx_can.o(i.can_time_trigger_mode_enable), (48 bytes).
    Removing gd32f4xx_can.o(i.can_transmission_stop), (80 bytes).
    Removing gd32f4xx_can.o(i.can_transmit_error_number_get), (10 bytes).
    Removing gd32f4xx_can.o(i.can_transmit_states), (124 bytes).
    Removing gd32f4xx_can.o(i.can_wakeup), (48 bytes).
    Removing gd32f4xx_can.o(i.can_working_mode_set), (168 bytes).
    Removing gd32f4xx_crc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_crc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_crc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_crc.o(i.crc_block_data_calculate), (36 bytes).
    Removing gd32f4xx_crc.o(i.crc_data_register_read), (12 bytes).
    Removing gd32f4xx_crc.o(i.crc_data_register_reset), (20 bytes).
    Removing gd32f4xx_crc.o(i.crc_deinit), (24 bytes).
    Removing gd32f4xx_crc.o(i.crc_free_data_register_read), (12 bytes).
    Removing gd32f4xx_crc.o(i.crc_free_data_register_write), (12 bytes).
    Removing gd32f4xx_crc.o(i.crc_single_data_calculate), (16 bytes).
    Removing gd32f4xx_ctc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_ctc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_ctc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_clock_limit_value_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_capture_value_read), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_direction_read), (24 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_disable), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_enable), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_reload_value_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_reload_value_read), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_deinit), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_flag_clear), (36 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_flag_get), (24 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_hardware_trim_mode_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_disable), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_enable), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_flag_clear), (36 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_flag_get), (56 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_irc48m_trim_value_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_irc48m_trim_value_read), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_refsource_polarity_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_refsource_prescaler_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_refsource_signal_select), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_software_refsource_pulse_generate), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_usbsof_signal_select), (28 bytes).
    Removing gd32f4xx_dac.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dac.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dac.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_data_set), (64 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_disable), (24 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_enable), (24 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_interrupt_disable), (24 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_interrupt_enable), (24 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_output_buffer_disable), (24 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_output_buffer_enable), (24 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_software_trigger_disable), (20 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_software_trigger_enable), (20 bytes).
    Removing gd32f4xx_dac.o(i.dac_data_set), (88 bytes).
    Removing gd32f4xx_dac.o(i.dac_deinit), (20 bytes).
    Removing gd32f4xx_dac.o(i.dac_disable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_dma_disable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_dma_enable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_enable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_flag_clear), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_flag_get), (40 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_disable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_enable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_flag_clear), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_flag_get), (68 bytes).
    Removing gd32f4xx_dac.o(i.dac_lfsr_noise_config), (52 bytes).
    Removing gd32f4xx_dac.o(i.dac_output_buffer_disable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_output_buffer_enable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_output_value_get), (28 bytes).
    Removing gd32f4xx_dac.o(i.dac_software_trigger_disable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_software_trigger_enable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_triangle_noise_config), (52 bytes).
    Removing gd32f4xx_dac.o(i.dac_trigger_disable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_trigger_enable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_trigger_source_config), (52 bytes).
    Removing gd32f4xx_dac.o(i.dac_wave_bit_width_config), (52 bytes).
    Removing gd32f4xx_dac.o(i.dac_wave_mode_config), (52 bytes).
    Removing gd32f4xx_dbg.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dbg.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dbg.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_deinit), (16 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_id_get), (12 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_low_power_disable), (16 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_low_power_enable), (16 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_periph_disable), (32 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_periph_enable), (32 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_trace_pin_disable), (20 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_trace_pin_enable), (20 bytes).
    Removing gd32f4xx_dci.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dci.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dci.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_dci.o(i.dci_capture_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_capture_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_crop_window_config), (24 bytes).
    Removing gd32f4xx_dci.o(i.dci_crop_window_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_crop_window_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_data_read), (12 bytes).
    Removing gd32f4xx_dci.o(i.dci_deinit), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_embedded_sync_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_embedded_sync_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_flag_get), (36 bytes).
    Removing gd32f4xx_dci.o(i.dci_init), (52 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_disable), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_enable), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_dci.o(i.dci_jpeg_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_jpeg_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_sync_codes_config), (24 bytes).
    Removing gd32f4xx_dci.o(i.dci_sync_codes_unmask_config), (24 bytes).
    Removing gd32f4xx_dma.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dma.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dma.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_dma.o(i.dma_channel_disable), (32 bytes).
    Removing gd32f4xx_dma.o(i.dma_channel_enable), (32 bytes).
    Removing gd32f4xx_dma.o(i.dma_channel_subperipheral_select), (38 bytes).
    Removing gd32f4xx_dma.o(i.dma_circulation_disable), (32 bytes).
    Removing gd32f4xx_dma.o(i.dma_circulation_enable), (32 bytes).
    Removing gd32f4xx_dma.o(i.dma_deinit), (164 bytes).
    Removing gd32f4xx_dma.o(i.dma_fifo_status_get), (20 bytes).
    Removing gd32f4xx_dma.o(i.dma_flag_clear), (62 bytes).
    Removing gd32f4xx_dma.o(i.dma_flag_get), (76 bytes).
    Removing gd32f4xx_dma.o(i.dma_flow_controller_config), (64 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_disable), (62 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_enable), (62 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_flag_clear), (62 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_flag_get), (516 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_address_config), (32 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_address_generation_config), (64 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_burst_beats_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_width_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_multi_data_mode_init), (356 bytes).
    Removing gd32f4xx_dma.o(i.dma_multi_data_para_struct_init), (40 bytes).
    Removing gd32f4xx_dma.o(i.dma_periph_address_config), (16 bytes).
    Removing gd32f4xx_dma.o(i.dma_periph_burst_beats_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_periph_width_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_peripheral_address_generation_config), (126 bytes).
    Removing gd32f4xx_dma.o(i.dma_priority_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_single_data_mode_init), (344 bytes).
    Removing gd32f4xx_dma.o(i.dma_single_data_para_struct_init), (34 bytes).
    Removing gd32f4xx_dma.o(i.dma_switch_buffer_mode_config), (76 bytes).
    Removing gd32f4xx_dma.o(i.dma_switch_buffer_mode_enable), (66 bytes).
    Removing gd32f4xx_dma.o(i.dma_transfer_direction_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_transfer_number_config), (16 bytes).
    Removing gd32f4xx_dma.o(i.dma_transfer_number_get), (16 bytes).
    Removing gd32f4xx_dma.o(i.dma_using_memory_get), (28 bytes).
    Removing gd32f4xx_enet.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_enet.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_enet.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_enet.o(i.enet_address_filter_config), (32 bytes).
    Removing gd32f4xx_enet.o(i.enet_address_filter_disable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_address_filter_enable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_current_desc_address_get), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_debug_status_get), (108 bytes).
    Removing gd32f4xx_enet.o(i.enet_default_init), (104 bytes).
    Removing gd32f4xx_enet.o(i.enet_deinit), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_delay), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_flag_clear), (8 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_flag_get), (14 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_flag_set), (8 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_information_get), (100 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_select_normal_mode), (20 bytes).
    Removing gd32f4xx_enet.o(i.enet_descriptors_chain_init), (200 bytes).
    Removing gd32f4xx_enet.o(i.enet_descriptors_ring_init), (236 bytes).
    Removing gd32f4xx_enet.o(i.enet_disable), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_dma_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_dma_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_dmaprocess_resume), (28 bytes).
    Removing gd32f4xx_enet.o(i.enet_dmaprocess_state_get), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_enable), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_flag_clear), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_flag_get), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_fliter_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_fliter_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_flowcontrol_feature_disable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_flowcontrol_feature_enable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_flowcontrol_threshold_config), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_forward_feature_disable), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_forward_feature_enable), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_frame_receive), (248 bytes).
    Removing gd32f4xx_enet.o(i.enet_frame_transmit), (204 bytes).
    Removing gd32f4xx_enet.o(i.enet_init), (868 bytes).
    Removing gd32f4xx_enet.o(i.enet_initpara_config), (356 bytes).
    Removing gd32f4xx_enet.o(i.enet_initpara_reset), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_disable), (72 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_enable), (72 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_flag_clear), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_flag_get), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_mac_address_get), (60 bytes).
    Removing gd32f4xx_enet.o(i.enet_mac_address_set), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_missed_frame_counter_get), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_counters_get), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_counters_preset_config), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_counters_reset), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_feature_disable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_feature_enable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_pauseframe_config), (44 bytes).
    Removing gd32f4xx_enet.o(i.enet_pauseframe_detect_config), (28 bytes).
    Removing gd32f4xx_enet.o(i.enet_pauseframe_generate), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_phy_config), (216 bytes).
    Removing gd32f4xx_enet.o(i.enet_phy_write_read), (156 bytes).
    Removing gd32f4xx_enet.o(i.enet_phyloopback_disable), (50 bytes).
    Removing gd32f4xx_enet.o(i.enet_phyloopback_enable), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_expected_time_config), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init), (236 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init), (280 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_pps_output_frequency_config), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_subsecond_increment_config), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_system_time_get), (32 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_timestamp_addend_config), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_timestamp_function_config), (256 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_timestamp_update_config), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptpframe_receive_normal_mode), (340 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptpframe_transmit_normal_mode), (444 bytes).
    Removing gd32f4xx_enet.o(i.enet_registers_get), (56 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_desc_delay_receive_complete_interrupt), (20 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_desc_immediate_receive_complete_interrupt), (10 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_disable), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_enable), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_rxframe_drop), (172 bytes).
    Removing gd32f4xx_enet.o(i.enet_rxframe_size_get), (152 bytes).
    Removing gd32f4xx_enet.o(i.enet_rxprocess_check_recovery), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_software_reset), (60 bytes).
    Removing gd32f4xx_enet.o(i.enet_transmit_checksum_config), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_tx_disable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_tx_enable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_txfifo_flush), (52 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_filter_config), (28 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_filter_register_pointer_reset), (20 bytes).
    Removing gd32f4xx_enet.o(.bss), (15460 bytes).
    Removing gd32f4xx_enet.o(.constdata), (116 bytes).
    Removing gd32f4xx_enet.o(.data), (20 bytes).
    Removing gd32f4xx_exmc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_exmc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_exmc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_ecc_get), (12 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_flag_clear), (52 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_flag_get), (52 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_disable), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_enable), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_flag_clear), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_flag_get), (72 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_deinit), (42 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_disable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_ecc_config), (48 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_enable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_init), (172 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_struct_para_init), (54 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_consecutive_clock_config), (42 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_deinit), (36 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_disable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_enable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_init), (228 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_page_size_config), (40 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_struct_para_init), (106 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_deinit), (36 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_disable), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_enable), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_init), (188 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_struct_para_init), (60 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_autorefresh_number_set), (32 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_bankstatus_get), (32 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_command_config), (28 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_deinit), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_init), (280 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_readsample_config), (32 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_readsample_enable), (44 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_refresh_count_set), (36 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_struct_para_init), (66 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_write_protection_config), (64 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_deinit), (44 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_high_id_get), (12 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_init), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_low_id_get), (12 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_read_command_set), (28 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_read_id_command_send), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_send_command_state_get), (48 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_struct_para_init), (20 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_write_cmd_send), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_write_command_set), (28 bytes).
    Removing gd32f4xx_exti.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_exti.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_exti.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_exti.o(i.exti_deinit), (28 bytes).
    Removing gd32f4xx_exti.o(i.exti_event_disable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_event_enable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_flag_clear), (12 bytes).
    Removing gd32f4xx_exti.o(i.exti_flag_get), (24 bytes).
    Removing gd32f4xx_exti.o(i.exti_init), (188 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_disable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_enable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_flag_clear), (12 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_flag_get), (36 bytes).
    Removing gd32f4xx_exti.o(i.exti_software_interrupt_disable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_software_interrupt_enable), (16 bytes).
    Removing gd32f4xx_fmc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_fmc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_fmc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_bank0_erase), (68 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_bank1_erase), (68 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_byte_program), (80 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_flag_clear), (12 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_flag_get), (24 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_halfword_program), (84 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_disable), (16 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_enable), (16 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_flag_clear), (12 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_flag_get), (64 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_lock), (20 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_mass_erase), (72 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_page_erase), (124 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_ready_wait), (32 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_sector_erase), (96 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_state_get), (76 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_unlock), (36 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_word_program), (84 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_wscnt_set), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_boot_mode_config), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp0_get), (32 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp1_get), (32 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp_disable), (96 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp_enable), (104 bytes).
    Removing gd32f4xx_fmc.o(i.ob_erase), (76 bytes).
    Removing gd32f4xx_fmc.o(i.ob_lock), (20 bytes).
    Removing gd32f4xx_fmc.o(i.ob_security_protection_config), (40 bytes).
    Removing gd32f4xx_fmc.o(i.ob_spc_get), (28 bytes).
    Removing gd32f4xx_fmc.o(i.ob_start), (20 bytes).
    Removing gd32f4xx_fmc.o(i.ob_unlock), (36 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_bor_threshold), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_bor_threshold_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_write), (52 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection0_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection1_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection_disable), (72 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection_enable), (72 bytes).
    Removing gd32f4xx_fwdgt.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_fwdgt.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_fwdgt.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_config), (104 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_counter_reload), (16 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_enable), (16 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_flag_get), (24 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_write_disable), (12 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_write_enable), (16 bytes).
    Removing gd32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_gpio.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_bit_toggle), (4 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_bit_write), (10 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_deinit), (206 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_input_bit_get), (16 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_input_port_get), (8 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_output_bit_get), (16 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_output_port_get), (8 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_pin_lock), (18 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_port_toggle), (8 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_port_write), (4 bytes).
    Removing gd32f4xx_i2c.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_i2c.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_i2c.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_ack_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_ackpos_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_analog_noise_filter_disable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_analog_noise_filter_enable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_clock_config), (228 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_data_receive), (8 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_data_transmit), (6 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_deinit), (88 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_digital_noise_filter_config), (8 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_disable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dma_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dma_last_transfer_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dualaddr_disable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dualaddr_enable), (12 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_enable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_flag_clear), (40 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_flag_get), (30 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_disable), (26 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_enable), (26 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_flag_clear), (44 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_flag_get), (92 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_master_addressing), (20 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_mode_addr_config), (28 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_pec_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_pec_transfer_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_pec_value_get), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_disable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_enable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_timeout_disable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_timeout_enable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_slave_response_to_gcall_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_smbus_alert_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_smbus_arp_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_smbus_type_config), (24 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_software_reset_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_start_on_bus), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_stop_on_bus), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_stretch_scl_low_config), (16 bytes).
    Removing gd32f4xx_ipa.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_ipa.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_ipa.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_init), (164 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_lut_init), (100 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_lut_loading_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_struct_para_init), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_deinit), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_destination_init), (316 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_destination_struct_para_init), (22 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_flag_clear), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_flag_get), (24 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_init), (164 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_lut_init), (100 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_lut_loading_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_struct_para_init), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_inter_timer_config), (36 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_disable), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_enable), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interval_clock_num_config), (28 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_line_mark_config), (24 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_pixel_format_convert_mode_set), (28 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_hangup_disable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_hangup_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_stop_disable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_stop_enable), (20 bytes).
    Removing gd32f4xx_iref.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_iref.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_iref.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_iref.o(i.iref_deinit), (20 bytes).
    Removing gd32f4xx_iref.o(i.iref_disable), (20 bytes).
    Removing gd32f4xx_iref.o(i.iref_enable), (20 bytes).
    Removing gd32f4xx_iref.o(i.iref_mode_set), (28 bytes).
    Removing gd32f4xx_iref.o(i.iref_precision_trim_value_set), (28 bytes).
    Removing gd32f4xx_iref.o(i.iref_sink_set), (28 bytes).
    Removing gd32f4xx_iref.o(i.iref_step_data_config), (28 bytes).
    Removing gd32f4xx_misc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_misc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_misc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_misc.o(i.nvic_irq_disable), (24 bytes).
    Removing gd32f4xx_misc.o(i.nvic_irq_enable), (196 bytes).
    Removing gd32f4xx_misc.o(i.nvic_priority_group_set), (20 bytes).
    Removing gd32f4xx_misc.o(i.nvic_vector_table_set), (24 bytes).
    Removing gd32f4xx_misc.o(i.system_lowpower_reset), (16 bytes).
    Removing gd32f4xx_misc.o(i.system_lowpower_set), (16 bytes).
    Removing gd32f4xx_misc.o(i.systick_clksource_set), (40 bytes).
    Removing gd32f4xx_pmu.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_pmu.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_pmu.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_backup_ldo_config), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_backup_write_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_backup_write_enable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_deinit), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_flag_clear), (48 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_flag_get), (24 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_highdriver_mode_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_highdriver_mode_enable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_highdriver_switch_select), (44 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_ldo_output_select), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lowdriver_mode_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lowdriver_mode_enable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lowpower_driver_config), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lvd_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lvd_select), (48 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_normalpower_driver_config), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_to_deepsleepmode), (244 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_to_sleepmode), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_to_standbymode), (56 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_wakeup_pin_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_wakeup_pin_enable), (20 bytes).
    Removing gd32f4xx_pmu.o(.bss), (16 bytes).
    Removing gd32f4xx_rcu.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_rcu.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_rcu.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ahb_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_all_reset_flag_clear), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_apb1_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_apb2_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_bkp_reset_disable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_bkp_reset_enable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ck48m_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ckout0_config), (28 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ckout1_config), (28 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_deepsleep_voltage_set), (16 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_deinit), (140 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_flag_get), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_hxtal_clock_monitor_disable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_hxtal_clock_monitor_enable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_i2s_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_disable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_enable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_flag_clear), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_flag_get), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_irc16m_adjust_value_set), (28 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_lxtal_drive_capability_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_bypass_mode_disable), (116 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_bypass_mode_enable), (116 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_off), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_on), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_stab_wait), (348 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_periph_clock_disable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_periph_clock_sleep_disable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_periph_clock_sleep_enable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_pll48m_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_pll_config), (132 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_plli2s_config), (44 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_pllsai_config), (72 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_rtc_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_rtc_div_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_spread_spectrum_config), (32 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_spread_spectrum_disable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_spread_spectrum_enable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_system_clock_source_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_system_clock_source_get), (16 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_timer_clock_prescaler_config), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_tli_clock_div_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_voltage_key_unlock), (16 bytes).
    Removing gd32f4xx_rtc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_rtc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_rtc.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_config), (100 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_disable), (128 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_enable), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_get), (68 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_output_config), (84 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_subsecond_config), (52 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_subsecond_get), (32 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_bypass_shadow_disable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_bypass_shadow_enable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_calibration_output_config), (48 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_coarse_calibration_config), (116 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_current_time_get), (100 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_deinit), (204 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_flag_clear), (16 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_flag_get), (20 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_hour_adjust), (36 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_init), (196 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_init_mode_enter), (72 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_init_mode_exit), (20 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_interrupt_disable), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_interrupt_enable), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_refclock_detection_disable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_refclock_detection_enable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_register_sync_wait), (96 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_second_adjust), (108 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_smooth_calibration_config), (80 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_subsecond_get), (20 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_tamper0_pin_map), (28 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_tamper_disable), (16 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_tamper_enable), (200 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_disable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_enable), (48 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_get), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_pin_map), (28 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_subsecond_get), (12 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_clock_set), (92 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_disable), (84 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_enable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_timer_get), (12 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_timer_set), (76 bytes).
    Removing gd32f4xx_sdio.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_sdio.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_sdio.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_bus_mode_set), (28 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_completion_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_completion_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_interrupt_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_interrupt_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_clock_config), (52 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_clock_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_clock_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_command_index_get), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_command_response_config), (56 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_csm_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_csm_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_data_config), (60 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_data_counter_get), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_data_read), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_data_transfer_config), (28 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_data_write), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_deinit), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_dma_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_dma_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_dsm_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_dsm_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_fifo_counter_get), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_flag_clear), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_flag_get), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_hardware_clock_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_hardware_clock_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_interrupt_disable), (16 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_interrupt_enable), (16 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_interrupt_flag_clear), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_interrupt_flag_get), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_operation_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_operation_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_power_state_get), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_power_state_set), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_readwait_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_readwait_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_readwait_type_set), (40 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_response_get), (60 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_stop_readwait_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_stop_readwait_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_suspend_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_suspend_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_wait_type_set), (28 bytes).
    Removing gd32f4xx_spi.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_spi.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_spi.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_spi.o(i.i2s_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.i2s_enable), (10 bytes).
    Removing gd32f4xx_spi.o(i.i2s_full_duplex_mode_config), (48 bytes).
    Removing gd32f4xx_spi.o(i.i2s_init), (28 bytes).
    Removing gd32f4xx_spi.o(i.i2s_psc_config), (292 bytes).
    Removing gd32f4xx_spi.o(i.qspi_disable), (14 bytes).
    Removing gd32f4xx_spi.o(i.qspi_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.qspi_io23_output_disable), (14 bytes).
    Removing gd32f4xx_spi.o(i.qspi_io23_output_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.qspi_read_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.qspi_write_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_bidirectional_transfer_config), (26 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_error_clear), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_get), (16 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_next), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_off), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_on), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_polynomial_get), (8 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_polynomial_set), (4 bytes).
    Removing gd32f4xx_spi.o(i.spi_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_dma_disable), (22 bytes).
    Removing gd32f4xx_spi.o(i.spi_dma_enable), (22 bytes).
    Removing gd32f4xx_spi.o(i.spi_enable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_data_frame_format_config), (16 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_deinit), (172 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_interrupt_disable), (48 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_interrupt_enable), (48 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_interrupt_flag_get), (112 bytes).
    Removing gd32f4xx_spi.o(i.spi_init), (50 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_internal_high), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_internal_low), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_output_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_output_enable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_struct_para_init), (18 bytes).
    Removing gd32f4xx_spi.o(i.spi_ti_mode_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_ti_mode_enable), (10 bytes).
    Removing gd32f4xx_syscfg.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_syscfg.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_syscfg.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_bootmode_config), (28 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_compensation_config), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_deinit), (20 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_enet_phy_interface_config), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_exmc_swap_config), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_exti_line_config), (172 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_flag_get), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_fmc_swap_config), (24 bytes).
    Removing gd32f4xx_timer.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_timer.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_timer.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_timer.o(i.timer_auto_reload_shadow_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_auto_reload_shadow_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_automatic_output_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_automatic_output_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_autoreload_value_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_config), (30 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_struct_para_init), (18 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_capture_value_register_read), (42 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_complementary_output_polarity_config), (70 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_complementary_output_state_config), (70 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_control_shadow_config), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_control_shadow_update_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_dma_request_source_select), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_input_struct_para_init), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_clear_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_config), (492 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_fast_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_mode_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_polarity_config), (92 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_pulse_value_config), (38 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_shadow_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_state_config), (92 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_struct_para_init), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_remap_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_alignment), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_down_direction), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_read), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_up_direction), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_value_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_deinit), (388 bytes).
    Removing gd32f4xx_timer.o(i.timer_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_dma_disable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_dma_enable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_dma_transfer_config), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_event_software_generate), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_clock_mode0_config), (40 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_clock_mode1_config), (32 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_clock_mode1_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_trigger_as_external_clock_config), (166 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_trigger_config), (30 bytes).
    Removing gd32f4xx_timer.o(i.timer_flag_clear), (6 bytes).
    Removing gd32f4xx_timer.o(i.timer_flag_get), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_hall_mode_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_init), (152 bytes).
    Removing gd32f4xx_timer.o(i.timer_input_capture_config), (326 bytes).
    Removing gd32f4xx_timer.o(i.timer_input_pwm_capture_config), (356 bytes).
    Removing gd32f4xx_timer.o(i.timer_input_trigger_source_select), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_internal_clock_config), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_internal_trigger_as_external_clock_config), (32 bytes).
    Removing gd32f4xx_timer.o(i.timer_interrupt_disable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_interrupt_enable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_master_output_trigger_source_select), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_master_slave_mode_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_output_value_selection_config), (34 bytes).
    Removing gd32f4xx_timer.o(i.timer_prescaler_config), (14 bytes).
    Removing gd32f4xx_timer.o(i.timer_prescaler_read), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_primary_output_config), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_quadrature_decoder_mode_config), (64 bytes).
    Removing gd32f4xx_timer.o(i.timer_repetition_value_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_single_pulse_mode_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_slave_mode_select), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_struct_para_init), (22 bytes).
    Removing gd32f4xx_timer.o(i.timer_update_event_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_update_event_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_update_source_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_write_chxval_register_config), (34 bytes).
    Removing gd32f4xx_tli.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_tli.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_tli.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_tli.o(i.tli_color_key_disable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_color_key_enable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_color_key_init), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_current_pos_get), (12 bytes).
    Removing gd32f4xx_tli.o(i.tli_deinit), (20 bytes).
    Removing gd32f4xx_tli.o(i.tli_disable), (20 bytes).
    Removing gd32f4xx_tli.o(i.tli_dither_config), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_enable), (20 bytes).
    Removing gd32f4xx_tli.o(i.tli_flag_get), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_init), (188 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_disable), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_enable), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_flag_get), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_disable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_enable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_init), (152 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_struct_para_init), (48 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_window_offset_modify), (228 bytes).
    Removing gd32f4xx_tli.o(i.tli_line_mark_set), (24 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_disable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_enable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_init), (28 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_struct_para_init), (12 bytes).
    Removing gd32f4xx_tli.o(i.tli_reload_config), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_struct_para_init), (34 bytes).
    Removing gd32f4xx_trng.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_trng.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_trng.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_trng.o(i.trng_deinit), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_disable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_enable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_flag_get), (24 bytes).
    Removing gd32f4xx_trng.o(i.trng_get_true_random_data), (12 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_disable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_enable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_usart.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_usart.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_usart.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_usart.o(i.usart_address_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_block_length_config), (28 bytes).
    Removing gd32f4xx_usart.o(i.usart_break_frame_coherence_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_data_first_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_dma_receive_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_dma_transmit_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_flag_clear), (26 bytes).
    Removing gd32f4xx_usart.o(i.usart_guard_time_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_halfduplex_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_halfduplex_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_hardware_flow_coherence_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_hardware_flow_cts_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_hardware_flow_rts_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_interrupt_disable), (26 bytes).
    Removing gd32f4xx_usart.o(i.usart_interrupt_enable), (26 bytes).
    Removing gd32f4xx_usart.o(i.usart_invert_config), (104 bytes).
    Removing gd32f4xx_usart.o(i.usart_irda_lowpower_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_irda_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_irda_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_lin_break_detection_length_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_lin_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_lin_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_mute_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_mute_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_mute_mode_wakeup_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_oversample_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_parity_check_coherence_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_parity_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_prescaler_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_receiver_timeout_disable), (14 bytes).
    Removing gd32f4xx_usart.o(i.usart_receiver_timeout_enable), (14 bytes).
    Removing gd32f4xx_usart.o(i.usart_receiver_timeout_threshold_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_sample_bit_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_send_break), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_autoretry_config), (26 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_nack_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_nack_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_stop_bit_set), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_synchronous_clock_config), (34 bytes).
    Removing gd32f4xx_usart.o(i.usart_synchronous_clock_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_synchronous_clock_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_word_length_set), (16 bytes).
    Removing gd32f4xx_wwdgt.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_wwdgt.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_wwdgt.o(.rrx_text), (6 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_config), (56 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_counter_update), (28 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_deinit), (20 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_enable), (20 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_flag_clear), (20 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_flag_get), (24 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_interrupt_enable), (20 bytes).
    Removing system_gd32f4xx.o(.rev16_text), (4 bytes).
    Removing system_gd32f4xx.o(.revsh_text), (4 bytes).
    Removing system_gd32f4xx.o(.rrx_text), (6 bytes).
    Removing system_gd32f4xx.o(i.SystemCoreClockUpdate), (272 bytes).
    Removing gd32f4xx_it.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_it.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_it.o(.rrx_text), (6 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing main.o(.data), (6 bytes).
    Removing systick.o(.rev16_text), (4 bytes).
    Removing systick.o(.revsh_text), (4 bytes).
    Removing systick.o(.rrx_text), (6 bytes).
    Removing systick.o(i.delay_1ms), (20 bytes).
    Removing 25lc080a.o(.rev16_text), (4 bytes).
    Removing 25lc080a.o(.revsh_text), (4 bytes).
    Removing 25lc080a.o(.rrx_text), (6 bytes).
    Removing 25lc080a.o(i.EEPROM_WritePage), (730 bytes).
    Removing 25lc080a.o(i.USER_EEPROM_RedeByte), (72 bytes).
    Removing 25lc080a.o(i.USER_EEPROM_RedePage), (680 bytes).
    Removing 25lc080a.o(i.USER_EEPROM_WriteByte), (556 bytes).
    Removing 25lc080a.o(i.USER_EEPROM_WritePage), (44 bytes).
    Removing eeprom_spi.o(.rev16_text), (4 bytes).
    Removing eeprom_spi.o(.revsh_text), (4 bytes).
    Removing eeprom_spi.o(.rrx_text), (6 bytes).
    Removing eeprom_spi.o(i.EEPROM_ReadStatusRegister), (40 bytes).
    Removing eeprom_spi.o(i.EEPROM_SPI_ReadBuffer), (108 bytes).
    Removing eeprom_spi.o(i.EEPROM_SPI_SendInstruction), (52 bytes).
    Removing eeprom_spi.o(i.EEPROM_SPI_WaitStandbyState), (76 bytes).
    Removing eeprom_spi.o(i.EEPROM_SPI_WriteBuffer), (394 bytes).
    Removing eeprom_spi.o(i.EEPROM_SPI_WritePage), (180 bytes).
    Removing eeprom_spi.o(i.EEPROM_SendByte), (68 bytes).
    Removing eeprom_spi.o(i.EEPROM_WriteDisable), (48 bytes).
    Removing eeprom_spi.o(i.EEPROM_WriteEnable), (48 bytes).
    Removing eeprom_spi.o(i.EEPROM_WriteStatusRegister), (60 bytes).
    Removing flash.o(.rev16_text), (4 bytes).
    Removing flash.o(.revsh_text), (4 bytes).
    Removing flash.o(.rrx_text), (6 bytes).
    Removing flash.o(i.FLASH_If_Init), (16 bytes).
    Removing flash.o(i.bsp_CmpCpuFlash), (68 bytes).
    Removing flash.o(i.bsp_EraseCpuFlash), (38 bytes).
    Removing flash.o(i.bsp_GetSector), (560 bytes).
    Removing flash.o(i.bsp_ReadCpuFlash), (44 bytes).
    Removing flash.o(i.bsp_ReadWordFlash), (6 bytes).
    Removing flash.o(i.bsp_WriteCpuFlash), (160 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing my_crc.o(.rev16_text), (4 bytes).
    Removing my_crc.o(.revsh_text), (4 bytes).
    Removing my_crc.o(.rrx_text), (6 bytes).
    Removing my_crc.o(i.CRC16_USB), (126 bytes).
    Removing my_crc.o(i.InvertUint16), (58 bytes).
    Removing my_crc.o(i.InvertUint8), (58 bytes).
    Removing rtc.o(.rev16_text), (4 bytes).
    Removing rtc.o(.revsh_text), (4 bytes).
    Removing rtc.o(.rrx_text), (6 bytes).
    Removing rtc.o(i.RTC_Init), (100 bytes).
    Removing rtc.o(i.rtc_register_set), (48 bytes).
    Removing rtc.o(.bss), (20 bytes).
    Removing rtc.o(.data), (2 bytes).
    Removing spi.o(.rev16_text), (4 bytes).
    Removing spi.o(.revsh_text), (4 bytes).
    Removing spi.o(.rrx_text), (6 bytes).
    Removing spi.o(i.DRV_SPI_SwapByte), (56 bytes).
    Removing spi.o(i.SPI1_Init), (156 bytes).
    Removing time.o(.rev16_text), (4 bytes).
    Removing time.o(.revsh_text), (4 bytes).
    Removing time.o(.rrx_text), (6 bytes).
    Removing time.o(i.TIMER1_Init), (118 bytes).
    Removing time.o(i.TIMER3_Init), (84 bytes).
    Removing time.o(i.TIMER6_Init), (84 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.API_Printf_Hex), (56 bytes).
    Removing usart.o(i.API_do_tcp_client), (208 bytes).
    Removing usart.o(i.ESP32_IO_Init), (36 bytes).
    Removing usart.o(i.FML_USART_Register), (80 bytes).
    Removing usart.o(i.InitBuffer), (46 bytes).
    Removing usart.o(i.UARTx_SendBuffer), (64 bytes).
    Removing usart.o(i.USART2_Init), (176 bytes).
    Removing user_step.o(.rev16_text), (4 bytes).
    Removing user_step.o(.revsh_text), (4 bytes).
    Removing user_step.o(.rrx_text), (6 bytes).
    Removing user_step.o(i.Float2char), (28 bytes).
    Removing gd32f470v_start.o(.rev16_text), (4 bytes).
    Removing gd32f470v_start.o(.revsh_text), (4 bytes).
    Removing gd32f470v_start.o(.rrx_text), (6 bytes).
    Removing gd32f470v_start.o(i.gd_eval_BEEP_init), (68 bytes).
    Removing gd32f470v_start.o(i.gd_eval_BEEP_off), (24 bytes).
    Removing gd32f470v_start.o(i.gd_eval_BEEP_on), (24 bytes).
    Removing gd32f470v_start.o(i.gd_eval_key_init), (128 bytes).
    Removing gd32f470v_start.o(i.gd_eval_key_state_get), (32 bytes).
    Removing gd32f470v_start.o(i.gd_eval_led_init), (84 bytes).
    Removing gd32f470v_start.o(i.gd_eval_led_off), (24 bytes).
    Removing gd32f470v_start.o(i.gd_eval_led_on), (24 bytes).
    Removing gd32f470v_start.o(i.gd_eval_led_toggle), (24 bytes).
    Removing gd32f470v_start.o(.data), (62 bytes).
    Removing api_lan_data_process .o(.rev16_text), (4 bytes).
    Removing api_lan_data_process .o(.revsh_text), (4 bytes).
    Removing api_lan_data_process .o(.rrx_text), (6 bytes).
    Removing api_lan_data_process .o(i.API_ACK_20CMD), (2 bytes).
    Removing api_lan_data_process .o(i.API_ACK_21CMD), (108 bytes).
    Removing api_lan_data_process .o(i.API_ACK_22CMD), (228 bytes).
    Removing api_lan_data_process .o(i.API_ACK_23CMD), (236 bytes).
    Removing api_lan_data_process .o(i.API_ACK_24CMD), (176 bytes).
    Removing api_lan_data_process .o(i.API_ACK_25CMD), (2 bytes).
    Removing api_lan_data_process .o(i.API_ACK_26CMD), (188 bytes).
    Removing api_lan_data_process .o(i.API_ACK_27CMD), (2 bytes).
    Removing api_lan_data_process .o(i.API_ACK_28CMD), (120 bytes).
    Removing api_lan_data_process .o(i.API_ACK_29CMD), (200 bytes).
    Removing api_lan_data_process .o(i.API_AnalysisIpPortAddr), (122 bytes).
    Removing api_lan_data_process .o(i.API_Erase_Flash), (116 bytes).
    Removing api_lan_data_process .o(i.API_ExecuteSeverCMD), (264 bytes).
    Removing api_lan_data_process .o(i.API_Itoi), (108 bytes).
    Removing api_lan_data_process .o(i.API_LAN_Info_To_String), (512 bytes).
    Removing api_lan_data_process .o(i.API_LanSock1Send_CRC), (96 bytes).
    Removing api_lan_data_process .o(i.API_Process_Lan_Data), (156 bytes).
    Removing api_lan_data_process .o(i.API_Write_SeverInfo), (364 bytes).
    Removing api_lan_data_process .o(i.HEXArrayToStringArray), (80 bytes).
    Removing api_lan_data_process .o(.data), (2 bytes).
    Removing api_tnrg.o(.rev16_text), (4 bytes).
    Removing api_tnrg.o(.revsh_text), (4 bytes).
    Removing api_tnrg.o(.rrx_text), (6 bytes).
    Removing api_tnrg.o(i.API_RNG_Init), (324 bytes).
    Removing api_tnrg.o(i.get_hard_rand_data), (20 bytes).
    Removing api_w5500.o(.rev16_text), (4 bytes).
    Removing api_w5500.o(.revsh_text), (4 bytes).
    Removing api_w5500.o(.rrx_text), (6 bytes).
    Removing api_w5500.o(i.API_Init_LAN), (16 bytes).
    Removing api_w5500.o(i.API_Init_Net_Parameters), (788 bytes).
    Removing api_w5500.o(i.API_PHY_Check), (256 bytes).
    Removing api_w5500.o(i.API_W5500_GPIO_Init), (40 bytes).
    Removing api_w5500.o(i.API_W5500_Reset), (80 bytes).
    Removing api_w5500.o(i.API_W5500_SPI0_Init), (160 bytes).
    Removing api_w5500.o(.data), (1 bytes).
    Removing socket.o(.rev16_text), (4 bytes).
    Removing socket.o(.revsh_text), (4 bytes).
    Removing socket.o(.rrx_text), (6 bytes).
    Removing socket.o(i.close), (52 bytes).
    Removing socket.o(i.connect), (428 bytes).
    Removing socket.o(i.disconnect), (38 bytes).
    Removing socket.o(i.listen), (62 bytes).
    Removing socket.o(i.recv), (66 bytes).
    Removing socket.o(i.recvfrom), (512 bytes).
    Removing socket.o(i.send), (316 bytes).
    Removing socket.o(i.sendto), (268 bytes).
    Removing socket.o(i.socket), (196 bytes).
    Removing utility.o(i.atoi16), (32 bytes).
    Removing utility.o(i.atoi32), (32 bytes).
    Removing utility.o(i.c2d), (54 bytes).
    Removing utility.o(i.checksum), (76 bytes).
    Removing utility.o(i.htonl), (12 bytes).
    Removing utility.o(i.htons), (12 bytes).
    Removing utility.o(i.inet_addr_), (96 bytes).
    Removing utility.o(i.inet_ntoa), (56 bytes).
    Removing utility.o(i.inet_ntoa_pad), (64 bytes).
    Removing utility.o(i.itoa), (64 bytes).
    Removing utility.o(i.mid), (68 bytes).
    Removing utility.o(i.ntohl), (12 bytes).
    Removing utility.o(i.ntohs), (12 bytes).
    Removing utility.o(i.replacetochar), (26 bytes).
    Removing utility.o(i.swapl), (28 bytes).
    Removing utility.o(i.swaps), (14 bytes).
    Removing utility.o(i.validatoi), (72 bytes).
    Removing utility.o(i.verify_ip_address), (120 bytes).
    Removing utility.o(.bss), (48 bytes).
    Removing w5500.o(.rev16_text), (4 bytes).
    Removing w5500.o(.revsh_text), (4 bytes).
    Removing w5500.o(.rrx_text), (6 bytes).
    Removing w5500.o(i.clearIR), (26 bytes).
    Removing w5500.o(i.getGAR), (16 bytes).
    Removing w5500.o(i.getIINCHIP_RxMAX), (16 bytes).
    Removing w5500.o(i.getIINCHIP_TxMAX), (16 bytes).
    Removing w5500.o(i.getIR), (12 bytes).
    Removing w5500.o(i.getSHAR), (18 bytes).
    Removing w5500.o(i.getSIPR), (18 bytes).
    Removing w5500.o(i.getSUBR), (18 bytes).
    Removing w5500.o(i.getSn_IR), (18 bytes).
    Removing w5500.o(i.getSn_RX_RSR), (84 bytes).
    Removing w5500.o(i.getSn_SR), (18 bytes).
    Removing w5500.o(i.getSn_TX_FSR), (84 bytes).
    Removing w5500.o(i.iinchip_init), (10 bytes).
    Removing w5500.o(i.recv_data_processing), (148 bytes).
    Removing w5500.o(i.send_data_processing), (148 bytes).
    Removing w5500.o(i.setRCR), (16 bytes).
    Removing w5500.o(i.setRTR), (26 bytes).
    Removing w5500.o(i.setSn_IR), (22 bytes).
    Removing w5500.o(i.setSn_MSS), (36 bytes).
    Removing w5500.o(i.setSn_TTL), (22 bytes).
    Removing w5500.o(i.socket_buf_init), (136 bytes).
    Removing w5500.o(.bss), (32 bytes).
    Removing w5500.o(.data), (16 bytes).
    Removing w5500_conf.o(.rev16_text), (4 bytes).
    Removing w5500_conf.o(.revsh_text), (4 bytes).
    Removing w5500_conf.o(.rrx_text), (6 bytes).
    Removing w5500_conf.o(i.IINCHIP_READ), (50 bytes).
    Removing w5500_conf.o(i.PHY_check), (112 bytes).
    Removing w5500_conf.o(i.dhcp_timer_init), (8 bytes).
    Removing w5500_conf.o(i.getPHYStatus), (12 bytes).
    Removing w5500_conf.o(i.ntp_timer_init), (8 bytes).
    Removing w5500_conf.o(i.reboot), (2 bytes).
    Removing w5500_conf.o(i.reset_break_gpio_init), (2 bytes).
    Removing w5500_conf.o(i.reset_w5500), (68 bytes).
    Removing w5500_conf.o(i.setKPALVTR), (22 bytes).
    Removing w5500_conf.o(i.set_w5500_mac), (104 bytes).
    Removing w5500_conf.o(i.set_w5500_netinfo), (272 bytes).
    Removing w5500_conf.o(i.timer2_init), (2 bytes).
    Removing w5500_conf.o(i.timer2_isr), (60 bytes).
    Removing w5500_conf.o(i.wiz_read_buf), (104 bytes).
    Removing w5500_conf.o(.bss), (1024 bytes).
    Removing w5500_conf.o(.data), (9 bytes).
    Removing dhcp.o(.rev16_text), (4 bytes).
    Removing dhcp.o(.revsh_text), (4 bytes).
    Removing dhcp.o(.rrx_text), (6 bytes).
    Removing dhcp.o(i.DHCP_run), (700 bytes).
    Removing dhcp.o(i.DHCP_timer_handler), (44 bytes).
    Removing dhcp.o(i.check_DHCP_Timeout), (132 bytes).
    Removing dhcp.o(i.check_DHCP_leasedIP), (80 bytes).
    Removing dhcp.o(i.check_DHCP_timeout), (168 bytes).
    Removing dhcp.o(i.init_dhcp_client), (68 bytes).
    Removing dhcp.o(i.makeDHCPMSG), (392 bytes).
    Removing dhcp.o(i.parseDHCPMSG), (484 bytes).
    Removing dhcp.o(i.reset_DHCP_timeout), (32 bytes).
    Removing dhcp.o(i.send_DHCP_DECLINE), (488 bytes).
    Removing dhcp.o(i.send_DHCP_DISCOVER), (500 bytes).
    Removing dhcp.o(i.send_DHCP_REQUEST), (884 bytes).
    Removing tcp_client.o(.rev16_text), (4 bytes).
    Removing tcp_client.o(.revsh_text), (4 bytes).
    Removing tcp_client.o(.rrx_text), (6 bytes).
    Removing tcp_client.o(i.do_tcp_client), (164 bytes).

1147 unused section(s) (total 87536 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/../cmprslib/zerorunl.c           0x00000000   Number         0  __dczerorl.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isupper_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isalnum_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isspace_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ispunct_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isprint_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  islower_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isgraph_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isdigit_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  iscntrl_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isblank_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isalpha_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isxdigit_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ctype_o.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  malloca.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  mallocra.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  malloc.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  mallocr.o ABSOLUTE
    ../clib/microlib/malloc/mvars.c          0x00000000   Number         0  mvars.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strchr.c         0x00000000   Number         0  strchr.o ABSOLUTE
    ../clib/microlib/string/strcpy.c         0x00000000   Number         0  strcpy.o ABSOLUTE
    ../clib/microlib/string/strlen.c         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/microlib/string/strncpy.c        0x00000000   Number         0  strncpy.o ABSOLUTE
    ../clib/microlib/string/strstr.c         0x00000000   Number         0  strstr.o ABSOLUTE
    ../clib/microlib/string/strtok.c         0x00000000   Number         0  strtok.o ABSOLUTE
    ../clib/microlib/string/strtok.c         0x00000000   Number         0  strtok_r.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtol.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  atoi.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _strtoul.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _chval.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ..\Ethernet\APP\dhcp.c                   0x00000000   Number         0  dhcp.o ABSOLUTE
    ..\Ethernet\APP\tcp_client.c             0x00000000   Number         0  tcp_client.o ABSOLUTE
    ..\Ethernet\W5500\socket.c               0x00000000   Number         0  socket.o ABSOLUTE
    ..\Ethernet\W5500\utility.c              0x00000000   Number         0  utility.o ABSOLUTE
    ..\Ethernet\W5500\w5500.c                0x00000000   Number         0  w5500.o ABSOLUTE
    ..\Ethernet\W5500\w5500_conf.c           0x00000000   Number         0  w5500_conf.o ABSOLUTE
    ..\\Ethernet\\APP\\dhcp.c                0x00000000   Number         0  dhcp.o ABSOLUTE
    ..\\Ethernet\\APP\\tcp_client.c          0x00000000   Number         0  tcp_client.o ABSOLUTE
    ..\\Ethernet\\W5500\\socket.c            0x00000000   Number         0  socket.o ABSOLUTE
    ..\\Ethernet\\W5500\\w5500.c             0x00000000   Number         0  w5500.o ABSOLUTE
    ..\\Ethernet\\W5500\\w5500_conf.c        0x00000000   Number         0  w5500_conf.o ABSOLUTE
    Firmware\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s 0x00000000   Number         0  startup_gd32f450_470.o ABSOLUTE
    Firmware\CMSIS\GD\GD32F4xx\Source\system_gd32f4xx.c 0x00000000   Number         0  system_gd32f4xx.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_adc.c 0x00000000   Number         0  gd32f4xx_adc.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_can.c 0x00000000   Number         0  gd32f4xx_can.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_crc.c 0x00000000   Number         0  gd32f4xx_crc.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_ctc.c 0x00000000   Number         0  gd32f4xx_ctc.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_dac.c 0x00000000   Number         0  gd32f4xx_dac.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_dbg.c 0x00000000   Number         0  gd32f4xx_dbg.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_dci.c 0x00000000   Number         0  gd32f4xx_dci.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_dma.c 0x00000000   Number         0  gd32f4xx_dma.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_enet.c 0x00000000   Number         0  gd32f4xx_enet.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_exmc.c 0x00000000   Number         0  gd32f4xx_exmc.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_exti.c 0x00000000   Number         0  gd32f4xx_exti.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_fmc.c 0x00000000   Number         0  gd32f4xx_fmc.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_fwdgt.c 0x00000000   Number         0  gd32f4xx_fwdgt.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_gpio.c 0x00000000   Number         0  gd32f4xx_gpio.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_i2c.c 0x00000000   Number         0  gd32f4xx_i2c.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_ipa.c 0x00000000   Number         0  gd32f4xx_ipa.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_iref.c 0x00000000   Number         0  gd32f4xx_iref.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_misc.c 0x00000000   Number         0  gd32f4xx_misc.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_pmu.c 0x00000000   Number         0  gd32f4xx_pmu.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_rcu.c 0x00000000   Number         0  gd32f4xx_rcu.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_rtc.c 0x00000000   Number         0  gd32f4xx_rtc.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_sdio.c 0x00000000   Number         0  gd32f4xx_sdio.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_spi.c 0x00000000   Number         0  gd32f4xx_spi.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_syscfg.c 0x00000000   Number         0  gd32f4xx_syscfg.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_timer.c 0x00000000   Number         0  gd32f4xx_timer.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_tli.c 0x00000000   Number         0  gd32f4xx_tli.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_trng.c 0x00000000   Number         0  gd32f4xx_trng.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_usart.c 0x00000000   Number         0  gd32f4xx_usart.o ABSOLUTE
    Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_wwdgt.c 0x00000000   Number         0  gd32f4xx_wwdgt.o ABSOLUTE
    Firmware\\CMSIS\\GD\\GD32F4xx\\Source\\system_gd32f4xx.c 0x00000000   Number         0  system_gd32f4xx.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_adc.c 0x00000000   Number         0  gd32f4xx_adc.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_can.c 0x00000000   Number         0  gd32f4xx_can.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_crc.c 0x00000000   Number         0  gd32f4xx_crc.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_ctc.c 0x00000000   Number         0  gd32f4xx_ctc.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dac.c 0x00000000   Number         0  gd32f4xx_dac.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dbg.c 0x00000000   Number         0  gd32f4xx_dbg.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dci.c 0x00000000   Number         0  gd32f4xx_dci.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dma.c 0x00000000   Number         0  gd32f4xx_dma.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_enet.c 0x00000000   Number         0  gd32f4xx_enet.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_exmc.c 0x00000000   Number         0  gd32f4xx_exmc.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_exti.c 0x00000000   Number         0  gd32f4xx_exti.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_fmc.c 0x00000000   Number         0  gd32f4xx_fmc.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_fwdgt.c 0x00000000   Number         0  gd32f4xx_fwdgt.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_gpio.c 0x00000000   Number         0  gd32f4xx_gpio.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_i2c.c 0x00000000   Number         0  gd32f4xx_i2c.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_ipa.c 0x00000000   Number         0  gd32f4xx_ipa.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_iref.c 0x00000000   Number         0  gd32f4xx_iref.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_misc.c 0x00000000   Number         0  gd32f4xx_misc.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_pmu.c 0x00000000   Number         0  gd32f4xx_pmu.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_rcu.c 0x00000000   Number         0  gd32f4xx_rcu.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_rtc.c 0x00000000   Number         0  gd32f4xx_rtc.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_sdio.c 0x00000000   Number         0  gd32f4xx_sdio.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_spi.c 0x00000000   Number         0  gd32f4xx_spi.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_syscfg.c 0x00000000   Number         0  gd32f4xx_syscfg.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_timer.c 0x00000000   Number         0  gd32f4xx_timer.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_tli.c 0x00000000   Number         0  gd32f4xx_tli.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_trng.c 0x00000000   Number         0  gd32f4xx_trng.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_usart.c 0x00000000   Number         0  gd32f4xx_usart.o ABSOLUTE
    Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_wwdgt.c 0x00000000   Number         0  gd32f4xx_wwdgt.o ABSOLUTE
    USER\\gd32f4xx_it.c                      0x00000000   Number         0  gd32f4xx_it.o ABSOLUTE
    USER\\main.c                             0x00000000   Number         0  main.o ABSOLUTE
    USER\\systick.c                          0x00000000   Number         0  systick.o ABSOLUTE
    USER\gd32f4xx_it.c                       0x00000000   Number         0  gd32f4xx_it.o ABSOLUTE
    USER\main.c                              0x00000000   Number         0  main.o ABSOLUTE
    USER\systick.c                           0x00000000   Number         0  systick.o ABSOLUTE
    Utilities\25LC080A.c                     0x00000000   Number         0  25lc080a.o ABSOLUTE
    Utilities\API_LAN_DATA_Process .c        0x00000000   Number         0  api_lan_data_process .o ABSOLUTE
    Utilities\API_TNRG.c                     0x00000000   Number         0  api_tnrg.o ABSOLUTE
    Utilities\API_W5500.c                    0x00000000   Number         0  api_w5500.o ABSOLUTE
    Utilities\My_CRC.c                       0x00000000   Number         0  my_crc.o ABSOLUTE
    Utilities\\25LC080A.c                    0x00000000   Number         0  25lc080a.o ABSOLUTE
    Utilities\\API_LAN_DATA_Process .c       0x00000000   Number         0  api_lan_data_process .o ABSOLUTE
    Utilities\\API_TNRG.c                    0x00000000   Number         0  api_tnrg.o ABSOLUTE
    Utilities\\API_W5500.c                   0x00000000   Number         0  api_w5500.o ABSOLUTE
    Utilities\\My_CRC.c                      0x00000000   Number         0  my_crc.o ABSOLUTE
    Utilities\\eeprom_spi.c                  0x00000000   Number         0  eeprom_spi.o ABSOLUTE
    Utilities\\flash.c                       0x00000000   Number         0  flash.o ABSOLUTE
    Utilities\\gd32f470v_start.c             0x00000000   Number         0  gd32f470v_start.o ABSOLUTE
    Utilities\\gpio.c                        0x00000000   Number         0  gpio.o ABSOLUTE
    Utilities\\rtc.c                         0x00000000   Number         0  rtc.o ABSOLUTE
    Utilities\\spi.c                         0x00000000   Number         0  spi.o ABSOLUTE
    Utilities\\time.c                        0x00000000   Number         0  time.o ABSOLUTE
    Utilities\\usart.c                       0x00000000   Number         0  usart.o ABSOLUTE
    Utilities\\user_step.c                   0x00000000   Number         0  user_step.o ABSOLUTE
    Utilities\eeprom_spi.c                   0x00000000   Number         0  eeprom_spi.o ABSOLUTE
    Utilities\flash.c                        0x00000000   Number         0  flash.o ABSOLUTE
    Utilities\gd32f470v_start.c              0x00000000   Number         0  gd32f470v_start.o ABSOLUTE
    Utilities\gpio.c                         0x00000000   Number         0  gpio.o ABSOLUTE
    Utilities\rtc.c                          0x00000000   Number         0  rtc.o ABSOLUTE
    Utilities\spi.c                          0x00000000   Number         0  spi.o ABSOLUTE
    Utilities\time.c                         0x00000000   Number         0  time.o ABSOLUTE
    Utilities\usart.c                        0x00000000   Number         0  usart.o ABSOLUTE
    Utilities\user_step.c                    0x00000000   Number         0  user_step.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    RESET                                    0x08040000   Section      428  startup_gd32f450_470.o(RESET)
    .ARM.Collect$$$$00000000                 0x080401ac   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x080401ac   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x080401b0   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x080401b4   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x080401b4   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x080401b4   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000E                 0x080401bc   Section        4  entry12b.o(.ARM.Collect$$$$0000000E)
    .ARM.Collect$$$$0000000F                 0x080401c0   Section        0  entry10a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00000011                 0x080401c0   Section        0  entry11a.o(.ARM.Collect$$$$00000011)
    .ARM.Collect$$$$00002712                 0x080401c0   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x080401c0   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x080401c4   Section       36  startup_gd32f450_470.o(.text)
    $v0                                      0x080401c4   Number         0  startup_gd32f450_470.o(.text)
    .text                                    0x080401e8   Section        0  memcpya.o(.text)
    .text                                    0x0804020c   Section        0  memseta.o(.text)
    .text                                    0x08040230   Section        0  uidiv.o(.text)
    .text                                    0x0804025c   Section        0  uldiv.o(.text)
    .text                                    0x080402be   Section        0  iusefp.o(.text)
    .text                                    0x080402be   Section        0  dadd.o(.text)
    .text                                    0x0804040c   Section        0  dmul.o(.text)
    .text                                    0x080404f0   Section        0  ddiv.o(.text)
    .text                                    0x080405ce   Section        0  dfixul.o(.text)
    .text                                    0x08040600   Section       48  cdrcmple.o(.text)
    .text                                    0x08040630   Section       36  init.o(.text)
    .text                                    0x08040654   Section        0  llshl.o(.text)
    .text                                    0x08040672   Section        0  llushr.o(.text)
    .text                                    0x08040692   Section        0  llsshr.o(.text)
    .text                                    0x080406b6   Section        0  depilogue.o(.text)
    .text                                    0x08040770   Section        0  __dczerorl.o(.text)
    i.API_Chose_TS5A3359_GAIN                0x080407ac   Section        0  gpio.o(i.API_Chose_TS5A3359_GAIN)
    i.API_LED_GPIO                           0x08040814   Section        0  gpio.o(i.API_LED_GPIO)
    i.API_SPI0_Send_Read_Byte                0x08040864   Section        0  api_w5500.o(i.API_SPI0_Send_Read_Byte)
    i.AddByteToBuffer                        0x0804089c   Section        0  usart.o(i.AddByteToBuffer)
    AddByteToBuffer                          0x0804089d   Thumb Code   110  usart.o(i.AddByteToBuffer)
    i.BusFault_Handler                       0x0804090a   Section        0  gd32f4xx_it.o(i.BusFault_Handler)
    i.DMA1_Channel0_IRQHandler               0x0804090e   Section        0  gd32f4xx_it.o(i.DMA1_Channel0_IRQHandler)
    i.DMA1_Channel1_IRQHandler               0x08040910   Section        0  gd32f4xx_it.o(i.DMA1_Channel1_IRQHandler)
    i.DMA1_Channel2_IRQHandler               0x08040912   Section        0  gd32f4xx_it.o(i.DMA1_Channel2_IRQHandler)
    i.DebugMon_Handler                       0x08040914   Section        0  gd32f4xx_it.o(i.DebugMon_Handler)
    i.EXTI10_15_IRQHandler                   0x08040916   Section        0  gd32f4xx_it.o(i.EXTI10_15_IRQHandler)
    i.FML_USART_RecvTask                     0x08040918   Section        0  usart.o(i.FML_USART_RecvTask)
    i.GPIO_Init                              0x080409d0   Section        0  gpio.o(i.GPIO_Init)
    i.HardFault_Handler                      0x08040a38   Section        0  gd32f4xx_it.o(i.HardFault_Handler)
    i.IINCHIP_SpiSendData                    0x08040a48   Section        0  w5500_conf.o(i.IINCHIP_SpiSendData)
    i.IINCHIP_WRITE                          0x08040a54   Section        0  w5500_conf.o(i.IINCHIP_WRITE)
    i.Init_GPIO_TS5A339                      0x08040a88   Section        0  gpio.o(i.Init_GPIO_TS5A339)
    i.MemManage_Handler                      0x08040ab0   Section        0  gd32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08040ab4   Section        0  gd32f4xx_it.o(i.NMI_Handler)
    i.PendSV_Handler                         0x08040ab6   Section        0  gd32f4xx_it.o(i.PendSV_Handler)
    i.ReadBytesToBuffer                      0x08040ab8   Section        0  usart.o(i.ReadBytesToBuffer)
    ReadBytesToBuffer                        0x08040ab9   Thumb Code   142  usart.o(i.ReadBytesToBuffer)
    i.RecvDataHandler                        0x08040b46   Section        0  usart.o(i.RecvDataHandler)
    RecvDataHandler                          0x08040b47   Thumb Code    30  usart.o(i.RecvDataHandler)
    i.SVC_Handler                            0x08040b64   Section        0  gd32f4xx_it.o(i.SVC_Handler)
    i.SysReset_Condition                     0x08040b68   Section        0  user_step.o(i.SysReset_Condition)
    i.SysTick_Handler                        0x08040ba0   Section        0  gd32f4xx_it.o(i.SysTick_Handler)
    i.SystemInit                             0x08040ba8   Section        0  system_gd32f4xx.o(i.SystemInit)
    i.TIMER2_IRQHandler                      0x08040c70   Section        0  gd32f4xx_it.o(i.TIMER2_IRQHandler)
    i.TIMER3_IRQHandler                      0x08040c74   Section        0  gd32f4xx_it.o(i.TIMER3_IRQHandler)
    i.TIMER6_IRQHandler                      0x08040c8c   Section        0  gd32f4xx_it.o(i.TIMER6_IRQHandler)
    i.TIM_PeriodElapsedCallback              0x08040ca4   Section        0  time.o(i.TIM_PeriodElapsedCallback)
    i.UART_IDLECallBack                      0x08040e34   Section        0  usart.o(i.UART_IDLECallBack)
    i.UART_RxCpltCallback                    0x08040e5c   Section        0  usart.o(i.UART_RxCpltCallback)
    i.USART0_Init                            0x08040e88   Section        0  usart.o(i.USART0_Init)
    i.USART2_IRQHandler                      0x08040f10   Section        0  gd32f4xx_it.o(i.USART2_IRQHandler)
    i.UsageFault_Handler                     0x08040f40   Section        0  gd32f4xx_it.o(i.UsageFault_Handler)
    i.__0printf                              0x08040f44   Section        0  printfa.o(i.__0printf)
    i.__NVIC_SetPriority                     0x08040f64   Section        0  systick.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x08040f65   Thumb Code    32  systick.o(i.__NVIC_SetPriority)
    i.__scatterload_copy                     0x08040f8c   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x08040f9a   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08040f9c   Section       14  handlers.o(i.__scatterload_zeroinit)
    i._fp_digits                             0x08040fac   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x08040fad   Thumb Code   366  printfa.o(i._fp_digits)
    i._printf_core                           0x08041130   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x08041131   Thumb Code  1704  printfa.o(i._printf_core)
    i._printf_post_padding                   0x080417e4   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x080417e5   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x08041808   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x08041809   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i.default_ip_assign                      0x08041838   Section        0  dhcp.o(i.default_ip_assign)
    i.default_ip_conflict                    0x0804185c   Section        0  dhcp.o(i.default_ip_conflict)
    i.default_ip_update                      0x08041870   Section        0  dhcp.o(i.default_ip_update)
    i.delay_decrement                        0x08041888   Section        0  systick.o(i.delay_decrement)
    i.fputc                                  0x080418a0   Section        0  usart.o(i.fputc)
    i.gpio_af_set                            0x080418c4   Section        0  gd32f4xx_gpio.o(i.gpio_af_set)
    i.gpio_bit_reset                         0x08041922   Section        0  gd32f4xx_gpio.o(i.gpio_bit_reset)
    i.gpio_bit_set                           0x08041926   Section        0  gd32f4xx_gpio.o(i.gpio_bit_set)
    i.gpio_mode_set                          0x0804192a   Section        0  gd32f4xx_gpio.o(i.gpio_mode_set)
    i.gpio_output_options_set                0x08041978   Section        0  gd32f4xx_gpio.o(i.gpio_output_options_set)
    i.iinchip_csoff                          0x080419bc   Section        0  w5500_conf.o(i.iinchip_csoff)
    i.iinchip_cson                           0x080419d0   Section        0  w5500_conf.o(i.iinchip_cson)
    i.main                                   0x080419e4   Section        0  main.o(i.main)
    i.rcu_clock_freq_get                     0x080419f8   Section        0  gd32f4xx_rcu.o(i.rcu_clock_freq_get)
    i.rcu_periph_clock_enable                0x08041b1c   Section        0  gd32f4xx_rcu.o(i.rcu_periph_clock_enable)
    i.rcu_periph_reset_disable               0x08041b40   Section        0  gd32f4xx_rcu.o(i.rcu_periph_reset_disable)
    i.rcu_periph_reset_enable                0x08041b64   Section        0  gd32f4xx_rcu.o(i.rcu_periph_reset_enable)
    i.setGAR                                 0x08041b88   Section        0  w5500.o(i.setGAR)
    i.setMR                                  0x08041b98   Section        0  w5500.o(i.setMR)
    i.setSHAR                                0x08041ba6   Section        0  w5500.o(i.setSHAR)
    i.setSIPR                                0x08041bb8   Section        0  w5500.o(i.setSIPR)
    i.setSUBR                                0x08041bca   Section        0  w5500.o(i.setSUBR)
    i.spi_i2s_data_receive                   0x08041bdc   Section        0  gd32f4xx_spi.o(i.spi_i2s_data_receive)
    i.spi_i2s_data_transmit                  0x08041be4   Section        0  gd32f4xx_spi.o(i.spi_i2s_data_transmit)
    i.spi_i2s_flag_get                       0x08041be8   Section        0  gd32f4xx_spi.o(i.spi_i2s_flag_get)
    i.system_clock_168m_25m_hxtal            0x08041bf8   Section        0  system_gd32f4xx.o(i.system_clock_168m_25m_hxtal)
    system_clock_168m_25m_hxtal              0x08041bf9   Thumb Code   240  system_gd32f4xx.o(i.system_clock_168m_25m_hxtal)
    i.system_clock_config                    0x08041cf4   Section        0  system_gd32f4xx.o(i.system_clock_config)
    system_clock_config                      0x08041cf5   Thumb Code     8  system_gd32f4xx.o(i.system_clock_config)
    i.systick_config                         0x08041cfc   Section        0  systick.o(i.systick_config)
    i.timer_interrupt_flag_clear             0x08041d4c   Section        0  gd32f4xx_timer.o(i.timer_interrupt_flag_clear)
    i.timer_interrupt_flag_get               0x08041d52   Section        0  gd32f4xx_timer.o(i.timer_interrupt_flag_get)
    i.usart_baudrate_set                     0x08041d6c   Section        0  gd32f4xx_usart.o(i.usart_baudrate_set)
    i.usart_data_receive                     0x08041e54   Section        0  gd32f4xx_usart.o(i.usart_data_receive)
    i.usart_data_transmit                    0x08041e5e   Section        0  gd32f4xx_usart.o(i.usart_data_transmit)
    i.usart_deinit                           0x08041e68   Section        0  gd32f4xx_usart.o(i.usart_deinit)
    i.usart_enable                           0x08041f44   Section        0  gd32f4xx_usart.o(i.usart_enable)
    i.usart_flag_get                         0x08041f4e   Section        0  gd32f4xx_usart.o(i.usart_flag_get)
    i.usart_interrupt_flag_clear             0x08041f6c   Section        0  gd32f4xx_usart.o(i.usart_interrupt_flag_clear)
    i.usart_interrupt_flag_get               0x08041f86   Section        0  gd32f4xx_usart.o(i.usart_interrupt_flag_get)
    i.usart_receive_config                   0x08041fbe   Section        0  gd32f4xx_usart.o(i.usart_receive_config)
    i.usart_transmit_config                  0x08041fce   Section        0  gd32f4xx_usart.o(i.usart_transmit_config)
    i.wiz_write_buf                          0x08041fe0   Section        0  w5500_conf.o(i.wiz_write_buf)
    .NoInit                                  0x20000000   Section        4  user_step.o(.NoInit)
    .data                                    0x20000004   Section        4  system_gd32f4xx.o(.data)
    .data                                    0x20000008   Section        4  systick.o(.data)
    delay                                    0x20000008   Data           4  systick.o(.data)
    .data                                    0x2000000c   Section        6  time.o(.data)
    .data                                    0x20000014   Section       76  dhcp.o(.data)
    .data                                    0x20000060   Section        4  stdout.o(.data)
    .bss                                     0x20000064   Section       20  time.o(.bss)
    .bss                                     0x20000078   Section     1024  dhcp.o(.bss)
    STACK                                    0x20000478   Section    20480  startup_gd32f450_470.o(STACK)
    .RAM_D3                                  0x20020000   Section     6167  usart.o(.RAM_D3)
    .RAM_D3                                  0x20021818   Section     2120  api_w5500.o(.RAM_D3)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __arm_fini_                               - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x000001ac   Number         0  startup_gd32f450_470.o ABSOLUTE
    __Vectors                                0x08040000   Data           4  startup_gd32f450_470.o(RESET)
    __Vectors_End                            0x080401ac   Data           0  startup_gd32f450_470.o(RESET)
    __main                                   0x080401ad   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x080401ad   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x080401b1   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x080401b5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x080401b5   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x080401b5   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x080401b5   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_lib_shutdown_fini                   0x080401bd   Thumb Code     0  entry12b.o(.ARM.Collect$$$$0000000E)
    __rt_final_cpp                           0x080401c1   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000F)
    __rt_final_exit                          0x080401c1   Thumb Code     0  entry11a.o(.ARM.Collect$$$$00000011)
    Reset_Handler                            0x080401c5   Thumb Code     8  startup_gd32f450_470.o(.text)
    ADC_IRQHandler                           0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_EWMC_IRQHandler                     0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_RX0_IRQHandler                      0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_RX1_IRQHandler                      0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_TX_IRQHandler                       0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_EWMC_IRQHandler                     0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_RX0_IRQHandler                      0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_RX1_IRQHandler                      0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_TX_IRQHandler                       0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    DCI_IRQHandler                           0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel0_IRQHandler                 0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel1_IRQHandler                 0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel2_IRQHandler                 0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel3_IRQHandler                 0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel4_IRQHandler                 0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel5_IRQHandler                 0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel6_IRQHandler                 0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel7_IRQHandler                 0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel3_IRQHandler                 0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel4_IRQHandler                 0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel5_IRQHandler                 0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel6_IRQHandler                 0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel7_IRQHandler                 0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    ENET_IRQHandler                          0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    ENET_WKUP_IRQHandler                     0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXMC_IRQHandler                          0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI0_IRQHandler                         0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI1_IRQHandler                         0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI2_IRQHandler                         0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI3_IRQHandler                         0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI4_IRQHandler                         0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI5_9_IRQHandler                       0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    FMC_IRQHandler                           0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    FPU_IRQHandler                           0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C0_ER_IRQHandler                       0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C0_EV_IRQHandler                       0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C1_ER_IRQHandler                       0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C1_EV_IRQHandler                       0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C2_ER_IRQHandler                       0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C2_EV_IRQHandler                       0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    IPA_IRQHandler                           0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    LVD_IRQHandler                           0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    RCU_CTC_IRQHandler                       0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    RTC_Alarm_IRQHandler                     0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    RTC_WKUP_IRQHandler                      0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    SDIO_IRQHandler                          0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI0_IRQHandler                          0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI1_IRQHandler                          0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI2_IRQHandler                          0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI3_IRQHandler                          0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI4_IRQHandler                          0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI5_IRQHandler                          0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    TAMPER_STAMP_IRQHandler                  0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_BRK_TIMER8_IRQHandler             0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_Channel_IRQHandler                0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_TRG_CMT_TIMER10_IRQHandler        0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_UP_TIMER9_IRQHandler              0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER1_IRQHandler                        0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER4_IRQHandler                        0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER5_DAC_IRQHandler                    0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_BRK_TIMER11_IRQHandler            0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_Channel_IRQHandler                0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_TRG_CMT_TIMER13_IRQHandler        0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_UP_TIMER12_IRQHandler             0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    TLI_ER_IRQHandler                        0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    TLI_IRQHandler                           0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    TRNG_IRQHandler                          0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART3_IRQHandler                         0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART4_IRQHandler                         0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART6_IRQHandler                         0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART7_IRQHandler                         0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    USART0_IRQHandler                        0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    USART1_IRQHandler                        0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    USART5_IRQHandler                        0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBFS_IRQHandler                         0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBFS_WKUP_IRQHandler                    0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_EP1_In_IRQHandler                  0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_EP1_Out_IRQHandler                 0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_IRQHandler                         0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_WKUP_IRQHandler                    0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    WWDGT_IRQHandler                         0x080401df   Thumb Code     0  startup_gd32f450_470.o(.text)
    __aeabi_memcpy                           0x080401e9   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x080401e9   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x080401e9   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x0804020d   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x0804020d   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x0804020d   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x0804021b   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x0804021b   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x0804021b   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x0804021f   Thumb Code    18  memseta.o(.text)
    __aeabi_uidiv                            0x08040231   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x08040231   Thumb Code    44  uidiv.o(.text)
    __aeabi_uldivmod                         0x0804025d   Thumb Code    98  uldiv.o(.text)
    __I$use$fp                               0x080402bf   Thumb Code     0  iusefp.o(.text)
    __aeabi_dadd                             0x080402bf   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x08040401   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x08040407   Thumb Code     6  dadd.o(.text)
    __aeabi_dmul                             0x0804040d   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x080404f1   Thumb Code   222  ddiv.o(.text)
    __aeabi_d2ulz                            0x080405cf   Thumb Code    48  dfixul.o(.text)
    __aeabi_cdrcmple                         0x08040601   Thumb Code    48  cdrcmple.o(.text)
    __scatterload                            0x08040631   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08040631   Thumb Code     0  init.o(.text)
    __aeabi_llsl                             0x08040655   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x08040655   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x08040673   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x08040673   Thumb Code     0  llushr.o(.text)
    __aeabi_lasr                             0x08040693   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x08040693   Thumb Code     0  llsshr.o(.text)
    _double_round                            0x080406b7   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x080406d5   Thumb Code   156  depilogue.o(.text)
    __decompress                             0x08040771   Thumb Code     0  __dczerorl.o(.text)
    __decompress0                            0x08040771   Thumb Code    58  __dczerorl.o(.text)
    API_Chose_TS5A3359_GAIN                  0x080407ad   Thumb Code    98  gpio.o(i.API_Chose_TS5A3359_GAIN)
    API_LED_GPIO                             0x08040815   Thumb Code    72  gpio.o(i.API_LED_GPIO)
    API_SPI0_Send_Read_Byte                  0x08040865   Thumb Code    50  api_w5500.o(i.API_SPI0_Send_Read_Byte)
    BusFault_Handler                         0x0804090b   Thumb Code     4  gd32f4xx_it.o(i.BusFault_Handler)
    DMA1_Channel0_IRQHandler                 0x0804090f   Thumb Code     2  gd32f4xx_it.o(i.DMA1_Channel0_IRQHandler)
    DMA1_Channel1_IRQHandler                 0x08040911   Thumb Code     2  gd32f4xx_it.o(i.DMA1_Channel1_IRQHandler)
    DMA1_Channel2_IRQHandler                 0x08040913   Thumb Code     2  gd32f4xx_it.o(i.DMA1_Channel2_IRQHandler)
    DebugMon_Handler                         0x08040915   Thumb Code     2  gd32f4xx_it.o(i.DebugMon_Handler)
    EXTI10_15_IRQHandler                     0x08040917   Thumb Code     2  gd32f4xx_it.o(i.EXTI10_15_IRQHandler)
    FML_USART_RecvTask                       0x08040919   Thumb Code   174  usart.o(i.FML_USART_RecvTask)
    GPIO_Init                                0x080409d1   Thumb Code    90  gpio.o(i.GPIO_Init)
    HardFault_Handler                        0x08040a39   Thumb Code    10  gd32f4xx_it.o(i.HardFault_Handler)
    IINCHIP_SpiSendData                      0x08040a49   Thumb Code    12  w5500_conf.o(i.IINCHIP_SpiSendData)
    IINCHIP_WRITE                            0x08040a55   Thumb Code    50  w5500_conf.o(i.IINCHIP_WRITE)
    Init_GPIO_TS5A339                        0x08040a89   Thumb Code    36  gpio.o(i.Init_GPIO_TS5A339)
    MemManage_Handler                        0x08040ab1   Thumb Code     4  gd32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08040ab5   Thumb Code     2  gd32f4xx_it.o(i.NMI_Handler)
    PendSV_Handler                           0x08040ab7   Thumb Code     2  gd32f4xx_it.o(i.PendSV_Handler)
    SVC_Handler                              0x08040b65   Thumb Code     2  gd32f4xx_it.o(i.SVC_Handler)
    SysReset_Condition                       0x08040b69   Thumb Code    42  user_step.o(i.SysReset_Condition)
    SysTick_Handler                          0x08040ba1   Thumb Code     8  gd32f4xx_it.o(i.SysTick_Handler)
    SystemInit                               0x08040ba9   Thumb Code   184  system_gd32f4xx.o(i.SystemInit)
    TIMER2_IRQHandler                        0x08040c71   Thumb Code     2  gd32f4xx_it.o(i.TIMER2_IRQHandler)
    TIMER3_IRQHandler                        0x08040c75   Thumb Code    20  gd32f4xx_it.o(i.TIMER3_IRQHandler)
    TIMER6_IRQHandler                        0x08040c8d   Thumb Code    20  gd32f4xx_it.o(i.TIMER6_IRQHandler)
    TIM_PeriodElapsedCallback                0x08040ca5   Thumb Code   374  time.o(i.TIM_PeriodElapsedCallback)
    UART_IDLECallBack                        0x08040e35   Thumb Code    30  usart.o(i.UART_IDLECallBack)
    UART_RxCpltCallback                      0x08040e5d   Thumb Code    30  usart.o(i.UART_RxCpltCallback)
    USART0_Init                              0x08040e89   Thumb Code   126  usart.o(i.USART0_Init)
    USART2_IRQHandler                        0x08040f11   Thumb Code    36  gd32f4xx_it.o(i.USART2_IRQHandler)
    UsageFault_Handler                       0x08040f41   Thumb Code     4  gd32f4xx_it.o(i.UsageFault_Handler)
    __0printf                                0x08040f45   Thumb Code    22  printfa.o(i.__0printf)
    __1printf                                0x08040f45   Thumb Code     0  printfa.o(i.__0printf)
    __2printf                                0x08040f45   Thumb Code     0  printfa.o(i.__0printf)
    __c89printf                              0x08040f45   Thumb Code     0  printfa.o(i.__0printf)
    printf                                   0x08040f45   Thumb Code     0  printfa.o(i.__0printf)
    __scatterload_copy                       0x08040f8d   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x08040f9b   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08040f9d   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    default_ip_assign                        0x08041839   Thumb Code    22  dhcp.o(i.default_ip_assign)
    default_ip_conflict                      0x0804185d   Thumb Code    16  dhcp.o(i.default_ip_conflict)
    default_ip_update                        0x08041871   Thumb Code    20  dhcp.o(i.default_ip_update)
    delay_decrement                          0x08041889   Thumb Code    18  systick.o(i.delay_decrement)
    fputc                                    0x080418a1   Thumb Code    32  usart.o(i.fputc)
    gpio_af_set                              0x080418c5   Thumb Code    94  gd32f4xx_gpio.o(i.gpio_af_set)
    gpio_bit_reset                           0x08041923   Thumb Code     4  gd32f4xx_gpio.o(i.gpio_bit_reset)
    gpio_bit_set                             0x08041927   Thumb Code     4  gd32f4xx_gpio.o(i.gpio_bit_set)
    gpio_mode_set                            0x0804192b   Thumb Code    78  gd32f4xx_gpio.o(i.gpio_mode_set)
    gpio_output_options_set                  0x08041979   Thumb Code    66  gd32f4xx_gpio.o(i.gpio_output_options_set)
    iinchip_csoff                            0x080419bd   Thumb Code    14  w5500_conf.o(i.iinchip_csoff)
    iinchip_cson                             0x080419d1   Thumb Code    14  w5500_conf.o(i.iinchip_cson)
    main                                     0x080419e5   Thumb Code    20  main.o(i.main)
    rcu_clock_freq_get                       0x080419f9   Thumb Code   264  gd32f4xx_rcu.o(i.rcu_clock_freq_get)
    rcu_periph_clock_enable                  0x08041b1d   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_periph_clock_enable)
    rcu_periph_reset_disable                 0x08041b41   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_periph_reset_disable)
    rcu_periph_reset_enable                  0x08041b65   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_periph_reset_enable)
    setGAR                                   0x08041b89   Thumb Code    16  w5500.o(i.setGAR)
    setMR                                    0x08041b99   Thumb Code    14  w5500.o(i.setMR)
    setSHAR                                  0x08041ba7   Thumb Code    18  w5500.o(i.setSHAR)
    setSIPR                                  0x08041bb9   Thumb Code    18  w5500.o(i.setSIPR)
    setSUBR                                  0x08041bcb   Thumb Code    18  w5500.o(i.setSUBR)
    spi_i2s_data_receive                     0x08041bdd   Thumb Code     8  gd32f4xx_spi.o(i.spi_i2s_data_receive)
    spi_i2s_data_transmit                    0x08041be5   Thumb Code     4  gd32f4xx_spi.o(i.spi_i2s_data_transmit)
    spi_i2s_flag_get                         0x08041be9   Thumb Code    16  gd32f4xx_spi.o(i.spi_i2s_flag_get)
    systick_config                           0x08041cfd   Thumb Code    74  systick.o(i.systick_config)
    timer_interrupt_flag_clear               0x08041d4d   Thumb Code     6  gd32f4xx_timer.o(i.timer_interrupt_flag_clear)
    timer_interrupt_flag_get                 0x08041d53   Thumb Code    24  gd32f4xx_timer.o(i.timer_interrupt_flag_get)
    usart_baudrate_set                       0x08041d6d   Thumb Code   224  gd32f4xx_usart.o(i.usart_baudrate_set)
    usart_data_receive                       0x08041e55   Thumb Code    10  gd32f4xx_usart.o(i.usart_data_receive)
    usart_data_transmit                      0x08041e5f   Thumb Code     8  gd32f4xx_usart.o(i.usart_data_transmit)
    usart_deinit                             0x08041e69   Thumb Code   210  gd32f4xx_usart.o(i.usart_deinit)
    usart_enable                             0x08041f45   Thumb Code    10  gd32f4xx_usart.o(i.usart_enable)
    usart_flag_get                           0x08041f4f   Thumb Code    30  gd32f4xx_usart.o(i.usart_flag_get)
    usart_interrupt_flag_clear               0x08041f6d   Thumb Code    26  gd32f4xx_usart.o(i.usart_interrupt_flag_clear)
    usart_interrupt_flag_get                 0x08041f87   Thumb Code    56  gd32f4xx_usart.o(i.usart_interrupt_flag_get)
    usart_receive_config                     0x08041fbf   Thumb Code    16  gd32f4xx_usart.o(i.usart_receive_config)
    usart_transmit_config                    0x08041fcf   Thumb Code    16  gd32f4xx_usart.o(i.usart_transmit_config)
    wiz_write_buf                            0x08041fe1   Thumb Code    80  w5500_conf.o(i.wiz_write_buf)
    Region$$Table$$Base                      0x08042048   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08042078   Number         0  anon$$obj.o(Region$$Table)
    g_JumpInit                               0x20000000   Data           4  user_step.o(.NoInit)
    SystemCoreClock                          0x20000004   Data           4  system_gd32f4xx.o(.data)
    tim6_msTic                               0x2000000c   Data           4  time.o(.data)
    TempTestTimer                            0x20000010   Data           2  time.o(.data)
    dhcp_tick_next                           0x20000014   Data           4  dhcp.o(.data)
    OLD_allocated_ip                         0x20000018   Data           4  dhcp.o(.data)
    DHCP_allocated_ip                        0x2000001c   Data           4  dhcp.o(.data)
    DHCP_allocated_gw                        0x20000020   Data           4  dhcp.o(.data)
    DHCP_allocated_sn                        0x20000024   Data           4  dhcp.o(.data)
    DHCP_allocated_dns                       0x20000028   Data           4  dhcp.o(.data)
    HOST_NAME                                0x2000002c   Data           8  dhcp.o(.data)
    dhcp_state                               0x20000034   Data           1  dhcp.o(.data)
    dhcp_retry_count                         0x20000035   Data           1  dhcp.o(.data)
    DHCP_timeout                             0x20000036   Data           1  dhcp.o(.data)
    dhcp_lease_time                          0x20000038   Data           4  dhcp.o(.data)
    dhcp_time                                0x2000003c   Data           4  dhcp.o(.data)
    next_dhcp_time                           0x20000040   Data           4  dhcp.o(.data)
    dhcp_tick_cnt                            0x20000044   Data           4  dhcp.o(.data)
    DHCP_timer                               0x20000048   Data           1  dhcp.o(.data)
    Conflict_flag                            0x20000049   Data           1  dhcp.o(.data)
    DHCP_XID                                 0x2000004c   Data           4  dhcp.o(.data)
    pDHCPMSG                                 0x20000050   Data           4  dhcp.o(.data)
    dhcp_ip_assign                           0x20000054   Data           4  dhcp.o(.data)
    dhcp_ip_update                           0x20000058   Data           4  dhcp.o(.data)
    dhcp_ip_conflict                         0x2000005c   Data           4  dhcp.o(.data)
    __stdout                                 0x20000060   Data           4  stdout.o(.data)
    g_tTimeSign                              0x20000064   Data           9  time.o(.bss)
    my_key                                   0x2000006e   Data          10  time.o(.bss)
    EXTERN_DHCPBUF                           0x20000078   Data        1024  dhcp.o(.bss)
    __initial_sp                             0x20005478   Data           0  startup_gd32f450_470.o(STACK)
    sg_tUsartDriveHandle                     0x20020000   Data        2068  usart.o(.RAM_D3)
    sg_arrUasrt2RecvBuf                      0x20020814   Data        2049  usart.o(.RAM_D3)
    tmpBuf_test                              0x20021015   Data        2049  usart.o(.RAM_D3)
    usart2_buf                               0x20021816   Data           1  usart.o(.RAM_D3)
    Lan_Para                                 0x20021818   Data        2120  api_w5500.o(.RAM_D3)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080401ad

  Load Region LR_IROM1 (Base: 0x08040000, Size: 0x00004138, Max: 0x000c0000, ABSOLUTE, COMPRESSED[0x0000211c])

    Execution Region ER_IROM1 (Exec base: 0x08040000, Load base: 0x08040000, Size: 0x00002078, Max: 0x000c0000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08040000   0x08040000   0x000001ac   Data   RO            3    RESET               startup_gd32f450_470.o
    0x080401ac   0x080401ac   0x00000000   Code   RO         7824  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x080401ac   0x080401ac   0x00000004   Code   RO         8140    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x080401b0   0x080401b0   0x00000004   Code   RO         8143    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x080401b4   0x080401b4   0x00000000   Code   RO         8145    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x080401b4   0x080401b4   0x00000000   Code   RO         8147    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x080401b4   0x080401b4   0x00000008   Code   RO         8148    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x080401bc   0x080401bc   0x00000004   Code   RO         8155    .ARM.Collect$$$$0000000E  mc_w.l(entry12b.o)
    0x080401c0   0x080401c0   0x00000000   Code   RO         8150    .ARM.Collect$$$$0000000F  mc_w.l(entry10a.o)
    0x080401c0   0x080401c0   0x00000000   Code   RO         8152    .ARM.Collect$$$$00000011  mc_w.l(entry11a.o)
    0x080401c0   0x080401c0   0x00000004   Code   RO         8141    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x080401c4   0x080401c4   0x00000024   Code   RO            4    .text               startup_gd32f450_470.o
    0x080401e8   0x080401e8   0x00000024   Code   RO         7827    .text               mc_w.l(memcpya.o)
    0x0804020c   0x0804020c   0x00000024   Code   RO         7829    .text               mc_w.l(memseta.o)
    0x08040230   0x08040230   0x0000002c   Code   RO         8157    .text               mc_w.l(uidiv.o)
    0x0804025c   0x0804025c   0x00000062   Code   RO         8159    .text               mc_w.l(uldiv.o)
    0x080402be   0x080402be   0x00000000   Code   RO         8172    .text               mc_w.l(iusefp.o)
    0x080402be   0x080402be   0x0000014e   Code   RO         8173    .text               mf_w.l(dadd.o)
    0x0804040c   0x0804040c   0x000000e4   Code   RO         8175    .text               mf_w.l(dmul.o)
    0x080404f0   0x080404f0   0x000000de   Code   RO         8177    .text               mf_w.l(ddiv.o)
    0x080405ce   0x080405ce   0x00000030   Code   RO         8179    .text               mf_w.l(dfixul.o)
    0x080405fe   0x080405fe   0x00000002   PAD
    0x08040600   0x08040600   0x00000030   Code   RO         8181    .text               mf_w.l(cdrcmple.o)
    0x08040630   0x08040630   0x00000024   Code   RO         8183    .text               mc_w.l(init.o)
    0x08040654   0x08040654   0x0000001e   Code   RO         8185    .text               mc_w.l(llshl.o)
    0x08040672   0x08040672   0x00000020   Code   RO         8187    .text               mc_w.l(llushr.o)
    0x08040692   0x08040692   0x00000024   Code   RO         8189    .text               mc_w.l(llsshr.o)
    0x080406b6   0x080406b6   0x000000ba   Code   RO         8221    .text               mf_w.l(depilogue.o)
    0x08040770   0x08040770   0x0000003a   Code   RO         8233    .text               mc_w.l(__dczerorl.o)
    0x080407aa   0x080407aa   0x00000002   PAD
    0x080407ac   0x080407ac   0x00000068   Code   RO         6338    i.API_Chose_TS5A3359_GAIN  gpio.o
    0x08040814   0x08040814   0x00000050   Code   RO         6339    i.API_LED_GPIO      gpio.o
    0x08040864   0x08040864   0x00000038   Code   RO         6985    i.API_SPI0_Send_Read_Byte  api_w5500.o
    0x0804089c   0x0804089c   0x0000006e   Code   RO         6578    i.AddByteToBuffer   usart.o
    0x0804090a   0x0804090a   0x00000004   Code   RO         5781    i.BusFault_Handler  gd32f4xx_it.o
    0x0804090e   0x0804090e   0x00000002   Code   RO         5782    i.DMA1_Channel0_IRQHandler  gd32f4xx_it.o
    0x08040910   0x08040910   0x00000002   Code   RO         5783    i.DMA1_Channel1_IRQHandler  gd32f4xx_it.o
    0x08040912   0x08040912   0x00000002   Code   RO         5784    i.DMA1_Channel2_IRQHandler  gd32f4xx_it.o
    0x08040914   0x08040914   0x00000002   Code   RO         5785    i.DebugMon_Handler  gd32f4xx_it.o
    0x08040916   0x08040916   0x00000002   Code   RO         5786    i.EXTI10_15_IRQHandler  gd32f4xx_it.o
    0x08040918   0x08040918   0x000000b8   Code   RO         6580    i.FML_USART_RecvTask  usart.o
    0x080409d0   0x080409d0   0x00000068   Code   RO         6340    i.GPIO_Init         gpio.o
    0x08040a38   0x08040a38   0x00000010   Code   RO         5787    i.HardFault_Handler  gd32f4xx_it.o
    0x08040a48   0x08040a48   0x0000000c   Code   RO         7523    i.IINCHIP_SpiSendData  w5500_conf.o
    0x08040a54   0x08040a54   0x00000032   Code   RO         7524    i.IINCHIP_WRITE     w5500_conf.o
    0x08040a86   0x08040a86   0x00000002   PAD
    0x08040a88   0x08040a88   0x00000028   Code   RO         6341    i.Init_GPIO_TS5A339  gpio.o
    0x08040ab0   0x08040ab0   0x00000004   Code   RO         5788    i.MemManage_Handler  gd32f4xx_it.o
    0x08040ab4   0x08040ab4   0x00000002   Code   RO         5789    i.NMI_Handler       gd32f4xx_it.o
    0x08040ab6   0x08040ab6   0x00000002   Code   RO         5790    i.PendSV_Handler    gd32f4xx_it.o
    0x08040ab8   0x08040ab8   0x0000008e   Code   RO         6583    i.ReadBytesToBuffer  usart.o
    0x08040b46   0x08040b46   0x0000001e   Code   RO         6584    i.RecvDataHandler   usart.o
    0x08040b64   0x08040b64   0x00000002   Code   RO         5791    i.SVC_Handler       gd32f4xx_it.o
    0x08040b66   0x08040b66   0x00000002   PAD
    0x08040b68   0x08040b68   0x00000038   Code   RO         6692    i.SysReset_Condition  user_step.o
    0x08040ba0   0x08040ba0   0x00000008   Code   RO         5792    i.SysTick_Handler   gd32f4xx_it.o
    0x08040ba8   0x08040ba8   0x000000c8   Code   RO         5736    i.SystemInit        system_gd32f4xx.o
    0x08040c70   0x08040c70   0x00000002   Code   RO         5793    i.TIMER2_IRQHandler  gd32f4xx_it.o
    0x08040c72   0x08040c72   0x00000002   PAD
    0x08040c74   0x08040c74   0x00000018   Code   RO         5794    i.TIMER3_IRQHandler  gd32f4xx_it.o
    0x08040c8c   0x08040c8c   0x00000018   Code   RO         5795    i.TIMER6_IRQHandler  gd32f4xx_it.o
    0x08040ca4   0x08040ca4   0x00000190   Code   RO         6492    i.TIM_PeriodElapsedCallback  time.o
    0x08040e34   0x08040e34   0x00000028   Code   RO         6585    i.UART_IDLECallBack  usart.o
    0x08040e5c   0x08040e5c   0x0000002c   Code   RO         6586    i.UART_RxCpltCallback  usart.o
    0x08040e88   0x08040e88   0x00000088   Code   RO         6588    i.USART0_Init       usart.o
    0x08040f10   0x08040f10   0x00000030   Code   RO         5796    i.USART2_IRQHandler  gd32f4xx_it.o
    0x08040f40   0x08040f40   0x00000004   Code   RO         5797    i.UsageFault_Handler  gd32f4xx_it.o
    0x08040f44   0x08040f44   0x00000020   Code   RO         8082    i.__0printf         mc_w.l(printfa.o)
    0x08040f64   0x08040f64   0x00000028   Code   RO         6074    i.__NVIC_SetPriority  systick.o
    0x08040f8c   0x08040f8c   0x0000000e   Code   RO         8227    i.__scatterload_copy  mc_w.l(handlers.o)
    0x08040f9a   0x08040f9a   0x00000002   Code   RO         8228    i.__scatterload_null  mc_w.l(handlers.o)
    0x08040f9c   0x08040f9c   0x0000000e   Code   RO         8229    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x08040faa   0x08040faa   0x00000002   PAD
    0x08040fac   0x08040fac   0x00000184   Code   RO         8089    i._fp_digits        mc_w.l(printfa.o)
    0x08041130   0x08041130   0x000006b4   Code   RO         8090    i._printf_core      mc_w.l(printfa.o)
    0x080417e4   0x080417e4   0x00000024   Code   RO         8091    i._printf_post_padding  mc_w.l(printfa.o)
    0x08041808   0x08041808   0x0000002e   Code   RO         8092    i._printf_pre_padding  mc_w.l(printfa.o)
    0x08041836   0x08041836   0x00000002   PAD
    0x08041838   0x08041838   0x00000024   Code   RO         7679    i.default_ip_assign  dhcp.o
    0x0804185c   0x0804185c   0x00000014   Code   RO         7680    i.default_ip_conflict  dhcp.o
    0x08041870   0x08041870   0x00000018   Code   RO         7681    i.default_ip_update  dhcp.o
    0x08041888   0x08041888   0x00000018   Code   RO         6076    i.delay_decrement   systick.o
    0x080418a0   0x080418a0   0x00000024   Code   RO         6590    i.fputc             usart.o
    0x080418c4   0x080418c4   0x0000005e   Code   RO         2656    i.gpio_af_set       gd32f4xx_gpio.o
    0x08041922   0x08041922   0x00000004   Code   RO         2657    i.gpio_bit_reset    gd32f4xx_gpio.o
    0x08041926   0x08041926   0x00000004   Code   RO         2658    i.gpio_bit_set      gd32f4xx_gpio.o
    0x0804192a   0x0804192a   0x0000004e   Code   RO         2664    i.gpio_mode_set     gd32f4xx_gpio.o
    0x08041978   0x08041978   0x00000042   Code   RO         2666    i.gpio_output_options_set  gd32f4xx_gpio.o
    0x080419ba   0x080419ba   0x00000002   PAD
    0x080419bc   0x080419bc   0x00000014   Code   RO         7528    i.iinchip_csoff     w5500_conf.o
    0x080419d0   0x080419d0   0x00000014   Code   RO         7529    i.iinchip_cson      w5500_conf.o
    0x080419e4   0x080419e4   0x00000014   Code   RO         6007    i.main              main.o
    0x080419f8   0x080419f8   0x00000124   Code   RO         3458    i.rcu_clock_freq_get  gd32f4xx_rcu.o
    0x08041b1c   0x08041b1c   0x00000024   Code   RO         3477    i.rcu_periph_clock_enable  gd32f4xx_rcu.o
    0x08041b40   0x08041b40   0x00000024   Code   RO         3480    i.rcu_periph_reset_disable  gd32f4xx_rcu.o
    0x08041b64   0x08041b64   0x00000024   Code   RO         3481    i.rcu_periph_reset_enable  gd32f4xx_rcu.o
    0x08041b88   0x08041b88   0x00000010   Code   RO         7313    i.setGAR            w5500.o
    0x08041b98   0x08041b98   0x0000000e   Code   RO         7314    i.setMR             w5500.o
    0x08041ba6   0x08041ba6   0x00000012   Code   RO         7317    i.setSHAR           w5500.o
    0x08041bb8   0x08041bb8   0x00000012   Code   RO         7318    i.setSIPR           w5500.o
    0x08041bca   0x08041bca   0x00000012   Code   RO         7319    i.setSUBR           w5500.o
    0x08041bdc   0x08041bdc   0x00000008   Code   RO         4337    i.spi_i2s_data_receive  gd32f4xx_spi.o
    0x08041be4   0x08041be4   0x00000004   Code   RO         4338    i.spi_i2s_data_transmit  gd32f4xx_spi.o
    0x08041be8   0x08041be8   0x00000010   Code   RO         4340    i.spi_i2s_flag_get  gd32f4xx_spi.o
    0x08041bf8   0x08041bf8   0x000000fc   Code   RO         5737    i.system_clock_168m_25m_hxtal  system_gd32f4xx.o
    0x08041cf4   0x08041cf4   0x00000008   Code   RO         5738    i.system_clock_config  system_gd32f4xx.o
    0x08041cfc   0x08041cfc   0x00000050   Code   RO         6077    i.systick_config    systick.o
    0x08041d4c   0x08041d4c   0x00000006   Code   RO         4686    i.timer_interrupt_flag_clear  gd32f4xx_timer.o
    0x08041d52   0x08041d52   0x00000018   Code   RO         4687    i.timer_interrupt_flag_get  gd32f4xx_timer.o
    0x08041d6a   0x08041d6a   0x00000002   PAD
    0x08041d6c   0x08041d6c   0x000000e8   Code   RO         5328    i.usart_baudrate_set  gd32f4xx_usart.o
    0x08041e54   0x08041e54   0x0000000a   Code   RO         5332    i.usart_data_receive  gd32f4xx_usart.o
    0x08041e5e   0x08041e5e   0x00000008   Code   RO         5333    i.usart_data_transmit  gd32f4xx_usart.o
    0x08041e66   0x08041e66   0x00000002   PAD
    0x08041e68   0x08041e68   0x000000dc   Code   RO         5334    i.usart_deinit      gd32f4xx_usart.o
    0x08041f44   0x08041f44   0x0000000a   Code   RO         5338    i.usart_enable      gd32f4xx_usart.o
    0x08041f4e   0x08041f4e   0x0000001e   Code   RO         5340    i.usart_flag_get    gd32f4xx_usart.o
    0x08041f6c   0x08041f6c   0x0000001a   Code   RO         5349    i.usart_interrupt_flag_clear  gd32f4xx_usart.o
    0x08041f86   0x08041f86   0x00000038   Code   RO         5350    i.usart_interrupt_flag_get  gd32f4xx_usart.o
    0x08041fbe   0x08041fbe   0x00000010   Code   RO         5365    i.usart_receive_config  gd32f4xx_usart.o
    0x08041fce   0x08041fce   0x00000010   Code   RO         5380    i.usart_transmit_config  gd32f4xx_usart.o
    0x08041fde   0x08041fde   0x00000002   PAD
    0x08041fe0   0x08041fe0   0x00000068   Code   RO         7540    i.wiz_write_buf     w5500_conf.o
    0x08042048   0x08042048   0x00000030   Data   RO         8225    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM2 (Exec base: 0x20000000, Load base: 0x08042078, Size: 0x00000004, Max: 0x00000004, ABSOLUTE, UNINIT)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000        -       0x00000004   Zero   RW         6693    .NoInit             user_step.o


    Execution Region RW_IRAM1 (Exec base: 0x20000004, Load base: 0x08042078, Size: 0x00005474, Max: 0x0001fffc, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000004   0x08042078   0x00000004   Data   RW         5739    .data               system_gd32f4xx.o
    0x20000008   0x0804207c   0x00000004   Data   RW         6078    .data               systick.o
    0x2000000c   0x08042080   0x00000006   Data   RW         6494    .data               time.o
    0x20000012   0x08042086   0x00000002   PAD
    0x20000014   0x08042088   0x0000004c   Data   RW         7690    .data               dhcp.o
    0x20000060   0x080420d4   0x00000004   Data   RW         8156    .data               mc_w.l(stdout.o)
    0x20000064        -       0x00000014   Zero   RW         6493    .bss                time.o
    0x20000078        -       0x00000400   Zero   RW         7689    .bss                dhcp.o
    0x20000478        -       0x00005000   Zero   RW            1    STACK               startup_gd32f450_470.o


    Execution Region RW_IRAM3 (Exec base: 0x20020000, Load base: 0x080420d8, Size: 0x00002060, Max: 0x00030000, ABSOLUTE, COMPRESSED[0x00000044])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20020000   COMPRESSED   0x00001817   Data   RW         6591    .RAM_D3             usart.o
    0x20021817   COMPRESSED   0x00000001   PAD
    0x20021818   COMPRESSED   0x00000848   Data   RW         6989    .RAM_D3             api_w5500.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

         0          0          0          0          0        468   25lc080a.o
        56          6          0       2120          0        885   api_w5500.o
        80         22          0         76       1024       6371   dhcp.o
         0          0          0          0          0      41696   gd32f4xx_adc.o
       246          0          0          0          0       3936   gd32f4xx_gpio.o
       150         26          0          0          0       8550   gd32f4xx_it.o
       400         40          0          0          0       3924   gd32f4xx_rcu.o
        28          0          0          0          0       2094   gd32f4xx_spi.o
        30          0          0          0          0       1496   gd32f4xx_timer.o
       624         18          0          0          0       7494   gd32f4xx_usart.o
       328         32          0          0          0       2249   gpio.o
        20          0          0          0          0        523   main.o
         0          0          0          0          0       6236   socket.o
        36          8        428          0      20480        952   startup_gd32f450_470.o
       460         28          0          4          0       2243   system_gd32f4xx.o
       144         20          0          4          0      31814   systick.o
       400         26          0          6         20       4623   time.o
       722         48          0       6167          0       7978   usart.o
        56         14          0          0          4      30829   user_step.o
        84          0          0          0          0       9746   w5500.o
       206         36          0          0          0       3824   w5500_conf.o

    ----------------------------------------------------------------------
      4084        <USER>        <GROUP>       8380      21528     177931   Object Totals
         0          0         48          0          0          0   (incl. Generated)
        14          0          0          3          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        58          0          0          0          0          0   __dczerorl.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         4          0          0          0          0          0   entry12b.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
        36          0          0          0          0         68   memcpya.o
        36          0          0          0          0        108   memseta.o
      2218         90          0          0          0        464   printfa.o
         0          0          0          4          0          0   stdout.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdrcmple.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        48          0          0          0          0         68   dfixul.o
       228          0          0          0          0         96   dmul.o

    ----------------------------------------------------------------------
      3752        <USER>          <GROUP>          4          0       1740   Library Totals
         8          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      2678        106          0          4          0       1084   mc_w.l
      1066          0          0          0          0        656   mf_w.l

    ----------------------------------------------------------------------
      3752        <USER>          <GROUP>          4          0       1740   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      7836        430        476       8384      21528     171971   Grand Totals
      7836        430        476        164      21528     171971   ELF Image Totals (compressed)
      7836        430        476        164          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                 8312 (   8.12kB)
    Total RW  Size (RW Data + ZI Data)             29912 (  29.21kB)
    Total ROM Size (Code + RO Data + RW Data)       8476 (   8.28kB)

==============================================================================

