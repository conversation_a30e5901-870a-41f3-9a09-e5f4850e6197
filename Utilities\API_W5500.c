#include "API_W5500.h"
#include "w5500.h"
#include "w5500_conf.h"
#include "w5500_conf.h"
#include "gpio.h"
#include "utility.h"
#include "dhcp.h"
#include "tcp_client.h"
#include "socket.h"
#include "user_step.h"
#define SPI0_DUTM    0x00
extern uint8_t dhcp_ok;
extern uint8 Conflict_flag ;
extern uint8_t Soft_Ver[3];
extern uint8_t HardWare_Ver[2];

__attribute__((section (".RAM_D3"))) LAN_PARA Lan_Para ={0};//??????????


void API_W5500_SPI0_Init(void)
{
    spi_parameter_struct spi_init_struct;

	  rcu_periph_clock_enable(RCU_GPIOA);
    rcu_periph_clock_enable(RCU_GPIOB);
    rcu_periph_clock_enable(RCU_SPI0);

	
    
    gpio_af_set(GPIOB, GPIO_AF_5, GPIO_PIN_3| GPIO_PIN_4| GPIO_PIN_5);//spi2 PB5 PB3,PB4
    gpio_mode_set(GPIOB, GPIO_MODE_AF, GPIO_PUPD_NONE,  GPIO_PIN_3| GPIO_PIN_4| GPIO_PIN_5);
    gpio_output_options_set(GPIOB, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_3| GPIO_PIN_4| GPIO_PIN_5);

    gpio_mode_set(GPIOA, GPIO_MODE_OUTPUT, GPIO_PUPD_NONE, GPIO_PIN_15);//CS????????PB2
    gpio_output_options_set(GPIOA, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_15);

      SET_SPI0_CS_Hi();    /* chip select invalid */

    /* SPI0 parameter config */
    spi_init_struct.trans_mode           = SPI_TRANSMODE_FULLDUPLEX;
    spi_init_struct.device_mode          = SPI_MASTER;
    spi_init_struct.frame_size           = SPI_FRAMESIZE_8BIT;
    spi_init_struct.clock_polarity_phase = SPI_CK_PL_LOW_PH_1EDGE;
    spi_init_struct.nss                  = SPI_NSS_SOFT;
    spi_init_struct.prescale             = SPI_PSC_32;
    spi_init_struct.endian               = SPI_ENDIAN_MSB;
    spi_init(SPI0, &spi_init_struct);	
    spi_enable(SPI0);

		
}

void API_W5500_GPIO_Init(void)//????W5500???????????????
{
    rcu_periph_clock_enable(W5500_RST_RCU);
	  gpio_mode_set(W5500_RST_PORT, GPIO_MODE_OUTPUT, GPIO_PUPD_NONE, W5500_RST_PIN);//PB4??????????
    gpio_output_options_set(W5500_RST_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, W5500_RST_PIN);
}



void API_Init_Net_Parameters(void)//????????????
{
		printf("[NET] Using fixed test parameters (EEPROM bypassed)\r\n");

		// ???????????????W5500????
		Lan_Para.Sub_Mask[0]   =   255;
		Lan_Para.Sub_Mask[1]   =   255;
		Lan_Para.Sub_Mask[2]   =   255;
		Lan_Para.Sub_Mask[3]   =   0;
		Lan_Para.Gateway_IP[0] =   192;
		Lan_Para.Gateway_IP[1] =   168;
		Lan_Para.Gateway_IP[2] =   1;
		Lan_Para.Gateway_IP[3] =   1;
		printf("[NET] Subnet: *************, Gateway: ***********\r\n");
		// ???IP????????W5500????
		// ????IP (DHCP???????????????????)
		Lan_Para.IP_B0[0] = 192;
		Lan_Para.IP_B0[1] = 168;
		Lan_Para.IP_B0[2] = 1;
		Lan_Para.IP_B0[3] = 100;
		Lan_Para.Port_B0 = 5000;

		// ?????????IP????
		Lan_Para.IP_S[0] = 192;  // ?????????????????
		Lan_Para.IP_S[1] = 168;
		Lan_Para.IP_S[2] = 1;
		Lan_Para.IP_S[3] = 10;   // ????????????PC????????????
		Lan_Para.Port_S = 8080;  // ??????????

		printf("[NET] Local IP: %d.%d.%d.%d:%d (DHCP will override)\r\n",
		       Lan_Para.IP_B0[0], Lan_Para.IP_B0[1], Lan_Para.IP_B0[2], Lan_Para.IP_B0[3], Lan_Para.Port_B0);
		printf("[NET] Test Server: %d.%d.%d.%d:%d\r\n",
		       Lan_Para.IP_S[0], Lan_Para.IP_S[1], Lan_Para.IP_S[2], Lan_Para.IP_S[3], Lan_Para.Port_S);

		// ???????MAC?????????? (????RNG????)
		Lan_Para.MAC[0] = 0x02;  // ?????????MAC???
		Lan_Para.MAC[1] = 0x08;
		Lan_Para.MAC[2] = 0xDC;
		Lan_Para.MAC[3] = 0x11;
		Lan_Para.MAC[4] = 0x22;
		Lan_Para.MAC[5] = 0x33;
		printf("[NET] Fixed MAC: %02X:%02X:%02X:%02X:%02X:%02X\r\n",
		       Lan_Para.MAC[0], Lan_Para.MAC[1], Lan_Para.MAC[2],
		       Lan_Para.MAC[3], Lan_Para.MAC[4], Lan_Para.MAC[5]);

}



void API_Init_LAN(void)
{
  	API_W5500_SPI0_Init();
    API_W5500_GPIO_Init();//????W5500???????????????
   	API_Init_Net_Parameters();//?????????

}

uint8_t  API_SPI0_Send_Read_Byte(uint8_t  byte)//SPI0???????1?????????
{   
    while (spi_i2s_flag_get(SPI0, SPI_FLAG_TBE) == RESET); /*!< transmit buffer empty flag */
    spi_i2s_data_transmit(SPI0, byte);
    while (spi_i2s_flag_get(SPI0, SPI_FLAG_RBNE) == RESET);/*!< receive buffer not empty flag */
    return (spi_i2s_data_receive(SPI0));
}

void API_W5500_Reset(void)
{
		printf("[W5500] Starting W5500 reset and initialization...\r\n");
		   dhcp_ok=0;
	   Conflict_flag = 1;
		Lan_Para.f_LogOnSever=0;//?????????
		printf("[W5500] Hardware reset...\r\n");
		reset_w5500();                        // w5500???????

		// �ȴ�W5500��λ���
		delay_1ms(100);  // �ȴ�100msȷ����λ���
		printf("[W5500] Reset completed, waiting for stabilization...\r\n");

		// 测试SPI通信 - 尝试读取一些基本寄存器
		printf("[W5500] Testing SPI communication...\r\n");

		// 测试简单的SPI读写
		printf("[SPI] Testing basic SPI communication...\r\n");
		uint8_t test_data[6] = {0};
		getSHAR(test_data);  // 读取MAC地址寄存器
		printf("[SPI] Initial MAC read: %02X:%02X:%02X:%02X:%02X:%02X\r\n",
		       test_data[0], test_data[1], test_data[2], test_data[3], test_data[4], test_data[5]);

		// 检查是否全为0xFF或0x00（可能表示SPI通信问题）
		if((test_data[0] == 0xFF && test_data[1] == 0xFF) ||
		   (test_data[0] == 0x00 && test_data[1] == 0x00)) {
			printf("[SPI] WARNING: SPI communication may have issues!\r\n");
		}

		printf("[W5500] PHY check...\r\n");
		PHY_check();                          // ?????????

		// ????????MAC??? (?????��?????????????)
		Lan_Para.MAC[0] = 0x02;  // ?????????MAC???
		Lan_Para.MAC[1] = 0x08;
		Lan_Para.MAC[2] = 0xDC;
		Lan_Para.MAC[3] = 0x11;
		Lan_Para.MAC[4] = 0x22;
		Lan_Para.MAC[5] = 0x33;
		printf("[W5500] Setting MAC address: %02X:%02X:%02X:%02X:%02X:%02X\r\n",
		       Lan_Para.MAC[0], Lan_Para.MAC[1], Lan_Para.MAC[2],
		       Lan_Para.MAC[3], Lan_Para.MAC[4], Lan_Para.MAC[5]);
		set_w5500_mac();                      // ????w5500MAC???
		printf("[W5500] Initializing socket buffers...\r\n");
		socket_buf_init(txsize, rxsize);      // ?????8??Socket??????????????
		printf("[DHCP] Starting DHCP client...\r\n");
		printf("[DHCP] Waiting for network cable connection...\r\n");
		uint32_t dhcp_timeout = 0;
		while(dhcp_ok == 0 && dhcp_timeout < 30000) {  // 30�볬ʱ
			DHCP_run();
			dhcp_timeout++;
			if(dhcp_timeout % 1000 == 0) {
				printf(".");  // ÿ1000��ѭ����ʾһ����
			}
			if(dhcp_timeout % 5000 == 0) {
				printf(" %ds", dhcp_timeout/1000);  // ÿ5����ʾʱ��
				// ���PHY״̬
				uint8_t phy_status = getPHYStatus();
				if(phy_status & 0x01) {
					printf(" [PHY:UP]");
				} else {
					printf(" [PHY:DOWN]");
				}
			}
		}

		if(dhcp_ok) {
			printf("\r\n[DHCP] DHCP configuration successful!\r\n");
		} else {
			printf("\r\n[DHCP] DHCP timeout! Using static IP configuration.\r\n");
			// ?????IP???????????
			setSIPR(Lan_Para.IP_B0);    // ???????IP
			setSUBR(Lan_Para.Sub_Mask); // ????????????
			setGAR(Lan_Para.Gateway_IP); // ????????
			printf("[STATIC] IP: %d.%d.%d.%d\r\n",
			       Lan_Para.IP_B0[0], Lan_Para.IP_B0[1], Lan_Para.IP_B0[2], Lan_Para.IP_B0[3]);
			printf("[STATIC] Gateway: %d.%d.%d.%d\r\n",
			       Lan_Para.Gateway_IP[0], Lan_Para.Gateway_IP[1], Lan_Para.Gateway_IP[2], Lan_Para.Gateway_IP[3]);
			printf("[STATIC] Static IP configuration completed!\r\n");
		}
}

void API_PHY_Check(void)
{
	 static uint8_t i=0;
	 uint8_t TempStatus;
	 TempStatus = 0x01&getPHYStatus();
	 if(TempStatus ==0)
	 {
	    i++;
		  LED_B_ON();LED_R_ON();LED_G_ON();
		  if(i>10)
			{
				   LED_B_OFF();LED_R_OFF();LED_G_OFF();
				  i=0;
  				SysReset_Condition(0xAA553344);
			}
			Lan_Para.f_Connect =0;
			printf("[PHY] Network cable disconnected, reset in 30s: %d\r\n",Lan_Para.GetDHCP_Cnt_1s);
	 }
	 else
	 {
			if(i>0)
			{
					i=0;
				  
				  LED_B_OFF();LED_R_OFF();LED_G_OFF();
			  	printf("[PHY] Network cable connected successfully!\r\n");
			}
			Lan_Para.f_Connect =1;
	 }
}





