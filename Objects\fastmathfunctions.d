.\objects\fastmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\FastMathFunctions\FastMathFunctions.c
.\objects\fastmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\FastMathFunctions\arm_cos_f32.c
.\objects\fastmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/fast_math_functions.h
.\objects\fastmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\arm_math_types.h
.\objects\fastmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h
.\objects\fastmathfunctions.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\fastmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h
.\objects\fastmathfunctions.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
.\objects\fastmathfunctions.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\math.h
.\objects\fastmathfunctions.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\float.h
.\objects\fastmathfunctions.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\limits.h
.\objects\fastmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\arm_math_memory.h
.\objects\fastmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/none.h
.\objects\fastmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\dsp/utils.h
.\objects\fastmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Include\arm_common_tables.h
.\objects\fastmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\FastMathFunctions\arm_cos_q15.c
.\objects\fastmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\FastMathFunctions\arm_cos_q31.c
.\objects\fastmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\FastMathFunctions\arm_sin_f32.c
.\objects\fastmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\FastMathFunctions\arm_sin_q15.c
.\objects\fastmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\FastMathFunctions\arm_sin_q31.c
.\objects\fastmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\FastMathFunctions\arm_sqrt_q15.c
.\objects\fastmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\FastMathFunctions\arm_sqrt_q31.c
.\objects\fastmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\FastMathFunctions\arm_vexp_f32.c
.\objects\fastmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\FastMathFunctions\arm_vlog_f32.c
.\objects\fastmathfunctions.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\DSP\Source\FastMathFunctions\arm_divide_q15.c
.\objects\fastmathfunctions.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdlib.h
