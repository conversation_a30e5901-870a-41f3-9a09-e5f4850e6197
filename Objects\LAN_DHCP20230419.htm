<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\Objects\LAN_DHCP20230419.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\Objects\LAN_DHCP20230419.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060750: Last Updated: Fri May 05 17:10:52 2023
<BR><P>
<H3>Maximum Stack Usage =      25424 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
main &rArr; ADC_RunTask &rArr; main_program &rArr; Heart_quality_process &rArr; arm_rfft_fast_f32_app &rArr; arm_rfft_fast_f32 &rArr; arm_cfft_f32 &rArr; arm_cfft_radix8by4_f32 &rArr; arm_radix8_butterfly_f32
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[48]">ADC_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[48]">ADC_IRQHandler</a><BR>
 <LI><a href="#[c1]">arm_quick_sort_core_f32</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[c1]">arm_quick_sort_core_f32</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[48]">ADC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[30]">BusFault_Handler</a> from gd32f4xx_it.o(i.BusFault_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4c]">CAN0_EWMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4a]">CAN0_RX0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4b]">CAN0_RX1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[49]">CAN0_TX_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[78]">CAN1_EWMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[76]">CAN1_RX0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[77]">CAN1_RX1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[75]">CAN1_TX_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[84]">DCI_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[41]">DMA0_Channel0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[42]">DMA0_Channel1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[43]">DMA0_Channel2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[44]">DMA0_Channel3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[45]">DMA0_Channel4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[46]">DMA0_Channel5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[47]">DMA0_Channel6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[65]">DMA0_Channel7_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[6e]">DMA1_Channel0_IRQHandler</a> from gd32f4xx_it.o(i.DMA1_Channel0_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[6f]">DMA1_Channel1_IRQHandler</a> from gd32f4xx_it.o(i.DMA1_Channel1_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[70]">DMA1_Channel2_IRQHandler</a> from gd32f4xx_it.o(i.DMA1_Channel2_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[71]">DMA1_Channel3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[72]">DMA1_Channel4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[7a]">DMA1_Channel5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[7b]">DMA1_Channel6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[7c]">DMA1_Channel7_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[33]">DebugMon_Handler</a> from gd32f4xx_it.o(i.DebugMon_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[73]">ENET_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[74]">ENET_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[66]">EXMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3c]">EXTI0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5e]">EXTI10_15_IRQHandler</a> from gd32f4xx_it.o(i.EXTI10_15_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3d]">EXTI1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3e]">EXTI2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3f]">EXTI3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[40]">EXTI4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4d]">EXTI5_9_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3a]">FMC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[86]">FPU_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2e]">HardFault_Handler</a> from gd32f4xx_it.o(i.HardFault_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[56]">I2C0_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[55]">I2C0_EV_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[58]">I2C1_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[57]">I2C1_EV_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[7f]">I2C2_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[7e]">I2C2_EV_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[8e]">IPA_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[37]">LVD_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2f]">MemManage_Handler</a> from gd32f4xx_it.o(i.MemManage_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2d]">NMI_Handler</a> from gd32f4xx_it.o(i.NMI_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[13]">OnPackBLEADVDATA_Cmd</a> from esp32_wifi.o(i.OnPackBLEADVDATA_Cmd) referenced 2 times from esp32_wifi.o(.data)
 <LI><a href="#[12]">OnPackBLENAME_Cmd</a> from esp32_wifi.o(i.OnPackBLENAME_Cmd) referenced 2 times from esp32_wifi.o(.data)
 <LI><a href="#[9]">OnPackBuildTCPOrUDPCmd</a> from esp32_wifi.o(i.OnPackBuildTCPOrUDPCmd) referenced 2 times from esp32_wifi.o(.data)
 <LI><a href="#[4]">OnPackCWModeCmd</a> from esp32_wifi.o(i.OnPackCWModeCmd) referenced 2 times from esp32_wifi.o(.data)
 <LI><a href="#[6]">OnPackJAPCmd</a> from esp32_wifi.o(i.OnPackJAPCmd) referenced 2 times from esp32_wifi.o(.data)
 <LI><a href="#[2]">OnPackRFPOWERCmd</a> from esp32_wifi.o(i.OnPackRFPOWERCmd) referenced 2 times from esp32_wifi.o(.data)
 <LI><a href="#[f]">OnPackSetSNTPCFG_Cmd</a> from esp32_wifi.o(i.OnPackSetSNTPCFG_Cmd) referenced 2 times from esp32_wifi.o(.data)
 <LI><a href="#[d]">OnPackTCPOrUDPData</a> from esp32_wifi.o(i.OnPackTCPOrUDPData) referenced 2 times from esp32_wifi.o(.data)
 <LI><a href="#[b]">OnPackTCPOrUDPDataCmd</a> from esp32_wifi.o(i.OnPackTCPOrUDPDataCmd) referenced 2 times from esp32_wifi.o(.data)
 <LI><a href="#[a]">OnUnpackBuildTCPOrUDPAck</a> from esp32_wifi.o(i.OnUnpackBuildTCPOrUDPAck) referenced 2 times from esp32_wifi.o(.data)
 <LI><a href="#[5]">OnUnpackCWModeAck</a> from esp32_wifi.o(i.OnUnpackCWModeAck) referenced 2 times from esp32_wifi.o(.data)
 <LI><a href="#[11]">OnUnpackDomainIPAck</a> from esp32_wifi.o(i.OnUnpackDomainIPAck) referenced 2 times from esp32_wifi.o(.data)
 <LI><a href="#[7]">OnUnpackJAPAck</a> from esp32_wifi.o(i.OnUnpackJAPAck) referenced 2 times from esp32_wifi.o(.data)
 <LI><a href="#[0]">OnUnpackRSTAck</a> from esp32_wifi.o(i.OnUnpackRSTAck) referenced 4 times from esp32_wifi.o(.data)
 <LI><a href="#[10]">OnUnpackSntpTimeAck</a> from esp32_wifi.o(i.OnUnpackSntpTimeAck) referenced 2 times from esp32_wifi.o(.data)
 <LI><a href="#[c]">OnUnpackTCPOrUDPDataCmdAck</a> from esp32_wifi.o(i.OnUnpackTCPOrUDPDataCmdAck) referenced 2 times from esp32_wifi.o(.data)
 <LI><a href="#[e]">OnUnpackTCPOrUDPDataSendAck</a> from esp32_wifi.o(i.OnUnpackTCPOrUDPDataSendAck) referenced 2 times from esp32_wifi.o(.data)
 <LI><a href="#[1]">OnUnpackVERSIONAck</a> from esp32_wifi.o(i.OnUnpackVERSIONAck) referenced 2 times from esp32_wifi.o(.data)
 <LI><a href="#[8]">OnUnpackyNetConnectStatusAck</a> from esp32_wifi.o(i.OnUnpackyNetConnectStatusAck) referenced 2 times from esp32_wifi.o(.data)
 <LI><a href="#[34]">PendSV_Handler</a> from gd32f4xx_it.o(i.PendSV_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3b]">RCU_CTC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5f]">RTC_Alarm_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[39]">RTC_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[2c]">Reset_Handler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[67]">SDIO_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[59]">SPI0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5a]">SPI1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[69]">SPI2_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[89]">SPI3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[8a]">SPI4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[8b]">SPI5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[32]">SVC_Handler</a> from gd32f4xx_it.o(i.SVC_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[35]">SysTick_Handler</a> from gd32f4xx_it.o(i.SysTick_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[90]">SystemInit</a> from system_gd32f4xx.o(i.SystemInit) referenced from startup_gd32f450_470.o(.text)
 <LI><a href="#[38]">TAMPER_STAMP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4e]">TIMER0_BRK_TIMER8_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[51]">TIMER0_Channel_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[50]">TIMER0_TRG_CMT_TIMER10_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[4f]">TIMER0_UP_TIMER9_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[52]">TIMER1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[53]">TIMER2_IRQHandler</a> from gd32f4xx_it.o(i.TIMER2_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[54]">TIMER3_IRQHandler</a> from gd32f4xx_it.o(i.TIMER3_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[68]">TIMER4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[6c]">TIMER5_DAC_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[6d]">TIMER6_IRQHandler</a> from gd32f4xx_it.o(i.TIMER6_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[61]">TIMER7_BRK_TIMER11_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[64]">TIMER7_Channel_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[63]">TIMER7_TRG_CMT_TIMER13_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[62]">TIMER7_UP_TIMER12_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[8d]">TLI_ER_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[8c]">TLI_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[85]">TRNG_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[6a]">UART3_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[6b]">UART4_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[87]">UART6_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[88]">UART7_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5b]">USART0_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5c]">USART1_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[5d]">USART2_IRQHandler</a> from gd32f4xx_it.o(i.USART2_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[7d]">USART5_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[79]">USBFS_IRQHandler</a> from gd32f4xx_it.o(i.USBFS_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[60]">USBFS_WKUP_IRQHandler</a> from gd32f4xx_it.o(i.USBFS_WKUP_IRQHandler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[81]">USBHS_EP1_In_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[80]">USBHS_EP1_Out_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[83]">USBHS_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[82]">USBHS_WKUP_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[31]">UsageFault_Handler</a> from gd32f4xx_it.o(i.UsageFault_Handler) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[93]">UserProcess_BLEADVSTOPCmd</a> from esp32_wifi.o(i.UserProcess_BLEADVSTOPCmd) referenced from user_step.o(i.IPD_Command)
 <LI><a href="#[92]">UserProcess_UpDataOkCmd</a> from esp32_wifi.o(i.UserProcess_UpDataOkCmd) referenced from user_step.o(i.IPD_Command)
 <LI><a href="#[36]">WWDGT_IRQHandler</a> from startup_gd32f450_470.o(.text) referenced from startup_gd32f450_470.o(RESET)
 <LI><a href="#[3]">XOnPackRFPOWERCmd</a> from esp32_wifi.o(i.XOnPackRFPOWERCmd) referenced 2 times from esp32_wifi.o(.data)
 <LI><a href="#[91]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_gd32f450_470.o(.text)
 <LI><a href="#[95]">_snputc</a> from printfa.o(i._snputc) referenced from printfa.o(i.__0snprintf)
 <LI><a href="#[96]">_sputc</a> from printfa.o(i._sputc) referenced from printfa.o(i.__0sprintf)
 <LI><a href="#[21]">_usb_config_desc_get</a> from usbd_enum.o(i._usb_config_desc_get) referenced 2 times from usbd_enum.o(.data)
 <LI><a href="#[20]">_usb_dev_desc_get</a> from usbd_enum.o(i._usb_dev_desc_get) referenced 2 times from usbd_enum.o(.data)
 <LI><a href="#[15]">_usb_std_clearfeature</a> from usbd_enum.o(i._usb_std_clearfeature) referenced 2 times from usbd_enum.o(.data)
 <LI><a href="#[1b]">_usb_std_getconfiguration</a> from usbd_enum.o(i._usb_std_getconfiguration) referenced 2 times from usbd_enum.o(.data)
 <LI><a href="#[19]">_usb_std_getdescriptor</a> from usbd_enum.o(i._usb_std_getdescriptor) referenced 2 times from usbd_enum.o(.data)
 <LI><a href="#[1d]">_usb_std_getinterface</a> from usbd_enum.o(i._usb_std_getinterface) referenced 2 times from usbd_enum.o(.data)
 <LI><a href="#[14]">_usb_std_getstatus</a> from usbd_enum.o(i._usb_std_getstatus) referenced 2 times from usbd_enum.o(.data)
 <LI><a href="#[16]">_usb_std_reserved</a> from usbd_enum.o(i._usb_std_reserved) referenced 4 times from usbd_enum.o(.data)
 <LI><a href="#[18]">_usb_std_setaddress</a> from usbd_enum.o(i._usb_std_setaddress) referenced 2 times from usbd_enum.o(.data)
 <LI><a href="#[1c]">_usb_std_setconfiguration</a> from usbd_enum.o(i._usb_std_setconfiguration) referenced 2 times from usbd_enum.o(.data)
 <LI><a href="#[1a]">_usb_std_setdescriptor</a> from usbd_enum.o(i._usb_std_setdescriptor) referenced 2 times from usbd_enum.o(.data)
 <LI><a href="#[17]">_usb_std_setfeature</a> from usbd_enum.o(i._usb_std_setfeature) referenced 2 times from usbd_enum.o(.data)
 <LI><a href="#[1e]">_usb_std_setinterface</a> from usbd_enum.o(i._usb_std_setinterface) referenced 2 times from usbd_enum.o(.data)
 <LI><a href="#[1f]">_usb_std_synchframe</a> from usbd_enum.o(i._usb_std_synchframe) referenced 2 times from usbd_enum.o(.data)
 <LI><a href="#[22]">_usb_str_desc_get</a> from usbd_enum.o(i._usb_str_desc_get) referenced 2 times from usbd_enum.o(.data)
 <LI><a href="#[24]">cdc_acm_deinit</a> from cdc_acm_core.o(i.cdc_acm_deinit) referenced 2 times from cdc_acm_core.o(.data)
 <LI><a href="#[27]">cdc_acm_in</a> from cdc_acm_core.o(i.cdc_acm_in) referenced 2 times from cdc_acm_core.o(.data)
 <LI><a href="#[23]">cdc_acm_init</a> from cdc_acm_core.o(i.cdc_acm_init) referenced 2 times from cdc_acm_core.o(.data)
 <LI><a href="#[28]">cdc_acm_out</a> from cdc_acm_core.o(i.cdc_acm_out) referenced 2 times from cdc_acm_core.o(.data)
 <LI><a href="#[25]">cdc_acm_req</a> from cdc_acm_core.o(i.cdc_acm_req) referenced 2 times from cdc_acm_core.o(.data)
 <LI><a href="#[26]">cdc_ctlx_out</a> from cdc_acm_core.o(i.cdc_ctlx_out) referenced 2 times from cdc_acm_core.o(.data)
 <LI><a href="#[29]">default_ip_assign</a> from dhcp.o(i.default_ip_assign) referenced 2 times from dhcp.o(.data)
 <LI><a href="#[2b]">default_ip_conflict</a> from dhcp.o(i.default_ip_conflict) referenced 2 times from dhcp.o(.data)
 <LI><a href="#[2a]">default_ip_update</a> from dhcp.o(i.default_ip_update) referenced 2 times from dhcp.o(.data)
 <LI><a href="#[94]">fputc</a> from usart.o(i.fputc) referenced from printfa.o(i.__0printf)
 <LI><a href="#[8f]">main</a> from main.o(i.main) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[91]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(.text)
</UL>
<P><STRONG><a name="[25a]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[97]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[b2]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[25b]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[25c]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[25d]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[25e]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000D))

<P><STRONG><a name="[25f]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[2c]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>ADC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>CAN0_EWMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>CAN0_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>CAN0_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>CAN0_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[78]"></a>CAN1_EWMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[76]"></a>CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[77]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[75]"></a>CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[84]"></a>DCI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>DMA0_Channel0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>DMA0_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>DMA0_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>DMA0_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>DMA0_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>DMA0_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>DMA0_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[65]"></a>DMA0_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[71]"></a>DMA1_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[72]"></a>DMA1_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[7a]"></a>DMA1_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[7b]"></a>DMA1_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[7c]"></a>DMA1_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[73]"></a>ENET_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[74]"></a>ENET_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[66]"></a>EXMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4d]"></a>EXTI5_9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>FMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[86]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>I2C0_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>I2C0_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[7f]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[7e]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[8e]"></a>IPA_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>LVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>RCU_CTC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5f]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[67]"></a>SDIO_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>SPI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[69]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[89]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[8a]"></a>SPI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[8b]"></a>SPI5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>TAMPER_STAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>TIMER0_BRK_TIMER8_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>TIMER0_Channel_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>TIMER0_TRG_CMT_TIMER10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>TIMER0_UP_TIMER9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>TIMER1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[68]"></a>TIMER4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[6c]"></a>TIMER5_DAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[61]"></a>TIMER7_BRK_TIMER11_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[64]"></a>TIMER7_Channel_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[63]"></a>TIMER7_TRG_CMT_TIMER13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[62]"></a>TIMER7_UP_TIMER12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[8d]"></a>TLI_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[8c]"></a>TLI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[85]"></a>TRNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[6a]"></a>UART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[6b]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[87]"></a>UART6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[88]"></a>UART7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5b]"></a>USART0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>USART1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[7d]"></a>USART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[81]"></a>USBHS_EP1_In_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[80]"></a>USBHS_EP1_Out_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[83]"></a>USBHS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[82]"></a>USBHS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>WWDGT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450_470.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[144]"></a>__aeabi_memcpy</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CDC_Command
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_WriteCpuFlash
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP32_SendData_Add_Plus
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP32_SendData_Add
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP32BLE_SendData_Add
<LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnUnpackVERSIONAck
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP32_Command_Read_Plus
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP32_Command_Read
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP32BLE_Command_Read
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_DevID
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_DevID
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FML_USART_RecvTask
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadBytesToBuffer
</UL>

<P><STRONG><a name="[b5]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[251]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_reset
<LI><a href="#[230]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_basic_init
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_selection_sort_f32
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_quick_sort_f32
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_insertion_sort_f32
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_heap_sort_f32
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_bubble_sort_f32
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_bitonic_sort_f32
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;freq_modify
</UL>

<P><STRONG><a name="[260]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[9a]"></a>__aeabi_memset</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset$wrapper
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>

<P><STRONG><a name="[261]"></a>__aeabi_memset4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[262]"></a>__aeabi_memset8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[99]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_RunTask
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IPD_Command
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetWifiInFo
<LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnUnpackVERSIONAck
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_DevID
</UL>

<P><STRONG><a name="[e9]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WIFI_Send_AD
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_RunTask
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IPD_Command
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CDC_Command
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_RunTask
<LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnUnpackJAPAck
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FML_USART_RecvTask
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Sig_QA
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Heart_FreqProcess
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Findpeak
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Breath_FreqProcess
<LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;error_process
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_rfft_fast_f32_app
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Heart_quality_process
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Heart_peak_Rate_new
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Fs_Power
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Energy_process
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Breath_rate_PSD
<LI><a href="#[251]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_reset
<LI><a href="#[230]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_basic_init
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_29CMD
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_28CMD
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_26CMD
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_24CMD
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_23CMD
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_21CMD
<LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;peak_diff_process
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mid_filter
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;freq_modify
</UL>

<P><STRONG><a name="[263]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[9b]"></a>_memset$wrapper</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[17a]"></a>strstr</STRONG> (Thumb, 36 bytes, Stack size 12 bytes, strstr.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = strstr
</UL>
<BR>[Called By]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_RunTask
<LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnUnpackyNetConnectStatusAck
<LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnUnpackVERSIONAck
<LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnUnpackTCPOrUDPDataSendAck
<LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnUnpackTCPOrUDPDataCmdAck
<LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnUnpackSntpTimeAck
<LI><a href="#[0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnUnpackRSTAck
<LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnUnpackJAPAck
<LI><a href="#[11]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnUnpackDomainIPAck
<LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnUnpackCWModeAck
<LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnUnpackBuildTCPOrUDPAck
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP_GetSntpTimeInfo
</UL>

<P><STRONG><a name="[18d]"></a>strncpy</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, strncpy.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = strncpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_RunTask
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetWifiInFo
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_HardVer
</UL>

<P><STRONG><a name="[104]"></a>strchr</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, strchr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_AnalysisIpPortAddr
</UL>

<P><STRONG><a name="[228]"></a>strlen</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, strlen.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;split
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_DHCP_REQUEST
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_DHCP_DISCOVER
</UL>

<P><STRONG><a name="[21d]"></a>strcmp</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, strcmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = strcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;week_str2num
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;month_str2num
</UL>

<P><STRONG><a name="[116]"></a>strcpy</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, strcpy.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_LAN_Info_To_String
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_DHCP_REQUEST
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_DHCP_DISCOVER
</UL>

<P><STRONG><a name="[22c]"></a>strtok</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, strtok.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = strtok
</UL>
<BR>[Called By]<UL><LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;split
</UL>

<P><STRONG><a name="[9c]"></a>atoi</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, atoi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = atoi &rArr; strtol &rArr; _strtoul
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strtol
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnUnpackJAPAck
<LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnUnpackCWModeAck
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_AnalysisIpPortAddr
</UL>

<P><STRONG><a name="[9f]"></a>__aeabi_dadd</STRONG> (Thumb, 322 bytes, Stack size 48 bytes, dadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_lasr
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Moving_process
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Heart_peak_Rate_new
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Energy_process
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Breath_rate_PSD
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[a4]"></a>__aeabi_dsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[a5]"></a>__aeabi_drsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[a6]"></a>__aeabi_dmul</STRONG> (Thumb, 228 bytes, Stack size 48 bytes, dmul.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC2_Set_Vol
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC1_Set_Vol
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[a7]"></a>__aeabi_ddiv</STRONG> (Thumb, 222 bytes, Stack size 32 bytes, ddiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __aeabi_ddiv &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC2_Set_Vol
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC1_Set_Vol
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[a8]"></a>__aeabi_ui2d</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, dfltui.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = __aeabi_ui2d &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC2_Set_Vol
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC1_Set_Vol
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Heart_peak_Rate_new
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Breath_rate_PSD
</UL>

<P><STRONG><a name="[a9]"></a>__aeabi_d2uiz</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, dfixui.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_d2uiz
</UL>
<BR>[Calls]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
</UL>
<BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC2_Set_Vol
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC1_Set_Vol
</UL>

<P><STRONG><a name="[13d]"></a>__aeabi_f2d</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, f2d.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PD_AUTO_Process
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Sig_QA
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Moving_process
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Heart_pretreatment
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Heart_peak_Rate_new
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Energy_process
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Breath_rate_PSD
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;freq_modify
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Pearson_corr
</UL>

<P><STRONG><a name="[264]"></a>__aeabi_cdcmpeq</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, cdcmple.o(.text), UNUSED)

<P><STRONG><a name="[1ab]"></a>__aeabi_cdcmple</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, cdcmple.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PD_AUTO_Process
</UL>

<P><STRONG><a name="[13e]"></a>__aeabi_cdrcmple</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, cdrcmple.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Heart_peak_Rate_new
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Breath_rate_PSD
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[ab]"></a>__aeabi_d2f</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, d2f.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_d2f
</UL>
<BR>[Calls]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_round
</UL>
<BR>[Called By]<UL><LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Sig_QA
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Moving_process
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Heart_pretreatment
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Energy_process
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;freq_modify
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Pearson_corr
</UL>

<P><STRONG><a name="[265]"></a>__aeabi_uidiv</STRONG> (Thumb, 0 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)

<P><STRONG><a name="[1ee]"></a>__aeabi_uidivmod</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[ad]"></a>__aeabi_uldivmod</STRONG> (Thumb, 98 bytes, Stack size 40 bytes, uldiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[a0]"></a>__aeabi_llsl</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, llshl.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>

<P><STRONG><a name="[266]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[aa]"></a>__aeabi_llsr</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, llushr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2uiz
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>

<P><STRONG><a name="[267]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[a1]"></a>__aeabi_lasr</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, llsshr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[268]"></a>_ll_sshift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)

<P><STRONG><a name="[9e]"></a>strtol</STRONG> (Thumb, 112 bytes, Stack size 32 bytes, strtol.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = strtol &rArr; _strtoul
</UL>
<BR>[Calls]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_strtoul
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_ctype_table
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atoi
</UL>

<P><STRONG><a name="[269]"></a>__I$use$fp</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, iusefp.o(.text), UNUSED)

<P><STRONG><a name="[ac]"></a>_float_round</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, fepilogue.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>

<P><STRONG><a name="[26a]"></a>_float_epilogue</STRONG> (Thumb, 92 bytes, Stack size 4 bytes, fepilogue.o(.text), UNUSED)

<P><STRONG><a name="[a3]"></a>_double_round</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[a2]"></a>_double_epilogue</STRONG> (Thumb, 156 bytes, Stack size 32 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[b0]"></a>_dsqrt</STRONG> (Thumb, 162 bytes, Stack size 32 bytes, dsqrt.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _dsqrt &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
</UL>

<P><STRONG><a name="[b1]"></a>__aeabi_d2ulz</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, dfixul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[98]"></a>__scatterload</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[26b]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[ae]"></a>__rt_ctype_table</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, ctype_o.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strtol
</UL>

<P><STRONG><a name="[af]"></a>_strtoul</STRONG> (Thumb, 158 bytes, Stack size 40 bytes, _strtoul.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _strtoul
</UL>
<BR>[Calls]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_chval
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strtol
</UL>

<P><STRONG><a name="[b3]"></a>_chval</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, _chval.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_strtoul
</UL>

<P><STRONG><a name="[26c]"></a>__decompress</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[26d]"></a>__decompress1</STRONG> (Thumb, 86 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[b4]"></a>arm_bitonic_sort_f32</STRONG> (Thumb, 252 bytes, Stack size 56 bytes, arm_bitonic_sort_f32.o(.text.arm_bitonic_sort_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = arm_bitonic_sort_f32
</UL>
<BR>[Calls]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_sort_f32
</UL>

<P><STRONG><a name="[bb]"></a>arm_bitreversal_32</STRONG> (Thumb, 68 bytes, Stack size 20 bytes, arm_bitreversal2.o(.text.arm_bitreversal_32))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = arm_bitreversal_32
</UL>
<BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cfft_f32
</UL>

<P><STRONG><a name="[b6]"></a>arm_bubble_sort_f32</STRONG> (Thumb, 110 bytes, Stack size 24 bytes, arm_bubble_sort_f32.o(.text.arm_bubble_sort_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = arm_bubble_sort_f32
</UL>
<BR>[Calls]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_sort_f32
</UL>

<P><STRONG><a name="[b7]"></a>arm_cfft_f32</STRONG> (Thumb, 224 bytes, Stack size 24 bytes, arm_cfft_f32.o(.text.arm_cfft_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 384<LI>Call Chain = arm_cfft_f32 &rArr; arm_cfft_radix8by4_f32 &rArr; arm_radix8_butterfly_f32
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_radix8_butterfly_f32
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cfft_radix8by4_f32
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cfft_radix8by2_f32
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_bitreversal_32
</UL>
<BR>[Called By]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_rfft_fast_f32
</UL>

<P><STRONG><a name="[c4]"></a>arm_cfft_init_f32</STRONG> (Thumb, 180 bytes, Stack size 0 bytes, arm_cfft_init_f32.o(.text.arm_cfft_init_f32), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_rfft_64_fast_init_f32
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_rfft_512_fast_init_f32
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_rfft_4096_fast_init_f32
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_rfft_32_fast_init_f32
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_rfft_256_fast_init_f32
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_rfft_2048_fast_init_f32
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_rfft_128_fast_init_f32
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_rfft_1024_fast_init_f32
</UL>

<P><STRONG><a name="[b9]"></a>arm_cfft_radix8by2_f32</STRONG> (Thumb, 408 bytes, Stack size 40 bytes, arm_cfft_f32.o(.text.arm_cfft_radix8by2_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 272<LI>Call Chain = arm_cfft_radix8by2_f32 &rArr; arm_radix8_butterfly_f32
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_radix8_butterfly_f32
</UL>
<BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cfft_f32
</UL>

<P><STRONG><a name="[b8]"></a>arm_cfft_radix8by4_f32</STRONG> (Thumb, 1020 bytes, Stack size 128 bytes, arm_cfft_f32.o(.text.arm_cfft_radix8by4_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 360<LI>Call Chain = arm_cfft_radix8by4_f32 &rArr; arm_radix8_butterfly_f32
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_radix8_butterfly_f32
</UL>
<BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cfft_f32
</UL>

<P><STRONG><a name="[bc]"></a>arm_cmplx_mag_f32</STRONG> (Thumb, 340 bytes, Stack size 40 bytes, arm_cmplx_mag_f32.o(.text.arm_cmplx_mag_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = arm_cmplx_mag_f32 &rArr; __hardfp_sqrtf
</UL>
<BR>[Calls]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrtf
</UL>
<BR>[Called By]<UL><LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;error_process
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_rfft_fast_f32_app
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Fs_Power
</UL>

<P><STRONG><a name="[ea]"></a>arm_copy_f32</STRONG> (Thumb, 70 bytes, Stack size 8 bytes, arm_copy_f32.o(.text.arm_copy_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = arm_copy_f32
</UL>
<BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main_program
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_RunTask
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Sig_QA
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Heart_FreqProcess
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Findpeak
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Breath_FreqProcess
<LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;error_process
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_rfft_fast_f32_app
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PD_process
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Moving_process
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InBed_process
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Heart_quality_process
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Heart_pretreatment
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Heart_peak_Rate_new
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Fs_Power
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Energy_process
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Breath_rate_PSD
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mid_filter
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;freq_modify
</UL>

<P><STRONG><a name="[188]"></a>arm_fill_f32</STRONG> (Thumb, 58 bytes, Stack size 0 bytes, arm_fill_f32.o(.text.arm_fill_f32))
<BR><BR>[Called By]<UL><LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Fs_Power
</UL>

<P><STRONG><a name="[be]"></a>arm_heap_sort_f32</STRONG> (Thumb, 100 bytes, Stack size 24 bytes, arm_heap_sort_f32.o(.text.arm_heap_sort_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = arm_heap_sort_f32 &rArr; arm_heapify
</UL>
<BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_heapify
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_sort_f32
</UL>

<P><STRONG><a name="[c0]"></a>arm_insertion_sort_f32</STRONG> (Thumb, 106 bytes, Stack size 24 bytes, arm_insertion_sort_f32.o(.text.arm_insertion_sort_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = arm_insertion_sort_f32
</UL>
<BR>[Calls]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_sort_f32
</UL>

<P><STRONG><a name="[139]"></a>arm_max_f32</STRONG> (Thumb, 188 bytes, Stack size 20 bytes, arm_max_f32.o(.text.arm_max_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = arm_max_f32
</UL>
<BR>[Called By]<UL><LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Sig_QA
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Heart_FreqProcess
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Findpeak
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Breath_FreqProcess
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PD_process
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InBed_process
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;freq_modify
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Normalization
</UL>

<P><STRONG><a name="[13a]"></a>arm_mean_f32</STRONG> (Thumb, 120 bytes, Stack size 8 bytes, arm_mean_f32.o(.text.arm_mean_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = arm_mean_f32
</UL>
<BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main_program
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Sig_QA
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Breath_FreqProcess
<LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;error_process
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_rfft_fast_f32_app
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PD_process
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InBed_process
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Heart_pretreatment
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Heart_peak_Rate_new
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Fs_Power
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Energy_process
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Breath_rate_PSD
</UL>

<P><STRONG><a name="[1a1]"></a>arm_min_f32</STRONG> (Thumb, 188 bytes, Stack size 20 bytes, arm_min_f32.o(.text.arm_min_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = arm_min_f32
</UL>
<BR>[Called By]<UL><LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Sig_QA
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PD_process
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InBed_process
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Normalization
</UL>

<P><STRONG><a name="[197]"></a>arm_power_f32</STRONG> (Thumb, 128 bytes, Stack size 8 bytes, arm_power_f32.o(.text.arm_power_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = arm_power_f32
</UL>
<BR>[Called By]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Heart_pretreatment
</UL>

<P><STRONG><a name="[c2]"></a>arm_quick_sort_f32</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, arm_quick_sort_f32.o(.text.arm_quick_sort_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = arm_quick_sort_f32 &rArr; arm_quick_sort_core_f32 &rArr;  arm_quick_sort_core_f32 (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_quick_sort_core_f32
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_sort_f32
</UL>

<P><STRONG><a name="[ba]"></a>arm_radix8_butterfly_f32</STRONG> (Thumb, 1458 bytes, Stack size 232 bytes, arm_cfft_radix8_f32.o(.text.arm_radix8_butterfly_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 232<LI>Call Chain = arm_radix8_butterfly_f32
</UL>
<BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cfft_radix8by4_f32
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cfft_radix8by2_f32
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cfft_f32
</UL>

<P><STRONG><a name="[cc]"></a>arm_rfft_fast_f32</STRONG> (Thumb, 70 bytes, Stack size 24 bytes, arm_rfft_fast_f32.o(.text.arm_rfft_fast_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 408<LI>Call Chain = arm_rfft_fast_f32 &rArr; arm_cfft_f32 &rArr; arm_cfft_radix8by4_f32 &rArr; arm_radix8_butterfly_f32
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;stage_rfft_f32
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;merge_rfft_f32
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cfft_f32
</UL>
<BR>[Called By]<UL><LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;error_process
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_rfft_fast_f32_app
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Fs_Power
</UL>

<P><STRONG><a name="[187]"></a>arm_rfft_fast_init_f32</STRONG> (Thumb, 144 bytes, Stack size 0 bytes, arm_rfft_fast_init_f32.o(.text.arm_rfft_fast_init_f32))
<BR><BR>[Called By]<UL><LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;error_process
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_rfft_fast_f32_app
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Fs_Power
</UL>

<P><STRONG><a name="[cf]"></a>arm_selection_sort_f32</STRONG> (Thumb, 134 bytes, Stack size 24 bytes, arm_selection_sort_f32.o(.text.arm_selection_sort_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = arm_selection_sort_f32
</UL>
<BR>[Calls]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_sort_f32
</UL>

<P><STRONG><a name="[d0]"></a>arm_sort_f32</STRONG> (Thumb, 72 bytes, Stack size 8 bytes, arm_sort_f32.o(.text.arm_sort_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = arm_sort_f32 &rArr; arm_bitonic_sort_f32
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_selection_sort_f32
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_quick_sort_f32
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_insertion_sort_f32
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_heap_sort_f32
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_bubble_sort_f32
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_bitonic_sort_f32
</UL>
<BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_RunTask
</UL>

<P><STRONG><a name="[cd]"></a>merge_rfft_f32</STRONG> (Thumb, 180 bytes, Stack size 8 bytes, arm_rfft_fast_f32.o(.text.merge_rfft_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = merge_rfft_f32
</UL>
<BR>[Called By]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_rfft_fast_f32
</UL>

<P><STRONG><a name="[ce]"></a>stage_rfft_f32</STRONG> (Thumb, 180 bytes, Stack size 0 bytes, arm_rfft_fast_f32.o(.text.stage_rfft_f32))
<BR><BR>[Called By]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_rfft_fast_f32
</UL>

<P><STRONG><a name="[d1]"></a>ADC0_Init</STRONG> (Thumb, 154 bytes, Stack size 8 bytes, adc.o(i.ADC0_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = ADC0_Init &rArr; adc_regular_channel_config
</UL>
<BR>[Calls]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_sync_mode_config
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_resolution_config
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_regular_channel_config
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_oversample_mode_enable
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_oversample_mode_config
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_external_trigger_source_config
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_external_trigger_config
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_enable
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_dma_request_after_last_enable
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_dma_mode_enable
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_data_alignment_config
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_clock_config
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_channel_length_config
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_calibration_enable
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[e3]"></a>ADC1_Init</STRONG> (Thumb, 154 bytes, Stack size 8 bytes, adc.o(i.ADC1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = ADC1_Init &rArr; adc_regular_channel_config
</UL>
<BR>[Calls]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_sync_mode_config
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_resolution_config
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_regular_channel_config
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_oversample_mode_enable
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_oversample_mode_config
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_external_trigger_source_config
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_external_trigger_config
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_enable
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_dma_request_after_last_enable
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_dma_mode_enable
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_data_alignment_config
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_clock_config
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_channel_length_config
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_calibration_enable
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[e4]"></a>ADC2_Init</STRONG> (Thumb, 154 bytes, Stack size 8 bytes, adc.o(i.ADC2_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = ADC2_Init &rArr; adc_regular_channel_config
</UL>
<BR>[Calls]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_sync_mode_config
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_resolution_config
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_regular_channel_config
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_oversample_mode_enable
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_oversample_mode_config
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_external_trigger_source_config
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_external_trigger_config
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_enable
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_dma_request_after_last_enable
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_dma_mode_enable
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_data_alignment_config
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_clock_config
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_channel_length_config
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;adc_calibration_enable
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[e5]"></a>ADC_ConvCpltCallback</STRONG> (Thumb, 138 bytes, Stack size 8 bytes, adc.o(i.ADC_ConvCpltCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ADC_ConvCpltCallback &rArr; dma_interrupt_flag_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_interrupt_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Channel2_IRQHandler
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Channel1_IRQHandler
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Channel0_IRQHandler
</UL>

<P><STRONG><a name="[e7]"></a>ADC_ConvHalfCpltCallback</STRONG> (Thumb, 138 bytes, Stack size 8 bytes, adc.o(i.ADC_ConvHalfCpltCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ADC_ConvHalfCpltCallback &rArr; dma_interrupt_flag_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_interrupt_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Channel2_IRQHandler
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Channel1_IRQHandler
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Channel0_IRQHandler
</UL>

<P><STRONG><a name="[e8]"></a>ADC_RunTask</STRONG> (Thumb, 1172 bytes, Stack size 104 bytes, main.o(i.ADC_RunTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 25424<LI>Call Chain = ADC_RunTask &rArr; main_program &rArr; Heart_quality_process &rArr; arm_rfft_fast_f32_app &rArr; arm_rfft_fast_f32 &rArr; arm_cfft_f32 &rArr; arm_cfft_radix8by4_f32 &rArr; arm_radix8_butterfly_f32
</UL>
<BR>[Calls]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_send
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main_program
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_sort_f32
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_copy_f32
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WIFI_Send_AD
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PD_AUTO_Process
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[109]"></a>API_ACK_20CMD</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, api_lan_data_process .o(i.API_ACK_20CMD))
<BR><BR>[Called By]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ExecuteSeverCMD
</UL>

<P><STRONG><a name="[f2]"></a>API_ACK_21CMD</STRONG> (Thumb, 116 bytes, Stack size 32 bytes, api_lan_data_process .o(i.API_ACK_21CMD))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = API_ACK_21CMD &rArr; send &rArr; send_data_processing &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16_USB
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Printf_Hex
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ExecuteSeverCMD
</UL>

<P><STRONG><a name="[f6]"></a>API_ACK_22CMD</STRONG> (Thumb, 212 bytes, Stack size 32 bytes, api_lan_data_process .o(i.API_ACK_22CMD))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = API_ACK_22CMD &rArr; send &rArr; send_data_processing &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16_USB
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_EraseCpuFlash
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Printf_Hex
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ExecuteSeverCMD
</UL>

<P><STRONG><a name="[f8]"></a>API_ACK_23CMD</STRONG> (Thumb, 218 bytes, Stack size 32 bytes, api_lan_data_process .o(i.API_ACK_23CMD))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = API_ACK_23CMD &rArr; send &rArr; send_data_processing &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16_USB
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_WriteCpuFlash
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_CmpCpuFlash
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Printf_Hex
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ExecuteSeverCMD
</UL>

<P><STRONG><a name="[fb]"></a>API_ACK_24CMD</STRONG> (Thumb, 154 bytes, Stack size 32 bytes, api_lan_data_process .o(i.API_ACK_24CMD))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = API_ACK_24CMD &rArr; USER_EEPROM_WriteByte &rArr; EEPROM_SPI_WriteBuffer &rArr; EEPROM_SPI_WritePage &rArr; EEPROM_SPI_WaitStandbyState &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16_USB
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USER_EEPROM_WriteByte
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysReset_Condition
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Printf_Hex
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ExecuteSeverCMD
</UL>

<P><STRONG><a name="[10a]"></a>API_ACK_25CMD</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, api_lan_data_process .o(i.API_ACK_25CMD))
<BR><BR>[Called By]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ExecuteSeverCMD
</UL>

<P><STRONG><a name="[fe]"></a>API_ACK_26CMD</STRONG> (Thumb, 164 bytes, Stack size 32 bytes, api_lan_data_process .o(i.API_ACK_26CMD))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = API_ACK_26CMD &rArr; send &rArr; send_data_processing &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16_USB
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Printf_Hex
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ExecuteSeverCMD
</UL>

<P><STRONG><a name="[10b]"></a>API_ACK_27CMD</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, api_lan_data_process .o(i.API_ACK_27CMD))
<BR><BR>[Called By]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ExecuteSeverCMD
</UL>

<P><STRONG><a name="[ff]"></a>API_ACK_28CMD</STRONG> (Thumb, 132 bytes, Stack size 32 bytes, api_lan_data_process .o(i.API_ACK_28CMD))
<BR><BR>[Stack]<UL><LI>Max Depth = 352<LI>Call Chain = API_ACK_28CMD &rArr; Set_DevID &rArr; USER_EEPROM_WritePage &rArr; EEPROM_WritePage &rArr; USER_EEPROM_WriteByte &rArr; EEPROM_SPI_WriteBuffer &rArr; EEPROM_SPI_WritePage &rArr; EEPROM_SPI_WaitStandbyState &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16_USB
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_DevID
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_DevID
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Printf_Hex
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ExecuteSeverCMD
</UL>

<P><STRONG><a name="[102]"></a>API_ACK_29CMD</STRONG> (Thumb, 182 bytes, Stack size 32 bytes, api_lan_data_process .o(i.API_ACK_29CMD))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = API_ACK_29CMD &rArr; send &rArr; send_data_processing &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16_USB
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Printf_Hex
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ExecuteSeverCMD
</UL>

<P><STRONG><a name="[103]"></a>API_AnalysisIpPortAddr</STRONG> (Thumb, 122 bytes, Stack size 32 bytes, api_lan_data_process .o(i.API_AnalysisIpPortAddr))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = API_AnalysisIpPortAddr &rArr; atoi &rArr; strtol &rArr; _strtoul
</UL>
<BR>[Calls]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strchr
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atoi
</UL>
<BR>[Called By]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CDC_Command
</UL>

<P><STRONG><a name="[105]"></a>API_Chose_TS5A3359_GAIN</STRONG> (Thumb, 98 bytes, Stack size 8 bytes, gpio.o(i.API_Chose_TS5A3359_GAIN))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = API_Chose_TS5A3359_GAIN
</UL>
<BR>[Calls]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_RunTask
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>

<P><STRONG><a name="[106]"></a>API_Erase_Flash</STRONG> (Thumb, 32 bytes, Stack size 24 bytes, api_lan_data_process .o(i.API_Erase_Flash))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = API_Erase_Flash &rArr; EEPROM_SPI_WriteBuffer &rArr; EEPROM_SPI_WritePage &rArr; EEPROM_SPI_WaitStandbyState &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WriteBuffer
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CDC_Command
</UL>

<P><STRONG><a name="[108]"></a>API_ExecuteSeverCMD</STRONG> (Thumb, 260 bytes, Stack size 8 bytes, api_lan_data_process .o(i.API_ExecuteSeverCMD))
<BR><BR>[Stack]<UL><LI>Max Depth = 360<LI>Call Chain = API_ExecuteSeverCMD &rArr; API_ACK_28CMD &rArr; Set_DevID &rArr; USER_EEPROM_WritePage &rArr; EEPROM_WritePage &rArr; USER_EEPROM_WriteByte &rArr; EEPROM_SPI_WriteBuffer &rArr; EEPROM_SPI_WritePage &rArr; EEPROM_SPI_WaitStandbyState &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Reset
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_SeverInfo
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_29CMD
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_28CMD
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_27CMD
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_26CMD
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_25CMD
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_24CMD
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_23CMD
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_22CMD
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_21CMD
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_20CMD
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[10e]"></a>API_Init_LAN</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, api_w5500.o(i.API_Init_LAN))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = API_Init_LAN &rArr; API_Init_Net_Parameters &rArr; EEPROM_SPI_ReadBuffer &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_SPI0_Init
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_GPIO_Init
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Init_Net_Parameters
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[111]"></a>API_Init_Net_Parameters</STRONG> (Thumb, 506 bytes, Stack size 40 bytes, api_w5500.o(i.API_Init_Net_Parameters))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = API_Init_Net_Parameters &rArr; EEPROM_SPI_ReadBuffer &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_ReadBuffer
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Init_LAN
</UL>

<P><STRONG><a name="[115]"></a>API_Itoi</STRONG> (Thumb, 94 bytes, Stack size 32 bytes, api_lan_data_process .o(i.API_Itoi))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = API_Itoi
</UL>
<BR>[Called By]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_LAN_Info_To_String
</UL>

<P><STRONG><a name="[113]"></a>API_LAN_Info_To_String</STRONG> (Thumb, 496 bytes, Stack size 120 bytes, api_lan_data_process .o(i.API_LAN_Info_To_String))
<BR><BR>[Stack]<UL><LI>Max Depth = 424<LI>Call Chain = API_LAN_Info_To_String &rArr; HEXArrayToStringArray &rArr; __2sprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HEXArrayToStringArray
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Itoi
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CDC_Command
</UL>

<P><STRONG><a name="[117]"></a>API_LED_GPIO</STRONG> (Thumb, 72 bytes, Stack size 8 bytes, gpio.o(i.API_LED_GPIO))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = API_LED_GPIO &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>

<P><STRONG><a name="[119]"></a>API_LanSock1Send_CRC</STRONG> (Thumb, 90 bytes, Stack size 16 bytes, api_lan_data_process .o(i.API_LanSock1Send_CRC))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = API_LanSock1Send_CRC &rArr; send &rArr; send_data_processing &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16_USB
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Printf_Hex
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send
</UL>
<BR>[Called By]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WIFI_Send_AD
</UL>

<P><STRONG><a name="[f5]"></a>API_Printf_Hex</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, usart.o(i.API_Printf_Hex))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = API_Printf_Hex &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_do_tcp_client
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_RNG_Init
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_LanSock1Send_CRC
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_29CMD
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_28CMD
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_26CMD
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_24CMD
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_23CMD
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_22CMD
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_21CMD
</UL>

<P><STRONG><a name="[133]"></a>API_Process_Lan_Data</STRONG> (Thumb, 150 bytes, Stack size 0 bytes, api_lan_data_process .o(i.API_Process_Lan_Data))
<BR><BR>[Called By]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_do_tcp_client
</UL>

<P><STRONG><a name="[11a]"></a>API_RNG_Init</STRONG> (Thumb, 266 bytes, Stack size 32 bytes, api_tnrg.o(i.API_RNG_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = API_RNG_Init &rArr; EEPROM_SPI_WriteBuffer &rArr; EEPROM_SPI_WritePage &rArr; EEPROM_SPI_WaitStandbyState &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WriteBuffer
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_ReadBuffer
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_flag_get
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_enable
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_deinit
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Printf_Hex
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_hard_rand_data
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[11f]"></a>API_SPI0_Send_Read_Byte</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, api_w5500.o(i.API_SPI0_Send_Read_Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = API_SPI0_Send_Read_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_flag_get
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_transmit
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_receive
</UL>
<BR>[Called By]<UL><LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_SpiSendData
</UL>

<P><STRONG><a name="[110]"></a>API_W5500_GPIO_Init</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, api_w5500.o(i.API_W5500_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = API_W5500_GPIO_Init &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Init_LAN
</UL>

<P><STRONG><a name="[10c]"></a>API_W5500_Reset</STRONG> (Thumb, 58 bytes, Stack size 8 bytes, api_w5500.o(i.API_W5500_Reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = API_W5500_Reset &rArr; DHCP_run &rArr; check_DHCP_timeout &rArr; send_DHCP_REQUEST &rArr; sendto &rArr; send_data_processing &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;socket_buf_init
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_w5500_mac
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reset_w5500
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PHY_check
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHCP_run
</UL>
<BR>[Called By]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CDC_Command
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ExecuteSeverCMD
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_SeverInfo
</UL>

<P><STRONG><a name="[10f]"></a>API_W5500_SPI0_Init</STRONG> (Thumb, 156 bytes, Stack size 32 bytes, api_w5500.o(i.API_W5500_SPI0_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = API_W5500_SPI0_Init &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_init
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Init_LAN
</UL>

<P><STRONG><a name="[10d]"></a>API_Write_SeverInfo</STRONG> (Thumb, 244 bytes, Stack size 24 bytes, api_lan_data_process .o(i.API_Write_SeverInfo))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = API_Write_SeverInfo &rArr; API_W5500_Reset &rArr; DHCP_run &rArr; check_DHCP_timeout &rArr; send_DHCP_REQUEST &rArr; sendto &rArr; send_data_processing &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WriteBuffer
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_ReadBuffer
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Reset
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ExecuteSeverCMD
</UL>

<P><STRONG><a name="[12b]"></a>API_do_tcp_client</STRONG> (Thumb, 178 bytes, Stack size 8 bytes, usart.o(i.API_do_tcp_client))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = API_do_tcp_client &rArr; recv &rArr; recv_data_processing &rArr; wiz_read_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;socket
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setSn_IR
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recv
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;getSn_SR
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;getSn_RX_RSR
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;getSn_IR
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disconnect
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;connect
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;close
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Process_Lan_Data
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Printf_Hex
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[21a]"></a>Boot_process</STRONG> (Thumb, 334 bytes, Stack size 0 bytes, mymath.o(i.Boot_process))
<BR><BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main_program
</UL>

<P><STRONG><a name="[136]"></a>Breath_FreqProcess</STRONG> (Thumb, 770 bytes, Stack size 5792 bytes, whut_math.o(i.Breath_FreqProcess))
<BR><BR>[Stack]<UL><LI>Max Depth = 7464<LI>Call Chain = Breath_FreqProcess &rArr; freq_modify &rArr; peak_diff_process
</UL>
<BR>[Calls]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_copy_f32
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_mean_f32
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_max_f32
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;freq_modify
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Psd_base
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Breath_rate_PSD
</UL>

<P><STRONG><a name="[13b]"></a>Breath_rate_PSD</STRONG> (Thumb, 440 bytes, Stack size 4224 bytes, mymath.o(i.Breath_rate_PSD))
<BR><BR>[Stack]<UL><LI>Max Depth = 21304<LI>Call Chain = Breath_rate_PSD &rArr; arm_rfft_fast_f32_app &rArr; arm_rfft_fast_f32 &rArr; arm_cfft_f32 &rArr; arm_cfft_radix8by4_f32 &rArr; arm_radix8_butterfly_f32
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_copy_f32
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_mean_f32
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Breath_FreqProcess
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_rfft_fast_f32_app
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main_program
</UL>

<P><STRONG><a name="[30]"></a>BusFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.BusFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[13f]"></a>CDC_Command</STRONG> (Thumb, 962 bytes, Stack size 648 bytes, usb.o(i.CDC_Command))
<BR><BR>[Stack]<UL><LI>Max Depth = 1072<LI>Call Chain = CDC_Command &rArr; API_LAN_Info_To_String &rArr; HEXArrayToStringArray &rArr; __2sprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_send
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_BEEP_on
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_BEEP_off
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_HardVer
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_DevID
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_HardVer
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_DevID
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Reset
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_LAN_Info_To_String
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Erase_Flash
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_AnalysisIpPortAddr
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[f3]"></a>CRC16_USB</STRONG> (Thumb, 126 bytes, Stack size 24 bytes, my_crc.o(i.CRC16_USB))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = CRC16_USB &rArr; InvertUint16
</UL>
<BR>[Calls]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InvertUint8
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InvertUint16
</UL>
<BR>[Called By]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USER_EEPROM_WriteByte
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USER_EEPROM_RedePage
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_WritePage
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IPD_Command
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_LanSock1Send_CRC
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC_WIFISEND
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_29CMD
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_28CMD
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_26CMD
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_24CMD
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_23CMD
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_22CMD
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_21CMD
</UL>

<P><STRONG><a name="[147]"></a>CRC_WIFISEND</STRONG> (Thumb, 94 bytes, Stack size 24 bytes, user_step.o(i.CRC_WIFISEND))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = CRC_WIFISEND &rArr; CRC16_USB &rArr; InvertUint16
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16_USB
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP32_SendData_Add_Plus
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP32_SendData_Add
</UL>
<BR>[Called By]<UL><LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IPD_Command
</UL>

<P><STRONG><a name="[14a]"></a>DAC1_Set_Vol</STRONG> (Thumb, 118 bytes, Stack size 32 bytes, dac.o(i.DAC1_Set_Vol))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = DAC1_Set_Vol &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dac_data_set
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2uiz
</UL>
<BR>[Called By]<UL><LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_Init
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PD_AUTO_Process
</UL>

<P><STRONG><a name="[14c]"></a>DAC2_Set_Vol</STRONG> (Thumb, 120 bytes, Stack size 32 bytes, dac.o(i.DAC2_Set_Vol))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = DAC2_Set_Vol &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dac_data_set
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2uiz
</UL>
<BR>[Called By]<UL><LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_Init
</UL>

<P><STRONG><a name="[14d]"></a>DAC_Init</STRONG> (Thumb, 90 bytes, Stack size 8 bytes, dac.o(i.DAC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = DAC_Init &rArr; gpio_mode_set
</UL>
<BR>[Calls]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dac_wave_mode_config
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dac_trigger_disable
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dac_output_buffer_disable
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dac_deinit
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dac_concurrent_enable
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dac_concurrent_data_set
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[127]"></a>DHCP_run</STRONG> (Thumb, 532 bytes, Stack size 16 bytes, dhcp.o(i.DHCP_run))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = DHCP_run &rArr; check_DHCP_timeout &rArr; send_DHCP_REQUEST &rArr; sendto &rArr; send_data_processing &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;socket
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;getSn_SR
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_DHCP_REQUEST
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_DHCP_DISCOVER
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reset_DHCP_timeout
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parseDHCPMSG
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_dhcp_client
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_DHCP_timeout
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_DHCP_leasedIP
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Reset
</UL>

<P><STRONG><a name="[15b]"></a>DMA1_CH0_Init</STRONG> (Thumb, 128 bytes, Stack size 40 bytes, dma.o(i.DMA1_CH0_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = DMA1_CH0_Init &rArr; nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_single_data_mode_init
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_interrupt_enable
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_deinit
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_circulation_enable
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_subperipheral_select
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_enable
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[163]"></a>DMA1_CH1_Init</STRONG> (Thumb, 128 bytes, Stack size 40 bytes, dma.o(i.DMA1_CH1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = DMA1_CH1_Init &rArr; nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_single_data_mode_init
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_interrupt_enable
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_deinit
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_circulation_enable
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_subperipheral_select
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_enable
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[164]"></a>DMA1_CH2_Init</STRONG> (Thumb, 128 bytes, Stack size 40 bytes, dma.o(i.DMA1_CH2_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = DMA1_CH2_Init &rArr; nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_single_data_mode_init
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_interrupt_enable
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_deinit
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_circulation_enable
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_subperipheral_select
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_enable
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[6e]"></a>DMA1_Channel0_IRQHandler</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.DMA1_Channel0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = DMA1_Channel0_IRQHandler &rArr; dma_interrupt_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_interrupt_flag_get
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_ConvHalfCpltCallback
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_ConvCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[6f]"></a>DMA1_Channel1_IRQHandler</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.DMA1_Channel1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = DMA1_Channel1_IRQHandler &rArr; dma_interrupt_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_interrupt_flag_get
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_ConvHalfCpltCallback
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_ConvCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[70]"></a>DMA1_Channel2_IRQHandler</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.DMA1_Channel2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = DMA1_Channel2_IRQHandler &rArr; dma_interrupt_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_interrupt_flag_get
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_ConvHalfCpltCallback
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_ConvCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[166]"></a>DRV_SPI_SwapByte</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, spi.o(i.DRV_SPI_SwapByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_flag_get
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_transmit
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_receive
</UL>
<BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_ReadBuffer
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WritePage
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WaitStandbyState
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_SendInstruction
</UL>

<P><STRONG><a name="[33]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[112]"></a>EEPROM_SPI_ReadBuffer</STRONG> (Thumb, 98 bytes, Stack size 24 bytes, eeprom_spi.o(i.EEPROM_SPI_ReadBuffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = EEPROM_SPI_ReadBuffer &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_flag_get
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DRV_SPI_SwapByte
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_SendInstruction
</UL>
<BR>[Called By]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USER_EEPROM_WriteByte
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USER_EEPROM_RedePage
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USER_EEPROM_RedeByte
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_WritePage
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_RNG_Init
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Init_Net_Parameters
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_SeverInfo
</UL>

<P><STRONG><a name="[167]"></a>EEPROM_SPI_SendInstruction</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, eeprom_spi.o(i.EEPROM_SPI_SendInstruction))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_flag_get
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DRV_SPI_SwapByte
</UL>
<BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_ReadBuffer
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_WriteEnable
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_WriteDisable
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WritePage
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WaitStandbyState
</UL>

<P><STRONG><a name="[168]"></a>EEPROM_SPI_WaitStandbyState</STRONG> (Thumb, 66 bytes, Stack size 16 bytes, eeprom_spi.o(i.EEPROM_SPI_WaitStandbyState))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = EEPROM_SPI_WaitStandbyState &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DRV_SPI_SwapByte
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_SendInstruction
</UL>
<BR>[Called By]<UL><LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WritePage
</UL>

<P><STRONG><a name="[107]"></a>EEPROM_SPI_WriteBuffer</STRONG> (Thumb, 394 bytes, Stack size 40 bytes, eeprom_spi.o(i.EEPROM_SPI_WriteBuffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = EEPROM_SPI_WriteBuffer &rArr; EEPROM_SPI_WritePage &rArr; EEPROM_SPI_WaitStandbyState &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WritePage
</UL>
<BR>[Called By]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USER_EEPROM_WriteByte
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_WritePage
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_RNG_Init
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Erase_Flash
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_SeverInfo
</UL>

<P><STRONG><a name="[169]"></a>EEPROM_SPI_WritePage</STRONG> (Thumb, 170 bytes, Stack size 32 bytes, eeprom_spi.o(i.EEPROM_SPI_WritePage))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = EEPROM_SPI_WritePage &rArr; EEPROM_SPI_WaitStandbyState &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_flag_get
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DRV_SPI_SwapByte
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_WriteEnable
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_WriteDisable
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WaitStandbyState
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_SendInstruction
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WriteBuffer
</UL>

<P><STRONG><a name="[16b]"></a>EEPROM_WriteDisable</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, eeprom_spi.o(i.EEPROM_WriteDisable))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = EEPROM_WriteDisable &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_SendInstruction
</UL>
<BR>[Called By]<UL><LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WritePage
</UL>

<P><STRONG><a name="[16a]"></a>EEPROM_WriteEnable</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, eeprom_spi.o(i.EEPROM_WriteEnable))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = EEPROM_WriteEnable &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_SendInstruction
</UL>
<BR>[Called By]<UL><LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WritePage
</UL>

<P><STRONG><a name="[170]"></a>ESP32BLE_Command_Read</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, user_step.o(i.ESP32BLE_Command_Read))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ESP32BLE_Command_Read
</UL>
<BR>[Calls]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IPD_Command
</UL>

<P><STRONG><a name="[171]"></a>ESP32BLE_SendData_Add</STRONG> (Thumb, 142 bytes, Stack size 16 bytes, esp32_wifi.o(i.ESP32BLE_SendData_Add))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ESP32BLE_SendData_Add
</UL>
<BR>[Calls]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IPD_Command
</UL>

<P><STRONG><a name="[172]"></a>ESP32_Command_Read</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, user_step.o(i.ESP32_Command_Read))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ESP32_Command_Read
</UL>
<BR>[Calls]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IPD_Command
</UL>

<P><STRONG><a name="[173]"></a>ESP32_Command_Read_Plus</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, user_step.o(i.ESP32_Command_Read_Plus))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ESP32_Command_Read_Plus
</UL>
<BR>[Calls]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IPD_Command
</UL>

<P><STRONG><a name="[149]"></a>ESP32_SendData_Add</STRONG> (Thumb, 156 bytes, Stack size 24 bytes, esp32_wifi.o(i.ESP32_SendData_Add))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = ESP32_SendData_Add
</UL>
<BR>[Calls]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC_WIFISEND
</UL>

<P><STRONG><a name="[148]"></a>ESP32_SendData_Add_Plus</STRONG> (Thumb, 166 bytes, Stack size 24 bytes, esp32_wifi.o(i.ESP32_SendData_Add_Plus))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = ESP32_SendData_Add_Plus
</UL>
<BR>[Calls]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC_WIFISEND
</UL>

<P><STRONG><a name="[174]"></a>ESP_BLEADVSTOP</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, esp32_wifi.o(i.ESP_BLEADVSTOP))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = ESP_BLEADVSTOP &rArr; CheckCmdRepeatInList
</UL>
<BR>[Calls]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CheckCmdRepeatInList
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AddCmdTaskToList
</UL>
<BR>[Called By]<UL><LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IPD_Command
</UL>

<P><STRONG><a name="[177]"></a>ESP_CloseNetConnect</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, esp32_wifi.o(i.ESP_CloseNetConnect))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = ESP_CloseNetConnect &rArr; CheckCmdRepeatInList
</UL>
<BR>[Calls]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CheckCmdRepeatInList
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AddCmdTaskToList
</UL>
<BR>[Called By]<UL><LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IPD_Command
</UL>

<P><STRONG><a name="[178]"></a>ESP_CloseWifiConnect</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, esp32_wifi.o(i.ESP_CloseWifiConnect))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = ESP_CloseWifiConnect &rArr; CheckCmdRepeatInList
</UL>
<BR>[Calls]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CheckCmdRepeatInList
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AddCmdTaskToList
</UL>
<BR>[Called By]<UL><LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IPD_Command
</UL>

<P><STRONG><a name="[5e]"></a>EXTI10_15_IRQHandler</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.EXTI10_15_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EXTI10_15_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exti_interrupt_flag_get
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exti_interrupt_flag_clear
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[181]"></a>Energy_process</STRONG> (Thumb, 636 bytes, Stack size 3280 bytes, mymath.o(i.Energy_process))
<BR><BR>[Stack]<UL><LI>Max Depth = 3368<LI>Call Chain = Energy_process &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_copy_f32
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_mean_f32
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main_program
</UL>

<P><STRONG><a name="[1f3]"></a>FLASH_If_Init</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, flash.o(i.FLASH_If_Init))
<BR><BR>[Called By]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_EraseCpuFlash
</UL>

<P><STRONG><a name="[183]"></a>FML_USART_RecvTask</STRONG> (Thumb, 174 bytes, Stack size 2072 bytes, usart.o(i.FML_USART_RecvTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 2096<LI>Call Chain = FML_USART_RecvTask &rArr; ReadBytesToBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadBytesToBuffer
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_IDLECallBack
</UL>

<P><STRONG><a name="[185]"></a>Findpeak</STRONG> (Thumb, 306 bytes, Stack size 232 bytes, whut_math.o(i.Findpeak))
<BR><BR>[Stack]<UL><LI>Max Depth = 252<LI>Call Chain = Findpeak &rArr; arm_max_f32
</UL>
<BR>[Calls]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_copy_f32
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_max_f32
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main_program
</UL>

<P><STRONG><a name="[1e5]"></a>Float2char</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, user_step.o(i.Float2char))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Float2char
</UL>
<BR>[Called By]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WIFI_Send_AD
</UL>

<P><STRONG><a name="[186]"></a>Fs_Power</STRONG> (Thumb, 620 bytes, Stack size 12400 bytes, mymath.o(i.Fs_Power))
<BR><BR>[Stack]<UL><LI>Max Depth = 12808<LI>Call Chain = Fs_Power &rArr; arm_rfft_fast_f32 &rArr; arm_cfft_f32 &rArr; arm_cfft_radix8by4_f32 &rArr; arm_radix8_butterfly_f32
</UL>
<BR>[Calls]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_copy_f32
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_rfft_fast_init_f32
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_rfft_fast_f32
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_mean_f32
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_fill_f32
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cmplx_mag_f32
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InBed_process
</UL>

<P><STRONG><a name="[189]"></a>GPIO_Init</STRONG> (Thumb, 90 bytes, Stack size 8 bytes, gpio.o(i.GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = GPIO_Init &rArr; Init_GPIO_TS5A339 &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_GPIO_TS5A339
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_LED_GPIO
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Chose_TS5A3359_GAIN
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[18b]"></a>GetWifiInFo</STRONG> (Thumb, 130 bytes, Stack size 56 bytes, user_step.o(i.GetWifiInFo))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = GetWifiInFo &rArr; USER_EEPROM_RedePage &rArr; EEPROM_SPI_ReadBuffer &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USER_EEPROM_RedePage
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncpy
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_zhcdInFo
</UL>

<P><STRONG><a name="[18e]"></a>Get_AlgorithmInFo</STRONG> (Thumb, 106 bytes, Stack size 24 bytes, user_step.o(i.Get_AlgorithmInFo))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = Get_AlgorithmInFo &rArr; USER_EEPROM_RedePage &rArr; EEPROM_SPI_ReadBuffer &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USER_EEPROM_RedePage
</UL>
<BR>[Called By]<UL><LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_Init
</UL>

<P><STRONG><a name="[101]"></a>Get_DevID</STRONG> (Thumb, 82 bytes, Stack size 24 bytes, user_step.o(i.Get_DevID))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = Get_DevID &rArr; USER_EEPROM_RedePage &rArr; EEPROM_SPI_ReadBuffer &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USER_EEPROM_RedePage
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IPD_Command
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CDC_Command
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_zhcdInFo
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_28CMD
</UL>

<P><STRONG><a name="[143]"></a>Get_HardVer</STRONG> (Thumb, 86 bytes, Stack size 24 bytes, user_step.o(i.Get_HardVer))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = Get_HardVer &rArr; USER_EEPROM_RedePage &rArr; EEPROM_SPI_ReadBuffer &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USER_EEPROM_RedePage
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncpy
</UL>
<BR>[Called By]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CDC_Command
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_zhcdInFo
</UL>

<P><STRONG><a name="[18f]"></a>Get_SeverInfo</STRONG> (Thumb, 104 bytes, Stack size 40 bytes, user_step.o(i.Get_SeverInfo))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = Get_SeverInfo &rArr; USER_EEPROM_RedePage &rArr; EEPROM_SPI_ReadBuffer &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USER_EEPROM_RedePage
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Sever_Buf2Info
</UL>
<BR>[Called By]<UL><LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_Init
</UL>

<P><STRONG><a name="[191]"></a>Get_zhcdInFo</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, user_step.o(i.Get_zhcdInFo))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = Get_zhcdInFo &rArr; GetWifiInFo &rArr; USER_EEPROM_RedePage &rArr; EEPROM_SPI_ReadBuffer &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetWifiInFo
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_HardVer
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_DevID
</UL>
<BR>[Called By]<UL><LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_Init
</UL>

<P><STRONG><a name="[114]"></a>HEXArrayToStringArray</STRONG> (Thumb, 76 bytes, Stack size 280 bytes, api_lan_data_process .o(i.HEXArrayToStringArray))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = HEXArrayToStringArray &rArr; __2sprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_LAN_Info_To_String
</UL>

<P><STRONG><a name="[2e]"></a>HardFault_Handler</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.HardFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysReset_Condition
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[193]"></a>Heart_FreqProcess</STRONG> (Thumb, 876 bytes, Stack size 1152 bytes, whut_math.o(i.Heart_FreqProcess))
<BR><BR>[Stack]<UL><LI>Max Depth = 1172<LI>Call Chain = Heart_FreqProcess &rArr; arm_max_f32
</UL>
<BR>[Calls]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_copy_f32
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_max_f32
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Psd_base
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Heart_quality_process
</UL>

<P><STRONG><a name="[194]"></a>Heart_peak_Rate_new</STRONG> (Thumb, 414 bytes, Stack size 56 bytes, mymath.o(i.Heart_peak_Rate_new))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = Heart_peak_Rate_new &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_copy_f32
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_mean_f32
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main_program
</UL>

<P><STRONG><a name="[195]"></a>Heart_pretreatment</STRONG> (Thumb, 398 bytes, Stack size 3224 bytes, mymath.o(i.Heart_pretreatment))
<BR><BR>[Stack]<UL><LI>Max Depth = 3296<LI>Call Chain = Heart_pretreatment &rArr; __hardfp_sqrt &rArr; _dsqrt &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_copy_f32
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_fir_function
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_power_f32
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_mean_f32
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
</UL>
<BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main_program
</UL>

<P><STRONG><a name="[199]"></a>Heart_quality_process</STRONG> (Thumb, 78 bytes, Stack size 8208 bytes, mymath.o(i.Heart_quality_process))
<BR><BR>[Stack]<UL><LI>Max Depth = 25288<LI>Call Chain = Heart_quality_process &rArr; arm_rfft_fast_f32_app &rArr; arm_rfft_fast_f32 &rArr; arm_cfft_f32 &rArr; arm_cfft_radix8by4_f32 &rArr; arm_radix8_butterfly_f32
</UL>
<BR>[Calls]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_copy_f32
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Heart_FreqProcess
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_rfft_fast_f32_app
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main_program
</UL>

<P><STRONG><a name="[19a]"></a>IINCHIP_READ</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, w5500_conf.o(i.IINCHIP_READ))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = IINCHIP_READ &rArr; IINCHIP_SpiSendData &rArr; API_SPI0_Send_Read_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;iinchip_cson
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;iinchip_csoff
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_SpiSendData
</UL>
<BR>[Called By]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;socket
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recv
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;getSn_SR
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;getSn_RX_RSR
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;getSn_IR
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disconnect
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;connect
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;close
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;getPHYStatus
<LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_data_processing
<LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recv_data_processing
<LI><a href="#[20c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;getSn_TX_FSR
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sendto
<LI><a href="#[21f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recvfrom
</UL>

<P><STRONG><a name="[19c]"></a>IINCHIP_SpiSendData</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, w5500_conf.o(i.IINCHIP_SpiSendData))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = IINCHIP_SpiSendData &rArr; API_SPI0_Send_Read_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Read_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wiz_write_buf
<LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wiz_read_buf
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_WRITE
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_READ
</UL>

<P><STRONG><a name="[19e]"></a>IINCHIP_WRITE</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, w5500_conf.o(i.IINCHIP_WRITE))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = IINCHIP_WRITE &rArr; IINCHIP_SpiSendData &rArr; API_SPI0_Send_Read_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;iinchip_cson
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;iinchip_csoff
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_SpiSendData
</UL>
<BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;socket_buf_init
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;socket
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setSn_IR
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recv
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;disconnect
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;connect
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;close
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send
<LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setMR
<LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_data_processing
<LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recv_data_processing
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sendto
<LI><a href="#[21f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recvfrom
</UL>

<P><STRONG><a name="[19f]"></a>IPD_Command</STRONG> (Thumb, 1332 bytes, Stack size 2104 bytes, user_step.o(i.IPD_Command))
<BR><BR>[Stack]<UL><LI>Max Depth = 2424<LI>Call Chain = IPD_Command &rArr; Set_DevID &rArr; USER_EEPROM_WritePage &rArr; EEPROM_WritePage &rArr; USER_EEPROM_WriteByte &rArr; EEPROM_SPI_WriteBuffer &rArr; EEPROM_SPI_WritePage &rArr; EEPROM_SPI_WaitStandbyState &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16_USB
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USER_EEPROM_WriteByte
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_BEEP_on
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_BEEP_off
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_WriteCpuFlash
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_EraseCpuFlash
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_CmpCpuFlash
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP_CloseWifiConnect
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP_CloseNetConnect
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP_BLEADVSTOP
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP32BLE_SendData_Add
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Sever_Buf2Info
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP32_Command_Read_Plus
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP32_Command_Read
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP32BLE_Command_Read
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC_WIFISEND
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_DevID
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_DevID
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1a0]"></a>InBed_process</STRONG> (Thumb, 1564 bytes, Stack size 112 bytes, mymath.o(i.InBed_process))
<BR><BR>[Stack]<UL><LI>Max Depth = 12920<LI>Call Chain = InBed_process &rArr; Fs_Power &rArr; arm_rfft_fast_f32 &rArr; arm_cfft_f32 &rArr; arm_cfft_radix8by4_f32 &rArr; arm_radix8_butterfly_f32
</UL>
<BR>[Calls]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_copy_f32
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_min_f32
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_mean_f32
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_max_f32
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Fs_Power
</UL>
<BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main_program
</UL>

<P><STRONG><a name="[18a]"></a>Init_GPIO_TS5A339</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, gpio.o(i.Init_GPIO_TS5A339))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = Init_GPIO_TS5A339 &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>

<P><STRONG><a name="[2f]"></a>MemManage_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.MemManage_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1a2]"></a>Moving_process</STRONG> (Thumb, 326 bytes, Stack size 80 bytes, mymath.o(i.Moving_process))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = Moving_process &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_copy_f32
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main_program
</UL>

<P><STRONG><a name="[1a3]"></a>Mymath_User_Init</STRONG> (Thumb, 106 bytes, Stack size 8 bytes, mymath.o(i.Mymath_User_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Mymath_User_Init &rArr; my_fir_struct_init
</UL>
<BR>[Calls]<UL><LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_fir_struct_init
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[2d]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.NMI_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1a5]"></a>Normalization</STRONG> (Thumb, 158 bytes, Stack size 40 bytes, whut_math.o(i.Normalization))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = Normalization &rArr; arm_min_f32
</UL>
<BR>[Calls]<UL><LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_min_f32
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_max_f32
</UL>
<BR>[Called By]<UL><LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Sig_QA
</UL>

<P><STRONG><a name="[ed]"></a>PD_AUTO_Process</STRONG> (Thumb, 684 bytes, Stack size 32 bytes, user_step.o(i.PD_AUTO_Process))
<BR><BR>[Stack]<UL><LI>Max Depth = 344<LI>Call Chain = PD_AUTO_Process &rArr; Set_AlgorithmInFo &rArr; USER_EEPROM_WritePage &rArr; EEPROM_WritePage &rArr; USER_EEPROM_WriteByte &rArr; EEPROM_SPI_WriteBuffer &rArr; EEPROM_SPI_WritePage &rArr; EEPROM_SPI_WaitStandbyState &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC1_Set_Vol
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_AlgorithmInFo
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_RunTask
</UL>

<P><STRONG><a name="[1ad]"></a>PD_process</STRONG> (Thumb, 442 bytes, Stack size 48 bytes, mymath.o(i.PD_process))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = PD_process &rArr; arm_min_f32
</UL>
<BR>[Calls]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_copy_f32
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_min_f32
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_mean_f32
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_max_f32
</UL>
<BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main_program
</UL>

<P><STRONG><a name="[124]"></a>PHY_check</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, w5500_conf.o(i.PHY_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = PHY_check &rArr; close &rArr; IINCHIP_WRITE &rArr; IINCHIP_SpiSendData &rArr; API_SPI0_Send_Read_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;close
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;getPHYStatus
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Reset
</UL>

<P><STRONG><a name="[1af]"></a>Pearson_corr</STRONG> (Thumb, 342 bytes, Stack size 72 bytes, whut_math.o(i.Pearson_corr))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = Pearson_corr &rArr; __hardfp_sqrt &rArr; _dsqrt &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
</UL>
<BR>[Called By]<UL><LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Sig_QA
</UL>

<P><STRONG><a name="[34]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.PendSV_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[137]"></a>Psd_base</STRONG> (Thumb, 58 bytes, Stack size 0 bytes, whut_math.o(i.Psd_base))
<BR><BR>[Called By]<UL><LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Heart_FreqProcess
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Breath_FreqProcess
</UL>

<P><STRONG><a name="[1b0]"></a>RTC_Init</STRONG> (Thumb, 96 bytes, Stack size 8 bytes, rtc.o(i.RTC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = RTC_Init &rArr; rtc_init &rArr; rtc_register_sync_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_register_sync_wait
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_rtc_clock_config
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_osci_stab_wait
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_osci_on
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pmu_backup_write_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1b9]"></a>SPI1_Init</STRONG> (Thumb, 146 bytes, Stack size 32 bytes, spi.o(i.SPI1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = SPI1_Init &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_init
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[32]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1ac]"></a>Set_AlgorithmInFo</STRONG> (Thumb, 138 bytes, Stack size 24 bytes, user_step.o(i.Set_AlgorithmInFo))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = Set_AlgorithmInFo &rArr; USER_EEPROM_WritePage &rArr; EEPROM_WritePage &rArr; USER_EEPROM_WriteByte &rArr; EEPROM_SPI_WriteBuffer &rArr; EEPROM_SPI_WritePage &rArr; EEPROM_SPI_WaitStandbyState &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USER_EEPROM_WritePage
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USER_EEPROM_RedePage
</UL>
<BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PD_AUTO_Process
</UL>

<P><STRONG><a name="[100]"></a>Set_DevID</STRONG> (Thumb, 118 bytes, Stack size 32 bytes, user_step.o(i.Set_DevID))
<BR><BR>[Stack]<UL><LI>Max Depth = 320<LI>Call Chain = Set_DevID &rArr; USER_EEPROM_WritePage &rArr; EEPROM_WritePage &rArr; USER_EEPROM_WriteByte &rArr; EEPROM_SPI_WriteBuffer &rArr; EEPROM_SPI_WritePage &rArr; EEPROM_SPI_WaitStandbyState &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USER_EEPROM_WritePage
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USER_EEPROM_RedePage
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IPD_Command
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CDC_Command
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_28CMD
</UL>

<P><STRONG><a name="[142]"></a>Set_HardVer</STRONG> (Thumb, 122 bytes, Stack size 32 bytes, user_step.o(i.Set_HardVer))
<BR><BR>[Stack]<UL><LI>Max Depth = 320<LI>Call Chain = Set_HardVer &rArr; USER_EEPROM_WritePage &rArr; EEPROM_WritePage &rArr; USER_EEPROM_WriteByte &rArr; EEPROM_SPI_WriteBuffer &rArr; EEPROM_SPI_WritePage &rArr; EEPROM_SPI_WaitStandbyState &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USER_EEPROM_WritePage
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USER_EEPROM_RedePage
</UL>
<BR>[Called By]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CDC_Command
</UL>

<P><STRONG><a name="[190]"></a>Sever_Buf2Info</STRONG> (Thumb, 116 bytes, Stack size 32 bytes, user_step.o(i.Sever_Buf2Info))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = Sever_Buf2Info
</UL>
<BR>[Calls]<UL><LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;figure
</UL>
<BR>[Called By]<UL><LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IPD_Command
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_SeverInfo
</UL>

<P><STRONG><a name="[1bc]"></a>Sig_QA</STRONG> (Thumb, 2376 bytes, Stack size 9736 bytes, whut_math.o(i.Sig_QA))
<BR><BR>[Stack]<UL><LI>Max Depth = 10268<LI>Call Chain = Sig_QA &rArr; mid_filter &rArr; find_mid
</UL>
<BR>[Calls]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_copy_f32
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_min_f32
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_mean_f32
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_max_f32
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mid_filter
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Pearson_corr
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Normalization
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main_program
</UL>

<P><STRONG><a name="[fd]"></a>SysReset_Condition</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, user_step.o(i.SysReset_Condition))
<BR><BR>[Called By]<UL><LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_PeriodElapsedCallback
<LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UserProcess_UpDataOkCmd
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_24CMD
</UL>

<P><STRONG><a name="[35]"></a>SysTick_Handler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.SysTick_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SysTick_Handler
</UL>
<BR>[Calls]<UL><LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_decrement
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[90]"></a>SystemInit</STRONG> (Thumb, 184 bytes, Stack size 8 bytes, system_gd32f4xx.o(i.SystemInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SystemInit &rArr; system_clock_config
</UL>
<BR>[Calls]<UL><LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_config
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(.text)
</UL>
<P><STRONG><a name="[1c0]"></a>TIMER1_Init</STRONG> (Thumb, 118 bytes, Stack size 32 bytes, time.o(i.TIMER1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = TIMER1_Init &rArr; timer_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_init
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_deinit
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_channel_output_shadow_config
<LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_channel_output_pulse_value_config
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_channel_output_mode_config
<LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_channel_output_config
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[53]"></a>TIMER2_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.TIMER2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIMER2_IRQHandler &rArr; usb_timer_irq
</UL>
<BR>[Calls]<UL><LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_timer_irq
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>TIMER3_IRQHandler</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.TIMER3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = TIMER3_IRQHandler &rArr; TIM_PeriodElapsedCallback &rArr; gd_eval_key_state_get
</UL>
<BR>[Calls]<UL><LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_PeriodElapsedCallback
<LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_flag_get
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1ca]"></a>TIMER3_Init</STRONG> (Thumb, 80 bytes, Stack size 24 bytes, time.o(i.TIMER3_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = TIMER3_Init &rArr; nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_enable
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_init
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_enable
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_deinit
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[6d]"></a>TIMER6_IRQHandler</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.TIMER6_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = TIMER6_IRQHandler &rArr; TIM_PeriodElapsedCallback &rArr; gd_eval_key_state_get
</UL>
<BR>[Calls]<UL><LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_PeriodElapsedCallback
<LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_flag_get
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1cd]"></a>TIMER6_Init</STRONG> (Thumb, 80 bytes, Stack size 24 bytes, time.o(i.TIMER6_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = TIMER6_Init &rArr; nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_enable
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_init
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_enable
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_deinit
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1c9]"></a>TIM_PeriodElapsedCallback</STRONG> (Thumb, 544 bytes, Stack size 8 bytes, time.o(i.TIM_PeriodElapsedCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_PeriodElapsedCallback &rArr; gd_eval_key_state_get
</UL>
<BR>[Calls]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysReset_Condition
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_flag_clear
<LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_key_state_get
</UL>
<BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER6_IRQHandler
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER3_IRQHandler
</UL>

<P><STRONG><a name="[1d0]"></a>UART_IDLECallBack</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, usart.o(i.UART_IDLECallBack))
<BR><BR>[Stack]<UL><LI>Max Depth = 2104<LI>Call Chain = UART_IDLECallBack &rArr; FML_USART_RecvTask &rArr; ReadBytesToBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_flag_clear
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_receive
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FML_USART_RecvTask
</UL>
<BR>[Called By]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
</UL>

<P><STRONG><a name="[1d3]"></a>UART_RxCpltCallback</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, usart.o(i.UART_RxCpltCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = UART_RxCpltCallback &rArr; RecvDataHandler &rArr; AddByteToBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_receive
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RecvDataHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
</UL>

<P><STRONG><a name="[1d4]"></a>USART0_Init</STRONG> (Thumb, 126 bytes, Stack size 8 bytes, usart.o(i.USART0_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = USART0_Init &rArr; usart_baudrate_set &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_transmit_config
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_receive_config
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_enable
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_baudrate_set
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[5d]"></a>USART2_IRQHandler</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.USART2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 2112<LI>Call Chain = USART2_IRQHandler &rArr; UART_IDLECallBack &rArr; FML_USART_RecvTask &rArr; ReadBytesToBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_RxCpltCallback
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_IDLECallBack
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_flag_get
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1db]"></a>USART2_Init</STRONG> (Thumb, 168 bytes, Stack size 8 bytes, usart.o(i.USART2_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = USART2_Init &rArr; usart_baudrate_set &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_transmit_config
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_receive_config
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_enable
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_enable
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_baudrate_set
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[79]"></a>USBFS_IRQHandler</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.USBFS_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 164<LI>Call Chain = USBFS_IRQHandler &rArr; usbd_isr &rArr; usbd_int_epout &rArr; usbd_setup_transc &rArr; usbd_ctl_send &rArr; usbd_ep_send &rArr; usb_transc_inxfer &rArr; usb_txfifo_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_isr
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[60]"></a>USBFS_WKUP_IRQHandler</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.USBFS_WKUP_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = USBFS_WKUP_IRQHandler &rArr; resume_mcu_clk
</UL>
<BR>[Calls]<UL><LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exti_interrupt_flag_clear
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_pll48m_clock_config
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_ck48m_clock_config
<LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_clock_active
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;resume_mcu_clk
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[1e2]"></a>USB_RunTask</STRONG> (Thumb, 402 bytes, Stack size 16 bytes, usb.o(i.USB_RunTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = USB_RunTask &rArr; usbd_ep_recev &rArr; usb_transc_outxfer
</UL>
<BR>[Calls]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Chose_TS5A3359_GAIN
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_recev
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncpy
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strstr
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[16f]"></a>USER_EEPROM_RedeByte</STRONG> (Thumb, 72 bytes, Stack size 24 bytes, 25lc080a.o(i.USER_EEPROM_RedeByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = USER_EEPROM_RedeByte &rArr; EEPROM_SPI_ReadBuffer &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_ReadBuffer
</UL>
<BR>[Called By]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USER_EEPROM_WriteByte
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_WritePage
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_Init
</UL>

<P><STRONG><a name="[18c]"></a>USER_EEPROM_RedePage</STRONG> (Thumb, 680 bytes, Stack size 72 bytes, 25lc080a.o(i.USER_EEPROM_RedePage))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = USER_EEPROM_RedePage &rArr; EEPROM_SPI_ReadBuffer &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_ReadBuffer
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16_USB
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;malloc
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;free
</UL>
<BR>[Called By]<UL><LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_SeverInfo
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetWifiInFo
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_AlgorithmInFo
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_AlgorithmInFo
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_HardVer
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_DevID
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_HardVer
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_DevID
</UL>

<P><STRONG><a name="[fc]"></a>USER_EEPROM_WriteByte</STRONG> (Thumb, 556 bytes, Stack size 80 bytes, 25lc080a.o(i.USER_EEPROM_WriteByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = USER_EEPROM_WriteByte &rArr; EEPROM_SPI_WriteBuffer &rArr; EEPROM_SPI_WritePage &rArr; EEPROM_SPI_WaitStandbyState &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WriteBuffer
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_ReadBuffer
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16_USB
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USER_EEPROM_RedeByte
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;malloc
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;free
</UL>
<BR>[Called By]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_WritePage
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_Init
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IPD_Command
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_24CMD
</UL>

<P><STRONG><a name="[1ba]"></a>USER_EEPROM_WritePage</STRONG> (Thumb, 44 bytes, Stack size 24 bytes, 25lc080a.o(i.USER_EEPROM_WritePage))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = USER_EEPROM_WritePage &rArr; EEPROM_WritePage &rArr; USER_EEPROM_WriteByte &rArr; EEPROM_SPI_WriteBuffer &rArr; EEPROM_SPI_WritePage &rArr; EEPROM_SPI_WaitStandbyState &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_WritePage
</UL>
<BR>[Called By]<UL><LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_AlgorithmInFo
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_HardVer
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_DevID
</UL>

<P><STRONG><a name="[31]"></a>UsageFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_it.o(i.UsageFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450_470.o(RESET)
</UL>
<P><STRONG><a name="[93]"></a>UserProcess_BLEADVSTOPCmd</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, esp32_wifi.o(i.UserProcess_BLEADVSTOPCmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = UserProcess_BLEADVSTOPCmd
</UL>
<BR>[Address Reference Count : 1]<UL><LI> user_step.o(i.IPD_Command)
</UL>
<P><STRONG><a name="[92]"></a>UserProcess_UpDataOkCmd</STRONG> (Thumb, 16 bytes, Stack size 16 bytes, esp32_wifi.o(i.UserProcess_UpDataOkCmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = UserProcess_UpDataOkCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysReset_Condition
</UL>
<BR>[Address Reference Count : 1]<UL><LI> user_step.o(i.IPD_Command)
</UL>
<P><STRONG><a name="[1e4]"></a>User_Init</STRONG> (Thumb, 200 bytes, Stack size 24 bytes, user_step.o(i.User_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = User_Init &rArr; USER_EEPROM_WriteByte &rArr; EEPROM_SPI_WriteBuffer &rArr; EEPROM_SPI_WritePage &rArr; EEPROM_SPI_WaitStandbyState &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC2_Set_Vol
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC1_Set_Vol
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USER_EEPROM_WriteByte
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USER_EEPROM_RedeByte
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_SeverInfo
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_zhcdInFo
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_AlgorithmInFo
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[f0]"></a>WIFI_Send_AD</STRONG> (Thumb, 546 bytes, Stack size 2064 bytes, user_step.o(i.WIFI_Send_AD))
<BR><BR>[Stack]<UL><LI>Max Depth = 2184<LI>Call Chain = WIFI_Send_AD &rArr; API_LanSock1Send_CRC &rArr; send &rArr; send_data_processing &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_LanSock1Send_CRC
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Float2char
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_RunTask
</UL>

<P><STRONG><a name="[1e6]"></a>__0printf</STRONG> (Thumb, 22 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[26e]"></a>__1printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[ee]"></a>__2printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_w5500_mac
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reset_w5500
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WIFI_Send_AD
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PHY_check
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHCP_run
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CDC_Command
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_do_tcp_client
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_RNG_Init
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Init_LAN
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_RunTask
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Erase_Flash
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;connect
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Printf_Hex
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Init_Net_Parameters
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Write_SeverInfo
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_29CMD
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_28CMD
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_26CMD
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_24CMD
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_23CMD
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_22CMD
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_21CMD
<LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wiz_write_buf
<LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wiz_read_buf
<LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_data_processing
<LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recv_data_processing
<LI><a href="#[21f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recvfrom
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_dhcp_client
</UL>

<P><STRONG><a name="[26f]"></a>__c89printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[270]"></a>printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[1e8]"></a>__0snprintf</STRONG> (Thumb, 44 bytes, Stack size 32 bytes, printfa.o(i.__0snprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[271]"></a>__1snprintf</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, printfa.o(i.__0snprintf), UNUSED)

<P><STRONG><a name="[1a6]"></a>__2snprintf</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, printfa.o(i.__0snprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;week_str2num
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;month_str2num
<LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XOnPackRFPOWERCmd
<LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnUnpackJAPAck
<LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnPackTCPOrUDPDataCmd
<LI><a href="#[f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnPackSetSNTPCFG_Cmd
<LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnPackRFPOWERCmd
<LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnPackJAPCmd
<LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnPackCWModeCmd
<LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnPackBuildTCPOrUDPCmd
<LI><a href="#[12]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnPackBLENAME_Cmd
<LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnPackBLEADVDATA_Cmd
</UL>

<P><STRONG><a name="[272]"></a>__c89snprintf</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, printfa.o(i.__0snprintf), UNUSED)

<P><STRONG><a name="[273]"></a>snprintf</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, printfa.o(i.__0snprintf), UNUSED)

<P><STRONG><a name="[1e9]"></a>__0sprintf</STRONG> (Thumb, 34 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[274]"></a>__1sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[192]"></a>__2sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HEXArrayToStringArray
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_DHCP_REQUEST
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_DHCP_DISCOVER
</UL>

<P><STRONG><a name="[275]"></a>__c89sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[276]"></a>sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[9d]"></a>__aeabi_errno_addr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, errno.o(i.__aeabi_errno_addr))
<BR><BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_strtoul
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strtol
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atoi
</UL>

<P><STRONG><a name="[277]"></a>__rt_errno_addr</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, errno.o(i.__aeabi_errno_addr), UNUSED)

<P><STRONG><a name="[182]"></a>__hardfp_fabs</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, fabs.o(i.__hardfp_fabs))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PD_AUTO_Process
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Sig_QA
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Moving_process
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Energy_process
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;freq_modify
</UL>

<P><STRONG><a name="[198]"></a>__hardfp_sqrt</STRONG> (Thumb, 122 bytes, Stack size 32 bytes, sqrt.o(i.__hardfp_sqrt))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = __hardfp_sqrt &rArr; _dsqrt &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Heart_pretreatment
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Pearson_corr
</UL>

<P><STRONG><a name="[bd]"></a>__hardfp_sqrtf</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, sqrtf.o(i.__hardfp_sqrtf))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __hardfp_sqrtf
</UL>
<BR>[Calls]<UL><LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cmplx_mag_f32
</UL>

<P><STRONG><a name="[278]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[279]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[27a]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)

<P><STRONG><a name="[1ea]"></a>__set_errno</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, errno.o(i.__set_errno))
<BR><BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrtf
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
</UL>

<P><STRONG><a name="[e2]"></a>adc_calibration_enable</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, gd32f4xx_adc.o(i.adc_calibration_enable))
<BR><BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC2_Init
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_Init
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_Init
</UL>

<P><STRONG><a name="[d6]"></a>adc_channel_length_config</STRONG> (Thumb, 82 bytes, Stack size 12 bytes, gd32f4xx_adc.o(i.adc_channel_length_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = adc_channel_length_config
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC2_Init
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_Init
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_Init
</UL>

<P><STRONG><a name="[d3]"></a>adc_clock_config</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, gd32f4xx_adc.o(i.adc_clock_config))
<BR><BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC2_Init
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_Init
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_Init
</UL>

<P><STRONG><a name="[da]"></a>adc_data_alignment_config</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, gd32f4xx_adc.o(i.adc_data_alignment_config))
<BR><BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC2_Init
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_Init
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_Init
</UL>

<P><STRONG><a name="[dd]"></a>adc_dma_mode_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_adc.o(i.adc_dma_mode_enable))
<BR><BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC2_Init
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_Init
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_Init
</UL>

<P><STRONG><a name="[dc]"></a>adc_dma_request_after_last_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_adc.o(i.adc_dma_request_after_last_enable))
<BR><BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC2_Init
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_Init
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_Init
</UL>

<P><STRONG><a name="[e0]"></a>adc_enable</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_adc.o(i.adc_enable))
<BR><BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC2_Init
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_Init
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_Init
</UL>

<P><STRONG><a name="[d8]"></a>adc_external_trigger_config</STRONG> (Thumb, 52 bytes, Stack size 0 bytes, gd32f4xx_adc.o(i.adc_external_trigger_config))
<BR><BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC2_Init
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_Init
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_Init
</UL>

<P><STRONG><a name="[d9]"></a>adc_external_trigger_source_config</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, gd32f4xx_adc.o(i.adc_external_trigger_source_config))
<BR><BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC2_Init
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_Init
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_Init
</UL>

<P><STRONG><a name="[de]"></a>adc_oversample_mode_config</STRONG> (Thumb, 58 bytes, Stack size 12 bytes, gd32f4xx_adc.o(i.adc_oversample_mode_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = adc_oversample_mode_config
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC2_Init
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_Init
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_Init
</UL>

<P><STRONG><a name="[df]"></a>adc_oversample_mode_enable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_adc.o(i.adc_oversample_mode_enable))
<BR><BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC2_Init
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_Init
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_Init
</UL>

<P><STRONG><a name="[d7]"></a>adc_regular_channel_config</STRONG> (Thumb, 172 bytes, Stack size 20 bytes, gd32f4xx_adc.o(i.adc_regular_channel_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = adc_regular_channel_config
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC2_Init
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_Init
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_Init
</UL>

<P><STRONG><a name="[db]"></a>adc_resolution_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_adc.o(i.adc_resolution_config))
<BR><BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC2_Init
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_Init
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_Init
</UL>

<P><STRONG><a name="[d4]"></a>adc_sync_mode_config</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, gd32f4xx_adc.o(i.adc_sync_mode_config))
<BR><BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC2_Init
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_Init
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_Init
</UL>

<P><STRONG><a name="[13c]"></a>arm_rfft_fast_f32_app</STRONG> (Thumb, 160 bytes, Stack size 16672 bytes, mymath.o(i.arm_rfft_fast_f32_app))
<BR><BR>[Stack]<UL><LI>Max Depth = 17080<LI>Call Chain = arm_rfft_fast_f32_app &rArr; arm_rfft_fast_f32 &rArr; arm_cfft_f32 &rArr; arm_cfft_radix8by4_f32 &rArr; arm_radix8_butterfly_f32
</UL>
<BR>[Calls]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_copy_f32
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_rfft_fast_init_f32
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_rfft_fast_f32
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_mean_f32
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cmplx_mag_f32
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Heart_quality_process
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Breath_rate_PSD
</UL>

<P><STRONG><a name="[fa]"></a>bsp_CmpCpuFlash</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, flash.o(i.bsp_CmpCpuFlash))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = bsp_CmpCpuFlash
</UL>
<BR>[Called By]<UL><LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IPD_Command
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_WriteCpuFlash
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_23CMD
</UL>

<P><STRONG><a name="[f7]"></a>bsp_EraseCpuFlash</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, flash.o(i.bsp_EraseCpuFlash))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = bsp_EraseCpuFlash &rArr; fmc_sector_erase &rArr; fmc_ready_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_unlock
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_sector_erase
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_lock
<LI><a href="#[1f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_GetSector
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FLASH_If_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IPD_Command
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_22CMD
</UL>

<P><STRONG><a name="[1f4]"></a>bsp_GetSector</STRONG> (Thumb, 454 bytes, Stack size 0 bytes, flash.o(i.bsp_GetSector))
<BR><BR>[Called By]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_EraseCpuFlash
</UL>

<P><STRONG><a name="[f9]"></a>bsp_WriteCpuFlash</STRONG> (Thumb, 160 bytes, Stack size 32 bytes, flash.o(i.bsp_WriteCpuFlash))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = bsp_WriteCpuFlash &rArr; fmc_word_program &rArr; fmc_ready_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_word_program
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_unlock
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_lock
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_CmpCpuFlash
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IPD_Command
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_23CMD
</UL>

<P><STRONG><a name="[159]"></a>check_DHCP_leasedIP</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, dhcp.o(i.check_DHCP_leasedIP))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = check_DHCP_leasedIP &rArr; send_DHCP_DECLINE &rArr; sendto &rArr; send_data_processing &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sendto
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_DHCP_DECLINE
</UL>
<BR>[Called By]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHCP_run
</UL>

<P><STRONG><a name="[158]"></a>check_DHCP_timeout</STRONG> (Thumb, 152 bytes, Stack size 8 bytes, dhcp.o(i.check_DHCP_timeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = check_DHCP_timeout &rArr; send_DHCP_REQUEST &rArr; sendto &rArr; send_data_processing &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_DHCP_REQUEST
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_DHCP_DISCOVER
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reset_DHCP_timeout
</UL>
<BR>[Called By]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHCP_run
</UL>

<P><STRONG><a name="[135]"></a>close</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, socket.o(i.close))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = close &rArr; IINCHIP_WRITE &rArr; IINCHIP_SpiSendData &rArr; API_SPI0_Send_Read_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_WRITE
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_READ
</UL>
<BR>[Called By]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PHY_check
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_do_tcp_client
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;socket
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send
</UL>

<P><STRONG><a name="[12e]"></a>connect</STRONG> (Thumb, 332 bytes, Stack size 32 bytes, socket.o(i.connect))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = connect &rArr; getSn_IR &rArr; IINCHIP_READ &rArr; IINCHIP_SpiSendData &rArr; API_SPI0_Send_Read_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;getSn_IR
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_WRITE
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_READ
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_do_tcp_client
</UL>

<P><STRONG><a name="[153]"></a>dac_concurrent_data_set</STRONG> (Thumb, 58 bytes, Stack size 8 bytes, gd32f4xx_dac.o(i.dac_concurrent_data_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = dac_concurrent_data_set
</UL>
<BR>[Called By]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC_Init
</UL>

<P><STRONG><a name="[152]"></a>dac_concurrent_enable</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_dac.o(i.dac_concurrent_enable))
<BR><BR>[Called By]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC_Init
</UL>

<P><STRONG><a name="[14b]"></a>dac_data_set</STRONG> (Thumb, 82 bytes, Stack size 0 bytes, gd32f4xx_dac.o(i.dac_data_set))
<BR><BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC2_Set_Vol
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC1_Set_Vol
</UL>

<P><STRONG><a name="[14e]"></a>dac_deinit</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, gd32f4xx_dac.o(i.dac_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = dac_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC_Init
</UL>

<P><STRONG><a name="[151]"></a>dac_output_buffer_disable</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, gd32f4xx_dac.o(i.dac_output_buffer_disable))
<BR><BR>[Called By]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC_Init
</UL>

<P><STRONG><a name="[14f]"></a>dac_trigger_disable</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, gd32f4xx_dac.o(i.dac_trigger_disable))
<BR><BR>[Called By]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC_Init
</UL>

<P><STRONG><a name="[150]"></a>dac_wave_mode_config</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, gd32f4xx_dac.o(i.dac_wave_mode_config))
<BR><BR>[Called By]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC_Init
</UL>

<P><STRONG><a name="[29]"></a>default_ip_assign</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, dhcp.o(i.default_ip_assign))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = default_ip_assign &rArr; setSUBR &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setSUBR
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setSIPR
<LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setGAR
</UL>
<BR>[Called By]<UL><LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;default_ip_update
</UL>
<BR>[Address Reference Count : 1]<UL><LI> dhcp.o(.data)
</UL>
<P><STRONG><a name="[2b]"></a>default_ip_conflict</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, dhcp.o(i.default_ip_conflict))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = default_ip_conflict &rArr; setSHAR &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setSHAR
<LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setMR
</UL>
<BR>[Address Reference Count : 1]<UL><LI> dhcp.o(.data)
</UL>
<P><STRONG><a name="[2a]"></a>default_ip_update</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, dhcp.o(i.default_ip_update))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = default_ip_update &rArr; default_ip_assign &rArr; setSUBR &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setSHAR
<LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setMR
<LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;default_ip_assign
</UL>
<BR>[Address Reference Count : 1]<UL><LI> dhcp.o(.data)
</UL>
<P><STRONG><a name="[e1]"></a>delay_1ms</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, systick.o(i.delay_1ms))
<BR><BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_ReadBuffer
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reset_w5500
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PHY_check
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IPD_Command
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CDC_Command
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC2_Init
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_Init
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_Init
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WritePage
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_SendInstruction
</UL>

<P><STRONG><a name="[1be]"></a>delay_decrement</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, systick.o(i.delay_decrement))
<BR><BR>[Called By]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[134]"></a>disconnect</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, socket.o(i.disconnect))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = disconnect &rArr; IINCHIP_WRITE &rArr; IINCHIP_SpiSendData &rArr; API_SPI0_Send_Read_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_WRITE
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_READ
</UL>
<BR>[Called By]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_do_tcp_client
</UL>

<P><STRONG><a name="[162]"></a>dma_channel_enable</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, gd32f4xx_dma.o(i.dma_channel_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = dma_channel_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_CH2_Init
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_CH1_Init
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_CH0_Init
</UL>

<P><STRONG><a name="[15e]"></a>dma_channel_subperipheral_select</STRONG> (Thumb, 38 bytes, Stack size 12 bytes, gd32f4xx_dma.o(i.dma_channel_subperipheral_select))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = dma_channel_subperipheral_select
</UL>
<BR>[Called By]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_CH2_Init
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_CH1_Init
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_CH0_Init
</UL>

<P><STRONG><a name="[15f]"></a>dma_circulation_enable</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, gd32f4xx_dma.o(i.dma_circulation_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = dma_circulation_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_CH2_Init
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_CH1_Init
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_CH0_Init
</UL>

<P><STRONG><a name="[15c]"></a>dma_deinit</STRONG> (Thumb, 164 bytes, Stack size 8 bytes, gd32f4xx_dma.o(i.dma_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = dma_deinit
</UL>
<BR>[Called By]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_CH2_Init
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_CH1_Init
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_CH0_Init
</UL>

<P><STRONG><a name="[161]"></a>dma_interrupt_enable</STRONG> (Thumb, 62 bytes, Stack size 12 bytes, gd32f4xx_dma.o(i.dma_interrupt_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = dma_interrupt_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_CH2_Init
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_CH1_Init
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_CH0_Init
</UL>

<P><STRONG><a name="[e6]"></a>dma_interrupt_flag_clear</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, gd32f4xx_dma.o(i.dma_interrupt_flag_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = dma_interrupt_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_ConvHalfCpltCallback
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_ConvCpltCallback
</UL>

<P><STRONG><a name="[165]"></a>dma_interrupt_flag_get</STRONG> (Thumb, 516 bytes, Stack size 20 bytes, gd32f4xx_dma.o(i.dma_interrupt_flag_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = dma_interrupt_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Channel2_IRQHandler
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Channel1_IRQHandler
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Channel0_IRQHandler
</UL>

<P><STRONG><a name="[15d]"></a>dma_single_data_mode_init</STRONG> (Thumb, 340 bytes, Stack size 16 bytes, gd32f4xx_dma.o(i.dma_single_data_mode_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = dma_single_data_mode_init
</UL>
<BR>[Called By]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_CH2_Init
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_CH1_Init
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_CH0_Init
</UL>

<P><STRONG><a name="[203]"></a>error_process</STRONG> (Thumb, 594 bytes, Stack size 3160 bytes, mymath.o(i.error_process))
<BR><BR>[Stack]<UL><LI>Max Depth = 3568<LI>Call Chain = error_process &rArr; arm_rfft_fast_f32 &rArr; arm_cfft_f32 &rArr; arm_cfft_radix8by4_f32 &rArr; arm_radix8_butterfly_f32
</UL>
<BR>[Calls]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_copy_f32
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_rfft_fast_init_f32
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_rfft_fast_f32
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_mean_f32
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cmplx_mag_f32
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main_program
</UL>

<P><STRONG><a name="[180]"></a>exti_interrupt_flag_clear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_exti.o(i.exti_interrupt_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBFS_WKUP_IRQHandler
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI10_15_IRQHandler
</UL>

<P><STRONG><a name="[17f]"></a>exti_interrupt_flag_get</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_exti.o(i.exti_interrupt_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI10_15_IRQHandler
</UL>

<P><STRONG><a name="[21c]"></a>find_mid</STRONG> (Thumb, 194 bytes, Stack size 20 bytes, whut_math.o(i.find_mid))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = find_mid
</UL>
<BR>[Called By]<UL><LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mid_filter
</UL>

<P><STRONG><a name="[1f6]"></a>fmc_lock</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_fmc.o(i.fmc_lock))
<BR><BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_WriteCpuFlash
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_EraseCpuFlash
</UL>

<P><STRONG><a name="[204]"></a>fmc_ready_wait</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, gd32f4xx_fmc.o(i.fmc_ready_wait))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = fmc_ready_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_state_get
</UL>
<BR>[Called By]<UL><LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_word_program
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_sector_erase
</UL>

<P><STRONG><a name="[1f5]"></a>fmc_sector_erase</STRONG> (Thumb, 90 bytes, Stack size 12 bytes, gd32f4xx_fmc.o(i.fmc_sector_erase))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = fmc_sector_erase &rArr; fmc_ready_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_ready_wait
</UL>
<BR>[Called By]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_EraseCpuFlash
</UL>

<P><STRONG><a name="[205]"></a>fmc_state_get</STRONG> (Thumb, 70 bytes, Stack size 0 bytes, gd32f4xx_fmc.o(i.fmc_state_get))
<BR><BR>[Called By]<UL><LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_ready_wait
</UL>

<P><STRONG><a name="[1f2]"></a>fmc_unlock</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f4xx_fmc.o(i.fmc_unlock))
<BR><BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_WriteCpuFlash
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_EraseCpuFlash
</UL>

<P><STRONG><a name="[1f7]"></a>fmc_word_program</STRONG> (Thumb, 80 bytes, Stack size 16 bytes, gd32f4xx_fmc.o(i.fmc_word_program))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = fmc_word_program &rArr; fmc_ready_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_ready_wait
</UL>
<BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_WriteCpuFlash
</UL>

<P><STRONG><a name="[94]"></a>fputc</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, usart.o(i.fputc))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = fputc &rArr; usart_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_flag_get
<LI><a href="#[206]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_transmit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0printf)
</UL>
<P><STRONG><a name="[16e]"></a>free</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, malloc.o(i.free))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = free
</UL>
<BR>[Called By]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USER_EEPROM_WriteByte
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USER_EEPROM_RedePage
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_WritePage
</UL>

<P><STRONG><a name="[138]"></a>freq_modify</STRONG> (Thumb, 4390 bytes, Stack size 1536 bytes, whut_math.o(i.freq_modify))
<BR><BR>[Stack]<UL><LI>Max Depth = 1672<LI>Call Chain = freq_modify &rArr; peak_diff_process
</UL>
<BR>[Calls]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_copy_f32
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_max_f32
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;peak_diff_process
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Breath_FreqProcess
</UL>

<P><STRONG><a name="[141]"></a>gd_eval_BEEP_off</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f470v_start.o(i.gd_eval_BEEP_off))
<BR><BR>[Called By]<UL><LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IPD_Command
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CDC_Command
</UL>

<P><STRONG><a name="[140]"></a>gd_eval_BEEP_on</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f470v_start.o(i.gd_eval_BEEP_on))
<BR><BR>[Called By]<UL><LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IPD_Command
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CDC_Command
</UL>

<P><STRONG><a name="[1cf]"></a>gd_eval_key_state_get</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, gd32f470v_start.o(i.gd_eval_key_state_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = gd_eval_key_state_get
</UL>
<BR>[Calls]<UL><LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_input_bit_get
</UL>
<BR>[Called By]<UL><LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_PeriodElapsedCallback
</UL>

<P><STRONG><a name="[226]"></a>getIINCHIP_TxMAX</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, w5500.o(i.getIINCHIP_TxMAX))
<BR><BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sendto
</UL>

<P><STRONG><a name="[1ae]"></a>getPHYStatus</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, w5500_conf.o(i.getPHYStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = getPHYStatus &rArr; IINCHIP_READ &rArr; IINCHIP_SpiSendData &rArr; API_SPI0_Send_Read_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_READ
</UL>
<BR>[Called By]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PHY_check
</UL>

<P><STRONG><a name="[20a]"></a>getSHAR</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, w5500.o(i.getSHAR))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = getSHAR &rArr; wiz_read_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wiz_read_buf
</UL>
<BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_w5500_mac
<LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;makeDHCPMSG
</UL>

<P><STRONG><a name="[12f]"></a>getSn_IR</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, w5500.o(i.getSn_IR))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = getSn_IR &rArr; IINCHIP_READ &rArr; IINCHIP_SpiSendData &rArr; API_SPI0_Send_Read_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_READ
</UL>
<BR>[Called By]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_do_tcp_client
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;connect
</UL>

<P><STRONG><a name="[131]"></a>getSn_RX_RSR</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, w5500.o(i.getSn_RX_RSR))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = getSn_RX_RSR &rArr; IINCHIP_READ &rArr; IINCHIP_SpiSendData &rArr; API_SPI0_Send_Read_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_READ
</UL>
<BR>[Called By]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_do_tcp_client
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parseDHCPMSG
</UL>

<P><STRONG><a name="[12c]"></a>getSn_SR</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, w5500.o(i.getSn_SR))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = getSn_SR &rArr; IINCHIP_READ &rArr; IINCHIP_SpiSendData &rArr; API_SPI0_Send_Read_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_READ
</UL>
<BR>[Called By]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHCP_run
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_do_tcp_client
</UL>

<P><STRONG><a name="[20c]"></a>getSn_TX_FSR</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, w5500.o(i.getSn_TX_FSR))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = getSn_TX_FSR &rArr; IINCHIP_READ &rArr; IINCHIP_SpiSendData &rArr; API_SPI0_Send_Read_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_READ
</UL>
<BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send
</UL>

<P><STRONG><a name="[11e]"></a>get_hard_rand_data</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, api_tnrg.o(i.get_hard_rand_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = get_hard_rand_data
</UL>
<BR>[Calls]<UL><LI><a href="#[20d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_get_true_random_data
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_RNG_Init
</UL>

<P><STRONG><a name="[128]"></a>gpio_af_set</STRONG> (Thumb, 94 bytes, Stack size 20 bytes, gd32f4xx_gpio.o(i.gpio_af_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_af_set
</UL>
<BR>[Called By]<UL><LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_gpio_config
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_Init
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Init
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_SPI0_Init
</UL>

<P><STRONG><a name="[ef]"></a>gpio_bit_reset</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(i.gpio_bit_reset))
<BR><BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_ReadBuffer
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reset_w5500
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_do_tcp_client
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_RunTask
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Chose_TS5A3359_GAIN
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_WriteEnable
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_WriteDisable
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WritePage
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WaitStandbyState
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;iinchip_csoff
</UL>

<P><STRONG><a name="[f1]"></a>gpio_bit_set</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(i.gpio_bit_set))
<BR><BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_ReadBuffer
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reset_w5500
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_do_tcp_client
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_RunTask
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Chose_TS5A3359_GAIN
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_WriteEnable
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_WriteDisable
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WritePage
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WaitStandbyState
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_SPI0_Init
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;iinchip_cson
</UL>

<P><STRONG><a name="[209]"></a>gpio_input_bit_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(i.gpio_input_bit_get))
<BR><BR>[Called By]<UL><LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gd_eval_key_state_get
</UL>

<P><STRONG><a name="[d5]"></a>gpio_mode_set</STRONG> (Thumb, 78 bytes, Stack size 20 bytes, gd32f4xx_gpio.o(i.gpio_mode_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_mode_set
</UL>
<BR>[Called By]<UL><LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_gpio_config
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_Init
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Init
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC_Init
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC2_Init
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_Init
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_Init
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_GPIO_TS5A339
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_LED_GPIO
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_SPI0_Init
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_GPIO_Init
</UL>

<P><STRONG><a name="[118]"></a>gpio_output_options_set</STRONG> (Thumb, 66 bytes, Stack size 20 bytes, gd32f4xx_gpio.o(i.gpio_output_options_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_output_options_set
</UL>
<BR>[Called By]<UL><LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_gpio_config
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_Init
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Init
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_GPIO_TS5A339
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_LED_GPIO
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_SPI0_Init
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_GPIO_Init
</UL>

<P><STRONG><a name="[19b]"></a>iinchip_csoff</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, w5500_conf.o(i.iinchip_csoff))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = iinchip_csoff
</UL>
<BR>[Calls]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wiz_write_buf
<LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wiz_read_buf
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_WRITE
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_READ
</UL>

<P><STRONG><a name="[19d]"></a>iinchip_cson</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, w5500_conf.o(i.iinchip_cson))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = iinchip_cson
</UL>
<BR>[Calls]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
</UL>
<BR>[Called By]<UL><LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wiz_write_buf
<LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wiz_read_buf
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_WRITE
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_READ
</UL>

<P><STRONG><a name="[154]"></a>init_dhcp_client</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, dhcp.o(i.init_dhcp_client))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = init_dhcp_client &rArr; setSUBR &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setSUBR
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setSIPR
<LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setGAR
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHCP_run
</UL>

<P><STRONG><a name="[22b]"></a>int_to_unicode</STRONG> (Thumb, 60 bytes, Stack size 12 bytes, usbd_enum.o(i.int_to_unicode))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = int_to_unicode
</UL>
<BR>[Called By]<UL><LI><a href="#[22a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;serial_string_get
</UL>

<P><STRONG><a name="[8f]"></a>main</STRONG> (Thumb, 326 bytes, Stack size 0 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 25424<LI>Call Chain = main &rArr; ADC_RunTask &rArr; main_program &rArr; Heart_quality_process &rArr; arm_rfft_fast_f32_app &rArr; arm_rfft_fast_f32 &rArr; arm_cfft_f32 &rArr; arm_cfft_radix8by4_f32 &rArr; arm_radix8_butterfly_f32
</UL>
<BR>[Calls]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_current_time_get
<LI><a href="#[213]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_vector_table_set
<LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_init
<LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_timer_init
<LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_rcu_config
<LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_intr_config
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_gpio_config
<LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;systick_config
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;socket_buf_init
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_w5500_mac
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reset_w5500
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_Init
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_RunTask
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_Init
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Init
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER6_Init
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER3_Init
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER1_Init
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Init
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PHY_check
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mymath_User_Init
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IPD_Command
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_CH2_Init
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_CH1_Init
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_CH0_Init
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHCP_run
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC_Init
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CDC_Command
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_do_tcp_client
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_RNG_Init
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_Init_LAN
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ExecuteSeverCMD
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC2_Init
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_Init
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_Init
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_RunTask
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_enable
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[eb]"></a>main_program</STRONG> (Thumb, 592 bytes, Stack size 32 bytes, mymath.o(i.main_program))
<BR><BR>[Stack]<UL><LI>Max Depth = 25320<LI>Call Chain = main_program &rArr; Heart_quality_process &rArr; arm_rfft_fast_f32_app &rArr; arm_rfft_fast_f32 &rArr; arm_cfft_f32 &rArr; arm_cfft_radix8by4_f32 &rArr; arm_radix8_butterfly_f32
</UL>
<BR>[Calls]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_copy_f32
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_mean_f32
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Sig_QA
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Findpeak
<LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;error_process
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PD_process
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Moving_process
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InBed_process
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Heart_quality_process
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Heart_pretreatment
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Heart_peak_Rate_new
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Energy_process
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Breath_rate_PSD
<LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Boot_process
</UL>
<BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_RunTask
</UL>

<P><STRONG><a name="[21b]"></a>makeDHCPMSG</STRONG> (Thumb, 380 bytes, Stack size 24 bytes, dhcp.o(i.makeDHCPMSG))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = makeDHCPMSG &rArr; getSHAR &rArr; wiz_read_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;getSHAR
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_DHCP_REQUEST
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_DHCP_DISCOVER
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_DHCP_DECLINE
</UL>

<P><STRONG><a name="[16d]"></a>malloc</STRONG> (Thumb, 92 bytes, Stack size 20 bytes, malloc.o(i.malloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = malloc
</UL>
<BR>[Called By]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USER_EEPROM_WriteByte
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USER_EEPROM_RedePage
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_WritePage
</UL>

<P><STRONG><a name="[1bd]"></a>mid_filter</STRONG> (Thumb, 200 bytes, Stack size 512 bytes, whut_math.o(i.mid_filter))
<BR><BR>[Stack]<UL><LI>Max Depth = 532<LI>Call Chain = mid_filter &rArr; find_mid
</UL>
<BR>[Calls]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_copy_f32
<LI><a href="#[21c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_mid
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Sig_QA
</UL>

<P><STRONG><a name="[17c]"></a>month_str2num</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, esp32_wifi.o(i.month_str2num))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = month_str2num &rArr; __2snprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP_GetSntpTimeInfo
</UL>

<P><STRONG><a name="[196]"></a>my_fir_function</STRONG> (Thumb, 208 bytes, Stack size 20 bytes, whut_math.o(i.my_fir_function))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = my_fir_function
</UL>
<BR>[Called By]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Heart_pretreatment
</UL>

<P><STRONG><a name="[1a4]"></a>my_fir_struct_init</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, whut_math.o(i.my_fir_struct_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = my_fir_struct_init
</UL>
<BR>[Called By]<UL><LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mymath_User_Init
</UL>

<P><STRONG><a name="[160]"></a>nvic_irq_enable</STRONG> (Thumb, 186 bytes, Stack size 24 bytes, gd32f4xx_misc.o(i.nvic_irq_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_priority_group_set
</UL>
<BR>[Called By]<UL><LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_timer_init
<LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_intr_config
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_Init
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER6_Init
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER3_Init
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_CH2_Init
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_CH1_Init
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_CH0_Init
</UL>

<P><STRONG><a name="[21e]"></a>nvic_priority_group_set</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_misc.o(i.nvic_priority_group_set))
<BR><BR>[Called By]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
<LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_timer_init
<LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_intr_config
</UL>

<P><STRONG><a name="[213]"></a>nvic_vector_table_set</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_misc.o(i.nvic_vector_table_set))
<BR><BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[155]"></a>parseDHCPMSG</STRONG> (Thumb, 458 bytes, Stack size 40 bytes, dhcp.o(i.parseDHCPMSG))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = parseDHCPMSG &rArr; recvfrom &rArr; wiz_read_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;getSn_RX_RSR
<LI><a href="#[21f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recvfrom
</UL>
<BR>[Called By]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHCP_run
</UL>

<P><STRONG><a name="[208]"></a>peak_diff_process</STRONG> (Thumb, 174 bytes, Stack size 136 bytes, whut_math.o(i.peak_diff_process))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = peak_diff_process
</UL>
<BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;freq_modify
</UL>

<P><STRONG><a name="[1b1]"></a>pmu_backup_write_enable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_pmu.o(i.pmu_backup_write_enable))
<BR><BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Init
</UL>

<P><STRONG><a name="[255]"></a>pmu_to_deepsleepmode</STRONG> (Thumb, 204 bytes, Stack size 8 bytes, gd32f4xx_pmu.o(i.pmu_to_deepsleepmode))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = pmu_to_deepsleepmode
</UL>
<BR>[Called By]<UL><LI><a href="#[254]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_suspend
</UL>

<P><STRONG><a name="[1e0]"></a>rcu_ck48m_clock_config</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_ck48m_clock_config))
<BR><BR>[Called By]<UL><LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_rcu_config
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBFS_WKUP_IRQHandler
</UL>

<P><STRONG><a name="[22f]"></a>rcu_clock_freq_get</STRONG> (Thumb, 264 bytes, Stack size 84 bytes, gd32f4xx_rcu.o(i.rcu_clock_freq_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = rcu_clock_freq_get
</UL>
<BR>[Called By]<UL><LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_baudrate_set
</UL>

<P><STRONG><a name="[220]"></a>rcu_flag_get</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_osci_stab_wait
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;resume_mcu_clk
</UL>

<P><STRONG><a name="[1b2]"></a>rcu_osci_on</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_osci_on))
<BR><BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Init
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;resume_mcu_clk
</UL>

<P><STRONG><a name="[1b3]"></a>rcu_osci_stab_wait</STRONG> (Thumb, 342 bytes, Stack size 20 bytes, gd32f4xx_rcu.o(i.rcu_osci_stab_wait))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = rcu_osci_stab_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[220]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Init
</UL>

<P><STRONG><a name="[d2]"></a>rcu_periph_clock_enable</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_periph_clock_enable))
<BR><BR>[Called By]<UL><LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_timer_init
<LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_rcu_config
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_gpio_config
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_Init
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Init
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER6_Init
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER3_Init
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER1_Init
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Init
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_CH2_Init
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_CH1_Init
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_CH0_Init
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DAC_Init
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_RNG_Init
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC2_Init
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_Init
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_Init
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBFS_WKUP_IRQHandler
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_GPIO_TS5A339
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_LED_GPIO
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_SPI0_Init
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_GPIO_Init
</UL>

<P><STRONG><a name="[1fd]"></a>rcu_periph_reset_disable</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_periph_reset_disable))
<BR><BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dac_deinit
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_deinit
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_deinit
</UL>

<P><STRONG><a name="[1fc]"></a>rcu_periph_reset_enable</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_periph_reset_enable))
<BR><BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dac_deinit
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trng_deinit
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_deinit
</UL>

<P><STRONG><a name="[1df]"></a>rcu_pll48m_clock_config</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_pll48m_clock_config))
<BR><BR>[Called By]<UL><LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_rcu_config
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBFS_WKUP_IRQHandler
</UL>

<P><STRONG><a name="[1b4]"></a>rcu_rtc_clock_config</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_rtc_clock_config))
<BR><BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Init
</UL>

<P><STRONG><a name="[222]"></a>rcu_system_clock_source_config</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_system_clock_source_config))
<BR><BR>[Called By]<UL><LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;resume_mcu_clk
</UL>

<P><STRONG><a name="[223]"></a>rcu_system_clock_source_get</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_system_clock_source_get))
<BR><BR>[Called By]<UL><LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;resume_mcu_clk
</UL>

<P><STRONG><a name="[132]"></a>recv</STRONG> (Thumb, 66 bytes, Stack size 24 bytes, socket.o(i.recv))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = recv &rArr; recv_data_processing &rArr; wiz_read_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recv_data_processing
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_WRITE
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_READ
</UL>
<BR>[Called By]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_do_tcp_client
</UL>

<P><STRONG><a name="[221]"></a>recv_data_processing</STRONG> (Thumb, 116 bytes, Stack size 24 bytes, w5500.o(i.recv_data_processing))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = recv_data_processing &rArr; wiz_read_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wiz_read_buf
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_WRITE
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_READ
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recv
</UL>

<P><STRONG><a name="[21f]"></a>recvfrom</STRONG> (Thumb, 486 bytes, Stack size 48 bytes, socket.o(i.recvfrom))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = recvfrom &rArr; wiz_read_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wiz_read_buf
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_WRITE
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_READ
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;parseDHCPMSG
</UL>

<P><STRONG><a name="[15a]"></a>reset_DHCP_timeout</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, dhcp.o(i.reset_DHCP_timeout))
<BR><BR>[Called By]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHCP_run
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_DHCP_timeout
</UL>

<P><STRONG><a name="[123]"></a>reset_w5500</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, w5500_conf.o(i.reset_w5500))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = reset_w5500 &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_1ms
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Reset
</UL>

<P><STRONG><a name="[17e]"></a>rtc_current_time_get</STRONG> (Thumb, 96 bytes, Stack size 12 bytes, gd32f4xx_rtc.o(i.rtc_current_time_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = rtc_current_time_get
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP_GetSntpTimeInfo
</UL>

<P><STRONG><a name="[1b6]"></a>rtc_init</STRONG> (Thumb, 190 bytes, Stack size 20 bytes, gd32f4xx_rtc.o(i.rtc_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = rtc_init &rArr; rtc_register_sync_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_register_sync_wait
<LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init_mode_exit
<LI><a href="#[224]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init_mode_enter
</UL>
<BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Init
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_register_set
</UL>

<P><STRONG><a name="[224]"></a>rtc_init_mode_enter</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, gd32f4xx_rtc.o(i.rtc_init_mode_enter))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rtc_init_mode_enter
</UL>
<BR>[Called By]<UL><LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
</UL>

<P><STRONG><a name="[225]"></a>rtc_init_mode_exit</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_rtc.o(i.rtc_init_mode_exit))
<BR><BR>[Called By]<UL><LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
</UL>

<P><STRONG><a name="[17d]"></a>rtc_register_set</STRONG> (Thumb, 42 bytes, Stack size 32 bytes, rtc.o(i.rtc_register_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = rtc_register_set &rArr; rtc_init &rArr; rtc_register_sync_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
</UL>
<BR>[Called By]<UL><LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP_GetSntpTimeInfo
</UL>

<P><STRONG><a name="[1b5]"></a>rtc_register_sync_wait</STRONG> (Thumb, 92 bytes, Stack size 8 bytes, gd32f4xx_rtc.o(i.rtc_register_sync_wait))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rtc_register_sync_wait
</UL>
<BR>[Called By]<UL><LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RTC_Init
</UL>

<P><STRONG><a name="[f4]"></a>send</STRONG> (Thumb, 232 bytes, Stack size 32 bytes, socket.o(i.send))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = send &rArr; send_data_processing &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;close
<LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_data_processing
<LI><a href="#[20c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;getSn_TX_FSR
<LI><a href="#[226]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;getIINCHIP_TxMAX
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_WRITE
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_READ
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_LanSock1Send_CRC
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_29CMD
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_28CMD
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_26CMD
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_24CMD
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_23CMD
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_22CMD
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_ACK_21CMD
</UL>

<P><STRONG><a name="[1fb]"></a>send_DHCP_DECLINE</STRONG> (Thumb, 476 bytes, Stack size 16 bytes, dhcp.o(i.send_DHCP_DECLINE))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = send_DHCP_DECLINE &rArr; sendto &rArr; send_data_processing &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sendto
<LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;makeDHCPMSG
</UL>
<BR>[Called By]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_DHCP_leasedIP
</UL>

<P><STRONG><a name="[156]"></a>send_DHCP_DISCOVER</STRONG> (Thumb, 462 bytes, Stack size 40 bytes, dhcp.o(i.send_DHCP_DISCOVER))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = send_DHCP_DISCOVER &rArr; sendto &rArr; send_data_processing &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sendto
<LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;makeDHCPMSG
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[228]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHCP_run
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_DHCP_timeout
</UL>

<P><STRONG><a name="[157]"></a>send_DHCP_REQUEST</STRONG> (Thumb, 838 bytes, Stack size 40 bytes, dhcp.o(i.send_DHCP_REQUEST))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = send_DHCP_REQUEST &rArr; sendto &rArr; send_data_processing &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sendto
<LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;makeDHCPMSG
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[228]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHCP_run
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_DHCP_timeout
</UL>

<P><STRONG><a name="[227]"></a>send_data_processing</STRONG> (Thumb, 116 bytes, Stack size 24 bytes, w5500.o(i.send_data_processing))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = send_data_processing &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wiz_write_buf
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_WRITE
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_READ
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sendto
</UL>

<P><STRONG><a name="[1fa]"></a>sendto</STRONG> (Thumb, 268 bytes, Stack size 32 bytes, socket.o(i.sendto))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = sendto &rArr; send_data_processing &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_data_processing
<LI><a href="#[226]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;getIINCHIP_TxMAX
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_WRITE
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_READ
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_DHCP_REQUEST
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_DHCP_DISCOVER
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_DHCP_DECLINE
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_DHCP_leasedIP
</UL>

<P><STRONG><a name="[22a]"></a>serial_string_get</STRONG> (Thumb, 66 bytes, Stack size 20 bytes, usbd_enum.o(i.serial_string_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = serial_string_get &rArr; int_to_unicode
</UL>
<BR>[Calls]<UL><LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;int_to_unicode
</UL>
<BR>[Called By]<UL><LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_init
</UL>

<P><STRONG><a name="[200]"></a>setGAR</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, w5500.o(i.setGAR))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = setGAR &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wiz_write_buf
</UL>
<BR>[Called By]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_dhcp_client
<LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;default_ip_assign
</UL>

<P><STRONG><a name="[201]"></a>setMR</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, w5500.o(i.setMR))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = setMR &rArr; IINCHIP_WRITE &rArr; IINCHIP_SpiSendData &rArr; API_SPI0_Send_Read_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_WRITE
</UL>
<BR>[Called By]<UL><LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;default_ip_update
<LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;default_ip_conflict
</UL>

<P><STRONG><a name="[202]"></a>setSHAR</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, w5500.o(i.setSHAR))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = setSHAR &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wiz_write_buf
</UL>
<BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_w5500_mac
<LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;default_ip_update
<LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;default_ip_conflict
</UL>

<P><STRONG><a name="[1fe]"></a>setSIPR</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, w5500.o(i.setSIPR))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = setSIPR &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wiz_write_buf
</UL>
<BR>[Called By]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_dhcp_client
<LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;default_ip_assign
</UL>

<P><STRONG><a name="[1ff]"></a>setSUBR</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, w5500.o(i.setSUBR))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = setSUBR &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;wiz_write_buf
</UL>
<BR>[Called By]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;init_dhcp_client
<LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;default_ip_assign
</UL>

<P><STRONG><a name="[130]"></a>setSn_IR</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, w5500.o(i.setSn_IR))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = setSn_IR &rArr; IINCHIP_WRITE &rArr; IINCHIP_SpiSendData &rArr; API_SPI0_Send_Read_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_WRITE
</UL>
<BR>[Called By]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_do_tcp_client
</UL>

<P><STRONG><a name="[125]"></a>set_w5500_mac</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, w5500_conf.o(i.set_w5500_mac))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = set_w5500_mac &rArr; setSHAR &rArr; wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setSHAR
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;getSHAR
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Reset
</UL>

<P><STRONG><a name="[12d]"></a>socket</STRONG> (Thumb, 192 bytes, Stack size 24 bytes, socket.o(i.socket))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = socket &rArr; close &rArr; IINCHIP_WRITE &rArr; IINCHIP_SpiSendData &rArr; API_SPI0_Send_Read_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;close
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_WRITE
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_READ
</UL>
<BR>[Called By]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DHCP_run
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_do_tcp_client
</UL>

<P><STRONG><a name="[126]"></a>socket_buf_init</STRONG> (Thumb, 128 bytes, Stack size 24 bytes, w5500.o(i.socket_buf_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = socket_buf_init &rArr; IINCHIP_WRITE &rArr; IINCHIP_SpiSendData &rArr; API_SPI0_Send_Read_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_WRITE
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_Reset
</UL>

<P><STRONG><a name="[12a]"></a>spi_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_enable))
<BR><BR>[Called By]<UL><LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_SPI0_Init
</UL>

<P><STRONG><a name="[122]"></a>spi_i2s_data_receive</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_i2s_data_receive))
<BR><BR>[Called By]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DRV_SPI_SwapByte
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Read_Byte
</UL>

<P><STRONG><a name="[121]"></a>spi_i2s_data_transmit</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_i2s_data_transmit))
<BR><BR>[Called By]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DRV_SPI_SwapByte
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Read_Byte
</UL>

<P><STRONG><a name="[120]"></a>spi_i2s_flag_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_i2s_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_ReadBuffer
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DRV_SPI_SwapByte
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WritePage
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_SendInstruction
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_SPI0_Send_Read_Byte
</UL>

<P><STRONG><a name="[129]"></a>spi_init</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_init))
<BR><BR>[Called By]<UL><LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_W5500_SPI0_Init
</UL>

<P><STRONG><a name="[1a9]"></a>split</STRONG> (Thumb, 82 bytes, Stack size 32 bytes, esp32_wifi.o(i.split))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = split &rArr; strtok
</UL>
<BR>[Calls]<UL><LI><a href="#[22c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strtok
<LI><a href="#[228]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
</UL>
<BR>[Called By]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnUnpackJAPAck
</UL>

<P><STRONG><a name="[214]"></a>systick_config</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, systick.o(i.systick_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = systick_config &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[212]"></a>timer_auto_reload_shadow_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_auto_reload_shadow_enable))
<BR><BR>[Called By]<UL><LI><a href="#[20f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hw_time_set
</UL>

<P><STRONG><a name="[1c3]"></a>timer_channel_output_config</STRONG> (Thumb, 484 bytes, Stack size 8 bytes, gd32f4xx_timer.o(i.timer_channel_output_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = timer_channel_output_config
</UL>
<BR>[Called By]<UL><LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER1_Init
</UL>

<P><STRONG><a name="[1c5]"></a>timer_channel_output_mode_config</STRONG> (Thumb, 90 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_channel_output_mode_config))
<BR><BR>[Called By]<UL><LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER1_Init
</UL>

<P><STRONG><a name="[1c4]"></a>timer_channel_output_pulse_value_config</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_channel_output_pulse_value_config))
<BR><BR>[Called By]<UL><LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER1_Init
</UL>

<P><STRONG><a name="[1c6]"></a>timer_channel_output_shadow_config</STRONG> (Thumb, 90 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_channel_output_shadow_config))
<BR><BR>[Called By]<UL><LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER1_Init
</UL>

<P><STRONG><a name="[1c1]"></a>timer_deinit</STRONG> (Thumb, 374 bytes, Stack size 8 bytes, gd32f4xx_timer.o(i.timer_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = timer_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER6_Init
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER3_Init
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER1_Init
</UL>

<P><STRONG><a name="[210]"></a>timer_disable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_disable))
<BR><BR>[Called By]<UL><LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_timer_irq
<LI><a href="#[20f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hw_time_set
<LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hw_delay
</UL>

<P><STRONG><a name="[1cc]"></a>timer_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_enable))
<BR><BR>[Called By]<UL><LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER6_Init
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER3_Init
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[20f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hw_time_set
</UL>

<P><STRONG><a name="[1c2]"></a>timer_init</STRONG> (Thumb, 122 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_init))
<BR><BR>[Called By]<UL><LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER6_Init
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER3_Init
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER1_Init
<LI><a href="#[20f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hw_time_set
</UL>

<P><STRONG><a name="[211]"></a>timer_interrupt_disable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_interrupt_disable))
<BR><BR>[Called By]<UL><LI><a href="#[20f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hw_time_set
</UL>

<P><STRONG><a name="[1cb]"></a>timer_interrupt_enable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_interrupt_enable))
<BR><BR>[Called By]<UL><LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER6_Init
<LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER3_Init
<LI><a href="#[20f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hw_time_set
</UL>

<P><STRONG><a name="[1ce]"></a>timer_interrupt_flag_clear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_interrupt_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_timer_irq
<LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_PeriodElapsedCallback
<LI><a href="#[20f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hw_time_set
</UL>

<P><STRONG><a name="[1c8]"></a>timer_interrupt_flag_get</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_interrupt_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_timer_irq
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER6_IRQHandler
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER3_IRQHandler
</UL>

<P><STRONG><a name="[11b]"></a>trng_deinit</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, gd32f4xx_trng.o(i.trng_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = trng_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_RNG_Init
</UL>

<P><STRONG><a name="[11c]"></a>trng_enable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_trng.o(i.trng_enable))
<BR><BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_RNG_Init
</UL>

<P><STRONG><a name="[11d]"></a>trng_flag_get</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_trng.o(i.trng_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;API_RNG_Init
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_hard_rand_data
</UL>

<P><STRONG><a name="[20d]"></a>trng_get_true_random_data</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_trng.o(i.trng_get_true_random_data))
<BR><BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_hard_rand_data
</UL>

<P><STRONG><a name="[1d6]"></a>usart_baudrate_set</STRONG> (Thumb, 224 bytes, Stack size 32 bytes, gd32f4xx_usart.o(i.usart_baudrate_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = usart_baudrate_set &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[22f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_clock_freq_get
</UL>
<BR>[Called By]<UL><LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_Init
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Init
</UL>

<P><STRONG><a name="[1d2]"></a>usart_data_receive</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_data_receive))
<BR><BR>[Called By]<UL><LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_RxCpltCallback
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_IDLECallBack
</UL>

<P><STRONG><a name="[206]"></a>usart_data_transmit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_data_transmit))
<BR><BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
</UL>

<P><STRONG><a name="[1d5]"></a>usart_deinit</STRONG> (Thumb, 210 bytes, Stack size 8 bytes, gd32f4xx_usart.o(i.usart_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_Init
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Init
</UL>

<P><STRONG><a name="[1d9]"></a>usart_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_enable))
<BR><BR>[Called By]<UL><LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_Init
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Init
</UL>

<P><STRONG><a name="[207]"></a>usart_flag_get</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, gd32f4xx_usart.o(i.usart_flag_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
</UL>

<P><STRONG><a name="[1dc]"></a>usart_interrupt_enable</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, gd32f4xx_usart.o(i.usart_interrupt_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_interrupt_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_Init
</UL>

<P><STRONG><a name="[1d1]"></a>usart_interrupt_flag_clear</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, gd32f4xx_usart.o(i.usart_interrupt_flag_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_interrupt_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_IDLECallBack
</UL>

<P><STRONG><a name="[1da]"></a>usart_interrupt_flag_get</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, gd32f4xx_usart.o(i.usart_interrupt_flag_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = usart_interrupt_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
</UL>

<P><STRONG><a name="[1d7]"></a>usart_receive_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_receive_config))
<BR><BR>[Called By]<UL><LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_Init
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Init
</UL>

<P><STRONG><a name="[1d8]"></a>usart_transmit_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_usart.o(i.usart_transmit_config))
<BR><BR>[Called By]<UL><LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_Init
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART0_Init
</UL>

<P><STRONG><a name="[230]"></a>usb_basic_init</STRONG> (Thumb, 240 bytes, Stack size 208 bytes, drv_usb_core.o(i.usb_basic_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = usb_basic_init
</UL>
<BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_init
</UL>

<P><STRONG><a name="[1e1]"></a>usb_clock_active</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, drv_usb_dev.o(i.usb_clock_active))
<BR><BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBFS_WKUP_IRQHandler
</UL>

<P><STRONG><a name="[231]"></a>usb_core_init</STRONG> (Thumb, 178 bytes, Stack size 24 bytes, drv_usb_core.o(i.usb_core_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = usb_core_init &rArr; usb_core_reset &rArr; usb_udelay &rArr; hw_delay &rArr; hw_time_set
</UL>
<BR>[Calls]<UL><LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_mdelay
<LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_core_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_init
</UL>

<P><STRONG><a name="[240]"></a>usb_ctlep_startout</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, drv_usb_dev.o(i.usb_ctlep_startout))
<BR><BR>[Called By]<UL><LI><a href="#[241]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctl_status_send
<LI><a href="#[23f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctl_status_recev
<LI><a href="#[244]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_enum_error
<LI><a href="#[251]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_reset
<LI><a href="#[24e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_epout
<LI><a href="#[24c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_epin
</UL>

<P><STRONG><a name="[24b]"></a>usb_curmode_set</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, drv_usb_core.o(i.usb_curmode_set))
<BR><BR>[Called By]<UL><LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_init
</UL>

<P><STRONG><a name="[235]"></a>usb_devcore_init</STRONG> (Thumb, 330 bytes, Stack size 16 bytes, drv_usb_dev.o(i.usb_devcore_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = usb_devcore_init &rArr; usb_txfifo_flush &rArr; usb_udelay &rArr; hw_delay &rArr; hw_time_set
</UL>
<BR>[Calls]<UL><LI><a href="#[239]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_devint_enable
<LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_txfifo_flush
<LI><a href="#[236]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_set_txfifo
<LI><a href="#[238]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_rxfifo_flush
</UL>
<BR>[Called By]<UL><LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_init
</UL>

<P><STRONG><a name="[239]"></a>usb_devint_enable</STRONG> (Thumb, 56 bytes, Stack size 0 bytes, drv_usb_dev.o(i.usb_devint_enable))
<BR><BR>[Called By]<UL><LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_devcore_init
</UL>

<P><STRONG><a name="[215]"></a>usb_gpio_config</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, gd32f4xx_hw.o(i.usb_gpio_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = usb_gpio_config &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[24d]"></a>usb_iepintr_read</STRONG> (Thumb, 42 bytes, Stack size 12 bytes, drv_usb_dev.o(i.usb_iepintr_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = usb_iepintr_read
</UL>
<BR>[Called By]<UL><LI><a href="#[24c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_epin
</UL>

<P><STRONG><a name="[219]"></a>usb_intr_config</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, gd32f4xx_hw.o(i.usb_intr_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = usb_intr_config &rArr; nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_priority_group_set
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[233]"></a>usb_mdelay</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, gd32f4xx_hw.o(i.usb_mdelay))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = usb_mdelay &rArr; hw_delay &rArr; hw_time_set
</UL>
<BR>[Calls]<UL><LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hw_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[242]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_disconnect
<LI><a href="#[23c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_connect
<LI><a href="#[231]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_core_init
</UL>

<P><STRONG><a name="[216]"></a>usb_rcu_config</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, gd32f4xx_hw.o(i.usb_rcu_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usb_rcu_config
</UL>
<BR>[Calls]<UL><LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_pll48m_clock_config
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_ck48m_clock_config
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[238]"></a>usb_rxfifo_flush</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, drv_usb_core.o(i.usb_rxfifo_flush))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = usb_rxfifo_flush &rArr; usb_udelay &rArr; hw_delay &rArr; hw_time_set
</UL>
<BR>[Calls]<UL><LI><a href="#[234]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_udelay
</UL>
<BR>[Called By]<UL><LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_devcore_init
</UL>

<P><STRONG><a name="[253]"></a>usb_rxfifo_read</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, drv_usb_core.o(i.usb_rxfifo_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = usb_rxfifo_read
</UL>
<BR>[Called By]<UL><LI><a href="#[252]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_rxfifo
</UL>

<P><STRONG><a name="[236]"></a>usb_set_txfifo</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, drv_usb_core.o(i.usb_set_txfifo))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = usb_set_txfifo
</UL>
<BR>[Called By]<UL><LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_devcore_init
</UL>

<P><STRONG><a name="[217]"></a>usb_timer_init</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, gd32f4xx_hw.o(i.usb_timer_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = usb_timer_init &rArr; nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_priority_group_set
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1c7]"></a>usb_timer_irq</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, gd32f4xx_hw.o(i.usb_timer_irq))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usb_timer_irq
</UL>
<BR>[Calls]<UL><LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_flag_get
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_flag_clear
<LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER2_IRQHandler
</UL>

<P><STRONG><a name="[247]"></a>usb_transc_active</STRONG> (Thumb, 142 bytes, Stack size 20 bytes, drv_usb_dev.o(i.usb_transc_active))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = usb_transc_active
</UL>
<BR>[Called By]<UL><LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_setup
<LI><a href="#[251]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_reset
</UL>

<P><STRONG><a name="[249]"></a>usb_transc_clrstall</STRONG> (Thumb, 68 bytes, Stack size 8 bytes, drv_usb_dev.o(i.usb_transc_clrstall))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usb_transc_clrstall
</UL>
<BR>[Called By]<UL><LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_stall_clear
</UL>

<P><STRONG><a name="[245]"></a>usb_transc_deactivate</STRONG> (Thumb, 100 bytes, Stack size 12 bytes, drv_usb_dev.o(i.usb_transc_deactivate))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = usb_transc_deactivate
</UL>
<BR>[Called By]<UL><LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_clear
</UL>

<P><STRONG><a name="[23a]"></a>usb_transc_inxfer</STRONG> (Thumb, 268 bytes, Stack size 32 bytes, drv_usb_dev.o(i.usb_transc_inxfer))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = usb_transc_inxfer &rArr; usb_txfifo_write
</UL>
<BR>[Calls]<UL><LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_txfifo_write
</UL>
<BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_send
</UL>

<P><STRONG><a name="[246]"></a>usb_transc_outxfer</STRONG> (Thumb, 144 bytes, Stack size 20 bytes, drv_usb_dev.o(i.usb_transc_outxfer))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = usb_transc_outxfer
</UL>
<BR>[Called By]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_recev
</UL>

<P><STRONG><a name="[248]"></a>usb_transc_stall</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, drv_usb_dev.o(i.usb_transc_stall))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usb_transc_stall
</UL>
<BR>[Called By]<UL><LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_stall
</UL>

<P><STRONG><a name="[237]"></a>usb_txfifo_flush</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, drv_usb_core.o(i.usb_txfifo_flush))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = usb_txfifo_flush &rArr; usb_udelay &rArr; hw_delay &rArr; hw_time_set
</UL>
<BR>[Calls]<UL><LI><a href="#[234]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_udelay
</UL>
<BR>[Called By]<UL><LI><a href="#[251]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_reset
<LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_devcore_init
</UL>

<P><STRONG><a name="[23b]"></a>usb_txfifo_write</STRONG> (Thumb, 34 bytes, Stack size 20 bytes, drv_usb_core.o(i.usb_txfifo_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = usb_txfifo_write
</UL>
<BR>[Called By]<UL><LI><a href="#[243]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_emptytxfifo_write
<LI><a href="#[23a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_transc_inxfer
</UL>

<P><STRONG><a name="[234]"></a>usb_udelay</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, gd32f4xx_hw.o(i.usb_udelay))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = usb_udelay &rArr; hw_delay &rArr; hw_time_set
</UL>
<BR>[Calls]<UL><LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hw_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_txfifo_flush
<LI><a href="#[238]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_rxfifo_flush
<LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_core_reset
</UL>

<P><STRONG><a name="[258]"></a>usbd_class_request</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, usbd_enum.o(i.usbd_class_request))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = usbd_class_request
</UL>
<BR>[Called By]<UL><LI><a href="#[250]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_setup_transc
</UL>

<P><STRONG><a name="[23c]"></a>usbd_connect</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, usbd_core.o(i.usbd_connect))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = usbd_connect &rArr; usb_mdelay &rArr; hw_delay &rArr; hw_time_set
</UL>
<BR>[Calls]<UL><LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_mdelay
</UL>
<BR>[Called By]<UL><LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_init
</UL>

<P><STRONG><a name="[23d]"></a>usbd_ctl_recev</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, usbd_transc.o(i.usbd_ctl_recev))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = usbd_ctl_recev &rArr; usbd_ep_recev &rArr; usb_transc_outxfer
</UL>
<BR>[Calls]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_recev
</UL>
<BR>[Called By]<UL><LI><a href="#[250]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_setup_transc
<LI><a href="#[24f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_out_transc
</UL>

<P><STRONG><a name="[23e]"></a>usbd_ctl_send</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, usbd_transc.o(i.usbd_ctl_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = usbd_ctl_send &rArr; usbd_ep_send &rArr; usb_transc_inxfer &rArr; usb_txfifo_write
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_send
</UL>
<BR>[Called By]<UL><LI><a href="#[250]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_setup_transc
<LI><a href="#[24a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_in_transc
</UL>

<P><STRONG><a name="[23f]"></a>usbd_ctl_status_recev</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, usbd_transc.o(i.usbd_ctl_status_recev))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = usbd_ctl_status_recev &rArr; usbd_ep_recev &rArr; usb_transc_outxfer
</UL>
<BR>[Calls]<UL><LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_recev
<LI><a href="#[240]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_ctlep_startout
</UL>
<BR>[Called By]<UL><LI><a href="#[24a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_in_transc
</UL>

<P><STRONG><a name="[241]"></a>usbd_ctl_status_send</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, usbd_transc.o(i.usbd_ctl_status_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = usbd_ctl_status_send &rArr; usbd_ep_send &rArr; usb_transc_inxfer &rArr; usb_txfifo_write
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_send
<LI><a href="#[240]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_ctlep_startout
</UL>
<BR>[Called By]<UL><LI><a href="#[250]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_setup_transc
<LI><a href="#[24f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_out_transc
</UL>

<P><STRONG><a name="[242]"></a>usbd_disconnect</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, usbd_core.o(i.usbd_disconnect))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = usbd_disconnect &rArr; usb_mdelay &rArr; hw_delay &rArr; hw_time_set
</UL>
<BR>[Calls]<UL><LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_mdelay
</UL>
<BR>[Called By]<UL><LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_init
</UL>

<P><STRONG><a name="[244]"></a>usbd_enum_error</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, usbd_enum.o(i.usbd_enum_error))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = usbd_enum_error &rArr; usbd_ep_stall &rArr; usb_transc_stall
</UL>
<BR>[Calls]<UL><LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_stall
<LI><a href="#[240]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_ctlep_startout
</UL>
<BR>[Called By]<UL><LI><a href="#[250]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_setup_transc
</UL>

<P><STRONG><a name="[1f8]"></a>usbd_ep_clear</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, usbd_core.o(i.usbd_ep_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = usbd_ep_clear &rArr; usb_transc_deactivate
</UL>
<BR>[Calls]<UL><LI><a href="#[245]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_transc_deactivate
</UL>
<BR>[Called By]<UL><LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cdc_acm_deinit
</UL>

<P><STRONG><a name="[1e3]"></a>usbd_ep_recev</STRONG> (Thumb, 60 bytes, Stack size 24 bytes, usbd_core.o(i.usbd_ep_recev))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = usbd_ep_recev &rArr; usb_transc_outxfer
</UL>
<BR>[Calls]<UL><LI><a href="#[246]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_transc_outxfer
</UL>
<BR>[Called By]<UL><LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USB_RunTask
<LI><a href="#[23f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctl_status_recev
<LI><a href="#[23d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctl_recev
</UL>

<P><STRONG><a name="[ec]"></a>usbd_ep_send</STRONG> (Thumb, 60 bytes, Stack size 24 bytes, usbd_core.o(i.usbd_ep_send))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = usbd_ep_send &rArr; usb_transc_inxfer &rArr; usb_txfifo_write
</UL>
<BR>[Calls]<UL><LI><a href="#[23a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_transc_inxfer
</UL>
<BR>[Called By]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CDC_Command
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_RunTask
<LI><a href="#[241]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctl_status_send
<LI><a href="#[23e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctl_send
<LI><a href="#[24a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_in_transc
<LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cdc_acm_in
</UL>

<P><STRONG><a name="[1f9]"></a>usbd_ep_setup</STRONG> (Thumb, 106 bytes, Stack size 24 bytes, usbd_core.o(i.usbd_ep_setup))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = usbd_ep_setup &rArr; usb_transc_active
</UL>
<BR>[Calls]<UL><LI><a href="#[247]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_transc_active
</UL>
<BR>[Called By]<UL><LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cdc_acm_init
</UL>

<P><STRONG><a name="[1f1]"></a>usbd_ep_stall</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, usbd_core.o(i.usbd_ep_stall))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = usbd_ep_stall &rArr; usb_transc_stall
</UL>
<BR>[Calls]<UL><LI><a href="#[248]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_transc_stall
</UL>
<BR>[Called By]<UL><LI><a href="#[244]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_enum_error
<LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_usb_std_setfeature
</UL>

<P><STRONG><a name="[1ef]"></a>usbd_ep_stall_clear</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, usbd_core.o(i.usbd_ep_stall_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = usbd_ep_stall_clear &rArr; usb_transc_clrstall
</UL>
<BR>[Calls]<UL><LI><a href="#[249]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_transc_clrstall
</UL>
<BR>[Called By]<UL><LI><a href="#[15]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_usb_std_clearfeature
</UL>

<P><STRONG><a name="[24a]"></a>usbd_in_transc</STRONG> (Thumb, 160 bytes, Stack size 16 bytes, usbd_transc.o(i.usbd_in_transc))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = usbd_in_transc &rArr; usbd_ctl_send &rArr; usbd_ep_send &rArr; usb_transc_inxfer &rArr; usb_txfifo_write
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_send
<LI><a href="#[23f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctl_status_recev
<LI><a href="#[23e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctl_send
</UL>
<BR>[Called By]<UL><LI><a href="#[24c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_epin
</UL>

<P><STRONG><a name="[218]"></a>usbd_init</STRONG> (Thumb, 124 bytes, Stack size 24 bytes, usbd_core.o(i.usbd_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 232<LI>Call Chain = usbd_init &rArr; usb_basic_init
</UL>
<BR>[Calls]<UL><LI><a href="#[22a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;serial_string_get
<LI><a href="#[242]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_disconnect
<LI><a href="#[23c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_connect
<LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_devcore_init
<LI><a href="#[24b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_curmode_set
<LI><a href="#[231]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_core_init
<LI><a href="#[230]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_basic_init
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1dd]"></a>usbd_isr</STRONG> (Thumb, 222 bytes, Stack size 16 bytes, drv_usbd_int.o(i.usbd_isr))
<BR><BR>[Stack]<UL><LI>Max Depth = 156<LI>Call Chain = usbd_isr &rArr; usbd_int_epout &rArr; usbd_setup_transc &rArr; usbd_ctl_send &rArr; usbd_ep_send &rArr; usb_transc_inxfer &rArr; usb_txfifo_write
</UL>
<BR>[Calls]<UL><LI><a href="#[254]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_suspend
<LI><a href="#[252]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_rxfifo
<LI><a href="#[251]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_reset
<LI><a href="#[24e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_epout
<LI><a href="#[24c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_epin
<LI><a href="#[256]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_enumfinish
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBFS_IRQHandler
</UL>

<P><STRONG><a name="[24f]"></a>usbd_out_transc</STRONG> (Thumb, 132 bytes, Stack size 16 bytes, usbd_transc.o(i.usbd_out_transc))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = usbd_out_transc &rArr; usbd_ctl_status_send &rArr; usbd_ep_send &rArr; usb_transc_inxfer &rArr; usb_txfifo_write
</UL>
<BR>[Calls]<UL><LI><a href="#[241]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctl_status_send
<LI><a href="#[23d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctl_recev
</UL>
<BR>[Called By]<UL><LI><a href="#[24e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_epout
</UL>

<P><STRONG><a name="[250]"></a>usbd_setup_transc</STRONG> (Thumb, 132 bytes, Stack size 24 bytes, usbd_transc.o(i.usbd_setup_transc))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = usbd_setup_transc &rArr; usbd_ctl_send &rArr; usbd_ep_send &rArr; usb_transc_inxfer &rArr; usb_txfifo_write
</UL>
<BR>[Calls]<UL><LI><a href="#[241]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctl_status_send
<LI><a href="#[23e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctl_send
<LI><a href="#[23d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ctl_recev
<LI><a href="#[259]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_vendor_request
<LI><a href="#[257]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_standard_request
<LI><a href="#[244]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_enum_error
<LI><a href="#[258]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_class_request
</UL>
<BR>[Called By]<UL><LI><a href="#[24e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_epout
</UL>

<P><STRONG><a name="[257]"></a>usbd_standard_request</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, usbd_enum.o(i.usbd_standard_request))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = usbd_standard_request
</UL>
<BR>[Called By]<UL><LI><a href="#[250]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_setup_transc
</UL>

<P><STRONG><a name="[259]"></a>usbd_vendor_request</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, usbd_enum.o(i.usbd_vendor_request))
<BR><BR>[Called By]<UL><LI><a href="#[250]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_setup_transc
</UL>

<P><STRONG><a name="[17b]"></a>week_str2num</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, esp32_wifi.o(i.week_str2num))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = week_str2num &rArr; __2snprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP_GetSntpTimeInfo
</UL>

<P><STRONG><a name="[20b]"></a>wiz_read_buf</STRONG> (Thumb, 78 bytes, Stack size 24 bytes, w5500_conf.o(i.wiz_read_buf))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = wiz_read_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;iinchip_cson
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;iinchip_csoff
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_SpiSendData
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;getSHAR
<LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recv_data_processing
<LI><a href="#[21f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;recvfrom
</UL>

<P><STRONG><a name="[229]"></a>wiz_write_buf</STRONG> (Thumb, 80 bytes, Stack size 24 bytes, w5500_conf.o(i.wiz_write_buf))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = wiz_write_buf &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;iinchip_cson
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;iinchip_csoff
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IINCHIP_SpiSendData
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setSUBR
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setSIPR
<LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setSHAR
<LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setGAR
<LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;send_data_processing
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[22d]"></a>system_clock_168m_25m_hxtal</STRONG> (Thumb, 240 bytes, Stack size 0 bytes, system_gd32f4xx.o(i.system_clock_168m_25m_hxtal))
<BR><BR>[Called By]<UL><LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_config
</UL>

<P><STRONG><a name="[1bf]"></a>system_clock_config</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, system_gd32f4xx.o(i.system_clock_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = system_clock_config
</UL>
<BR>[Calls]<UL><LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_168m_25m_hxtal
</UL>
<BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
</UL>

<P><STRONG><a name="[1de]"></a>resume_mcu_clk</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, gd32f4xx_it.o(i.resume_mcu_clk))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = resume_mcu_clk
</UL>
<BR>[Calls]<UL><LI><a href="#[223]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_system_clock_source_get
<LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_system_clock_source_config
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_osci_on
<LI><a href="#[220]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USBFS_WKUP_IRQHandler
</UL>

<P><STRONG><a name="[22e]"></a>__NVIC_SetPriority</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, systick.o(i.__NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;systick_config
</UL>

<P><STRONG><a name="[16c]"></a>EEPROM_WritePage</STRONG> (Thumb, 730 bytes, Stack size 72 bytes, 25lc080a.o(i.EEPROM_WritePage))
<BR><BR>[Stack]<UL><LI>Max Depth = 264<LI>Call Chain = EEPROM_WritePage &rArr; USER_EEPROM_WriteByte &rArr; EEPROM_SPI_WriteBuffer &rArr; EEPROM_SPI_WritePage &rArr; EEPROM_SPI_WaitStandbyState &rArr; EEPROM_SPI_SendInstruction &rArr; DRV_SPI_SwapByte
</UL>
<BR>[Calls]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_WriteBuffer
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EEPROM_SPI_ReadBuffer
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16_USB
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USER_EEPROM_WriteByte
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USER_EEPROM_RedeByte
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;malloc
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;free
</UL>
<BR>[Called By]<UL><LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USER_EEPROM_WritePage
</UL>

<P><STRONG><a name="[176]"></a>AddCmdTaskToList</STRONG> (Thumb, 122 bytes, Stack size 8 bytes, esp32_wifi.o(i.AddCmdTaskToList))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = AddCmdTaskToList
</UL>
<BR>[Called By]<UL><LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP_CloseWifiConnect
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP_CloseNetConnect
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP_BLEADVSTOP
</UL>

<P><STRONG><a name="[175]"></a>CheckCmdRepeatInList</STRONG> (Thumb, 144 bytes, Stack size 12 bytes, esp32_wifi.o(i.CheckCmdRepeatInList))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = CheckCmdRepeatInList
</UL>
<BR>[Called By]<UL><LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP_CloseWifiConnect
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP_CloseNetConnect
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP_BLEADVSTOP
</UL>

<P><STRONG><a name="[1aa]"></a>ConversionSignalIntensity</STRONG> (Thumb, 170 bytes, Stack size 0 bytes, esp32_wifi.o(i.ConversionSignalIntensity))
<BR><BR>[Called By]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnUnpackJAPAck
</UL>

<P><STRONG><a name="[1a8]"></a>DeletCurrCmdTaskFromList</STRONG> (Thumb, 76 bytes, Stack size 0 bytes, esp32_wifi.o(i.DeletCurrCmdTaskFromList))
<BR><BR>[Called By]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnUnpackyNetConnectStatusAck
<LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnUnpackVERSIONAck
<LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnUnpackTCPOrUDPDataSendAck
<LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnUnpackTCPOrUDPDataCmdAck
<LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnUnpackSntpTimeAck
<LI><a href="#[0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnUnpackRSTAck
<LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnUnpackJAPAck
<LI><a href="#[11]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnUnpackDomainIPAck
<LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnUnpackCWModeAck
<LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnUnpackBuildTCPOrUDPAck
</UL>

<P><STRONG><a name="[179]"></a>ESP_GetSntpTimeInfo</STRONG> (Thumb, 588 bytes, Stack size 56 bytes, esp32_wifi.o(i.ESP_GetSntpTimeInfo))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = ESP_GetSntpTimeInfo &rArr; rtc_register_set &rArr; rtc_init &rArr; rtc_register_sync_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_current_time_get
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_register_set
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;week_str2num
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;month_str2num
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strstr
</UL>
<BR>[Called By]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnUnpackSntpTimeAck
</UL>

<P><STRONG><a name="[13]"></a>OnPackBLEADVDATA_Cmd</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, esp32_wifi.o(i.OnPackBLEADVDATA_Cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = OnPackBLEADVDATA_Cmd &rArr; __2snprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> esp32_wifi.o(.data)
</UL>
<P><STRONG><a name="[12]"></a>OnPackBLENAME_Cmd</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, esp32_wifi.o(i.OnPackBLENAME_Cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = OnPackBLENAME_Cmd &rArr; __2snprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> esp32_wifi.o(.data)
</UL>
<P><STRONG><a name="[9]"></a>OnPackBuildTCPOrUDPCmd</STRONG> (Thumb, 180 bytes, Stack size 48 bytes, esp32_wifi.o(i.OnPackBuildTCPOrUDPCmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = OnPackBuildTCPOrUDPCmd &rArr; __2snprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> esp32_wifi.o(.data)
</UL>
<P><STRONG><a name="[4]"></a>OnPackCWModeCmd</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, esp32_wifi.o(i.OnPackCWModeCmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = OnPackCWModeCmd &rArr; __2snprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> esp32_wifi.o(.data)
</UL>
<P><STRONG><a name="[6]"></a>OnPackJAPCmd</STRONG> (Thumb, 68 bytes, Stack size 24 bytes, esp32_wifi.o(i.OnPackJAPCmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = OnPackJAPCmd &rArr; __2snprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> esp32_wifi.o(.data)
</UL>
<P><STRONG><a name="[2]"></a>OnPackRFPOWERCmd</STRONG> (Thumb, 76 bytes, Stack size 32 bytes, esp32_wifi.o(i.OnPackRFPOWERCmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = OnPackRFPOWERCmd &rArr; __2snprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> esp32_wifi.o(.data)
</UL>
<P><STRONG><a name="[f]"></a>OnPackSetSNTPCFG_Cmd</STRONG> (Thumb, 80 bytes, Stack size 32 bytes, esp32_wifi.o(i.OnPackSetSNTPCFG_Cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = OnPackSetSNTPCFG_Cmd &rArr; __2snprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> esp32_wifi.o(.data)
</UL>
<P><STRONG><a name="[d]"></a>OnPackTCPOrUDPData</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, esp32_wifi.o(i.OnPackTCPOrUDPData))
<BR>[Address Reference Count : 1]<UL><LI> esp32_wifi.o(.data)
</UL>
<P><STRONG><a name="[b]"></a>OnPackTCPOrUDPDataCmd</STRONG> (Thumb, 68 bytes, Stack size 24 bytes, esp32_wifi.o(i.OnPackTCPOrUDPDataCmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = OnPackTCPOrUDPDataCmd &rArr; __2snprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> esp32_wifi.o(.data)
</UL>
<P><STRONG><a name="[a]"></a>OnUnpackBuildTCPOrUDPAck</STRONG> (Thumb, 186 bytes, Stack size 16 bytes, esp32_wifi.o(i.OnUnpackBuildTCPOrUDPAck))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = OnUnpackBuildTCPOrUDPAck &rArr; strstr
</UL>
<BR>[Calls]<UL><LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadCmdTaskCallFunFromList
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DeletCurrCmdTaskFromList
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strstr
</UL>
<BR>[Address Reference Count : 1]<UL><LI> esp32_wifi.o(.data)
</UL>
<P><STRONG><a name="[5]"></a>OnUnpackCWModeAck</STRONG> (Thumb, 158 bytes, Stack size 24 bytes, esp32_wifi.o(i.OnUnpackCWModeAck))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = OnUnpackCWModeAck &rArr; atoi &rArr; strtol &rArr; _strtoul
</UL>
<BR>[Calls]<UL><LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadCmdTaskCallFunFromList
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DeletCurrCmdTaskFromList
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strstr
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atoi
</UL>
<BR>[Address Reference Count : 1]<UL><LI> esp32_wifi.o(.data)
</UL>
<P><STRONG><a name="[11]"></a>OnUnpackDomainIPAck</STRONG> (Thumb, 176 bytes, Stack size 24 bytes, esp32_wifi.o(i.OnUnpackDomainIPAck))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = OnUnpackDomainIPAck &rArr; strstr
</UL>
<BR>[Calls]<UL><LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadCmdTaskCallFunFromList
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DeletCurrCmdTaskFromList
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strstr
</UL>
<BR>[Address Reference Count : 1]<UL><LI> esp32_wifi.o(.data)
</UL>
<P><STRONG><a name="[7]"></a>OnUnpackJAPAck</STRONG> (Thumb, 296 bytes, Stack size 64 bytes, esp32_wifi.o(i.OnUnpackJAPAck))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = OnUnpackJAPAck &rArr; atoi &rArr; strtol &rArr; _strtoul
</UL>
<BR>[Calls]<UL><LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;split
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadCmdTaskCallFunFromList
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DeletCurrCmdTaskFromList
<LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ConversionSignalIntensity
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strstr
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atoi
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> esp32_wifi.o(.data)
</UL>
<P><STRONG><a name="[0]"></a>OnUnpackRSTAck</STRONG> (Thumb, 132 bytes, Stack size 16 bytes, esp32_wifi.o(i.OnUnpackRSTAck))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = OnUnpackRSTAck &rArr; strstr
</UL>
<BR>[Calls]<UL><LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadCmdTaskCallFunFromList
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DeletCurrCmdTaskFromList
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strstr
</UL>
<BR>[Address Reference Count : 1]<UL><LI> esp32_wifi.o(.data)
</UL>
<P><STRONG><a name="[10]"></a>OnUnpackSntpTimeAck</STRONG> (Thumb, 136 bytes, Stack size 16 bytes, esp32_wifi.o(i.OnUnpackSntpTimeAck))
<BR><BR>[Stack]<UL><LI>Max Depth = 132<LI>Call Chain = OnUnpackSntpTimeAck &rArr; ESP_GetSntpTimeInfo &rArr; rtc_register_set &rArr; rtc_init &rArr; rtc_register_sync_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadCmdTaskCallFunFromList
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESP_GetSntpTimeInfo
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DeletCurrCmdTaskFromList
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strstr
</UL>
<BR>[Address Reference Count : 1]<UL><LI> esp32_wifi.o(.data)
</UL>
<P><STRONG><a name="[c]"></a>OnUnpackTCPOrUDPDataCmdAck</STRONG> (Thumb, 128 bytes, Stack size 16 bytes, esp32_wifi.o(i.OnUnpackTCPOrUDPDataCmdAck))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = OnUnpackTCPOrUDPDataCmdAck &rArr; strstr
</UL>
<BR>[Calls]<UL><LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadCmdTaskCallFunFromList
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DeletCurrCmdTaskFromList
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strstr
</UL>
<BR>[Address Reference Count : 1]<UL><LI> esp32_wifi.o(.data)
</UL>
<P><STRONG><a name="[e]"></a>OnUnpackTCPOrUDPDataSendAck</STRONG> (Thumb, 142 bytes, Stack size 16 bytes, esp32_wifi.o(i.OnUnpackTCPOrUDPDataSendAck))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = OnUnpackTCPOrUDPDataSendAck &rArr; strstr
</UL>
<BR>[Calls]<UL><LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadCmdTaskCallFunFromList
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DeletCurrCmdTaskFromList
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strstr
</UL>
<BR>[Address Reference Count : 1]<UL><LI> esp32_wifi.o(.data)
</UL>
<P><STRONG><a name="[1]"></a>OnUnpackVERSIONAck</STRONG> (Thumb, 188 bytes, Stack size 24 bytes, esp32_wifi.o(i.OnUnpackVERSIONAck))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = OnUnpackVERSIONAck &rArr; strstr
</UL>
<BR>[Calls]<UL><LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadCmdTaskCallFunFromList
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DeletCurrCmdTaskFromList
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strstr
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Address Reference Count : 1]<UL><LI> esp32_wifi.o(.data)
</UL>
<P><STRONG><a name="[8]"></a>OnUnpackyNetConnectStatusAck</STRONG> (Thumb, 228 bytes, Stack size 24 bytes, esp32_wifi.o(i.OnUnpackyNetConnectStatusAck))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = OnUnpackyNetConnectStatusAck &rArr; strstr
</UL>
<BR>[Calls]<UL><LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadCmdTaskCallFunFromList
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DeletCurrCmdTaskFromList
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strstr
</UL>
<BR>[Address Reference Count : 1]<UL><LI> esp32_wifi.o(.data)
</UL>
<P><STRONG><a name="[1a7]"></a>ReadCmdTaskCallFunFromList</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, esp32_wifi.o(i.ReadCmdTaskCallFunFromList))
<BR><BR>[Called By]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnUnpackyNetConnectStatusAck
<LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnUnpackVERSIONAck
<LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnUnpackTCPOrUDPDataSendAck
<LI><a href="#[c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnUnpackTCPOrUDPDataCmdAck
<LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnUnpackSntpTimeAck
<LI><a href="#[0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnUnpackRSTAck
<LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnUnpackJAPAck
<LI><a href="#[11]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnUnpackDomainIPAck
<LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnUnpackCWModeAck
<LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnUnpackBuildTCPOrUDPAck
</UL>

<P><STRONG><a name="[3]"></a>XOnPackRFPOWERCmd</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, esp32_wifi.o(i.XOnPackRFPOWERCmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = XOnPackRFPOWERCmd &rArr; __2snprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> esp32_wifi.o(.data)
</UL>
<P><STRONG><a name="[146]"></a>InvertUint16</STRONG> (Thumb, 58 bytes, Stack size 20 bytes, my_crc.o(i.InvertUint16))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = InvertUint16
</UL>
<BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16_USB
</UL>

<P><STRONG><a name="[145]"></a>InvertUint8</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, my_crc.o(i.InvertUint8))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = InvertUint8
</UL>
<BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16_USB
</UL>

<P><STRONG><a name="[1b8]"></a>AddByteToBuffer</STRONG> (Thumb, 110 bytes, Stack size 8 bytes, usart.o(i.AddByteToBuffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = AddByteToBuffer
</UL>
<BR>[Called By]<UL><LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RecvDataHandler
</UL>

<P><STRONG><a name="[184]"></a>ReadBytesToBuffer</STRONG> (Thumb, 142 bytes, Stack size 24 bytes, usart.o(i.ReadBytesToBuffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = ReadBytesToBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FML_USART_RecvTask
</UL>

<P><STRONG><a name="[1b7]"></a>RecvDataHandler</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, usart.o(i.RecvDataHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = RecvDataHandler &rArr; AddByteToBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AddByteToBuffer
</UL>
<BR>[Called By]<UL><LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_RxCpltCallback
</UL>

<P><STRONG><a name="[1bb]"></a>figure</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, user_step.o(i.figure))
<BR><BR>[Called By]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Sever_Buf2Info
</UL>

<P><STRONG><a name="[232]"></a>usb_core_reset</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, drv_usb_core.o(i.usb_core_reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = usb_core_reset &rArr; usb_udelay &rArr; hw_delay &rArr; hw_time_set
</UL>
<BR>[Calls]<UL><LI><a href="#[234]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_udelay
</UL>
<BR>[Called By]<UL><LI><a href="#[231]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_core_init
</UL>

<P><STRONG><a name="[243]"></a>usbd_emptytxfifo_write</STRONG> (Thumb, 140 bytes, Stack size 24 bytes, drv_usbd_int.o(i.usbd_emptytxfifo_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = usbd_emptytxfifo_write &rArr; usb_txfifo_write
</UL>
<BR>[Calls]<UL><LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_txfifo_write
</UL>
<BR>[Called By]<UL><LI><a href="#[24c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_int_epin
</UL>

<P><STRONG><a name="[256]"></a>usbd_int_enumfinish</STRONG> (Thumb, 98 bytes, Stack size 0 bytes, drv_usbd_int.o(i.usbd_int_enumfinish))
<BR><BR>[Called By]<UL><LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_isr
</UL>

<P><STRONG><a name="[24c]"></a>usbd_int_epin</STRONG> (Thumb, 138 bytes, Stack size 24 bytes, drv_usbd_int.o(i.usbd_int_epin))
<BR><BR>[Stack]<UL><LI>Max Depth = 132<LI>Call Chain = usbd_int_epin &rArr; usbd_in_transc &rArr; usbd_ctl_send &rArr; usbd_ep_send &rArr; usb_transc_inxfer &rArr; usb_txfifo_write
</UL>
<BR>[Calls]<UL><LI><a href="#[24a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_in_transc
<LI><a href="#[243]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_emptytxfifo_write
<LI><a href="#[24d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_iepintr_read
<LI><a href="#[240]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_ctlep_startout
</UL>
<BR>[Called By]<UL><LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_isr
</UL>

<P><STRONG><a name="[24e]"></a>usbd_int_epout</STRONG> (Thumb, 206 bytes, Stack size 24 bytes, drv_usbd_int.o(i.usbd_int_epout))
<BR><BR>[Stack]<UL><LI>Max Depth = 140<LI>Call Chain = usbd_int_epout &rArr; usbd_setup_transc &rArr; usbd_ctl_send &rArr; usbd_ep_send &rArr; usb_transc_inxfer &rArr; usb_txfifo_write
</UL>
<BR>[Calls]<UL><LI><a href="#[250]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_setup_transc
<LI><a href="#[24f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_out_transc
<LI><a href="#[240]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_ctlep_startout
</UL>
<BR>[Called By]<UL><LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_isr
</UL>

<P><STRONG><a name="[251]"></a>usbd_int_reset</STRONG> (Thumb, 208 bytes, Stack size 72 bytes, drv_usbd_int.o(i.usbd_int_reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = usbd_int_reset &rArr; usb_txfifo_flush &rArr; usb_udelay &rArr; hw_delay &rArr; hw_time_set
</UL>
<BR>[Calls]<UL><LI><a href="#[247]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_transc_active
<LI><a href="#[240]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_ctlep_startout
<LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_txfifo_flush
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_isr
</UL>

<P><STRONG><a name="[252]"></a>usbd_int_rxfifo</STRONG> (Thumb, 182 bytes, Stack size 32 bytes, drv_usbd_int.o(i.usbd_int_rxfifo))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = usbd_int_rxfifo &rArr; usb_rxfifo_read
</UL>
<BR>[Calls]<UL><LI><a href="#[253]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_rxfifo_read
</UL>
<BR>[Called By]<UL><LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_isr
</UL>

<P><STRONG><a name="[254]"></a>usbd_int_suspend</STRONG> (Thumb, 104 bytes, Stack size 24 bytes, drv_usbd_int.o(i.usbd_int_suspend))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = usbd_int_suspend &rArr; pmu_to_deepsleepmode
</UL>
<BR>[Calls]<UL><LI><a href="#[255]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pmu_to_deepsleepmode
</UL>
<BR>[Called By]<UL><LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_isr
</UL>

<P><STRONG><a name="[1f0]"></a>_usb_bos_desc_get</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, usbd_enum.o(i._usb_bos_desc_get))
<BR><BR>[Called By]<UL><LI><a href="#[19]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_usb_std_getdescriptor
</UL>

<P><STRONG><a name="[21]"></a>_usb_config_desc_get</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, usbd_enum.o(i._usb_config_desc_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _usb_config_desc_get
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_enum.o(.data)
</UL>
<P><STRONG><a name="[20]"></a>_usb_dev_desc_get</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, usbd_enum.o(i._usb_dev_desc_get))
<BR>[Address Reference Count : 1]<UL><LI> usbd_enum.o(.data)
</UL>
<P><STRONG><a name="[15]"></a>_usb_std_clearfeature</STRONG> (Thumb, 116 bytes, Stack size 16 bytes, usbd_enum.o(i._usb_std_clearfeature))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _usb_std_clearfeature &rArr; usbd_ep_stall_clear &rArr; usb_transc_clrstall
</UL>
<BR>[Calls]<UL><LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_stall_clear
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_enum.o(.data)
</UL>
<P><STRONG><a name="[1b]"></a>_usb_std_getconfiguration</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, usbd_enum.o(i._usb_std_getconfiguration))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _usb_std_getconfiguration
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_enum.o(.data)
</UL>
<P><STRONG><a name="[19]"></a>_usb_std_getdescriptor</STRONG> (Thumb, 272 bytes, Stack size 32 bytes, usbd_enum.o(i._usb_std_getdescriptor))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = _usb_std_getdescriptor
</UL>
<BR>[Calls]<UL><LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_usb_bos_desc_get
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_enum.o(.data)
</UL>
<P><STRONG><a name="[1d]"></a>_usb_std_getinterface</STRONG> (Thumb, 60 bytes, Stack size 0 bytes, usbd_enum.o(i._usb_std_getinterface))
<BR>[Address Reference Count : 1]<UL><LI> usbd_enum.o(.data)
</UL>
<P><STRONG><a name="[14]"></a>_usb_std_getstatus</STRONG> (Thumb, 192 bytes, Stack size 16 bytes, usbd_enum.o(i._usb_std_getstatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _usb_std_getstatus
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_enum.o(.data)
</UL>
<P><STRONG><a name="[16]"></a>_usb_std_reserved</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, usbd_enum.o(i._usb_std_reserved))
<BR>[Address Reference Count : 1]<UL><LI> usbd_enum.o(.data)
</UL>
<P><STRONG><a name="[18]"></a>_usb_std_setaddress</STRONG> (Thumb, 92 bytes, Stack size 8 bytes, usbd_enum.o(i._usb_std_setaddress))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _usb_std_setaddress
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_enum.o(.data)
</UL>
<P><STRONG><a name="[1c]"></a>_usb_std_setconfiguration</STRONG> (Thumb, 176 bytes, Stack size 16 bytes, usbd_enum.o(i._usb_std_setconfiguration))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _usb_std_setconfiguration
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_enum.o(.data)
</UL>
<P><STRONG><a name="[1a]"></a>_usb_std_setdescriptor</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, usbd_enum.o(i._usb_std_setdescriptor))
<BR>[Address Reference Count : 1]<UL><LI> usbd_enum.o(.data)
</UL>
<P><STRONG><a name="[17]"></a>_usb_std_setfeature</STRONG> (Thumb, 104 bytes, Stack size 16 bytes, usbd_enum.o(i._usb_std_setfeature))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _usb_std_setfeature &rArr; usbd_ep_stall &rArr; usb_transc_stall
</UL>
<BR>[Calls]<UL><LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_stall
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_enum.o(.data)
</UL>
<P><STRONG><a name="[1e]"></a>_usb_std_setinterface</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, usbd_enum.o(i._usb_std_setinterface))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _usb_std_setinterface
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_enum.o(.data)
</UL>
<P><STRONG><a name="[1f]"></a>_usb_std_synchframe</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, usbd_enum.o(i._usb_std_synchframe))
<BR>[Address Reference Count : 1]<UL><LI> usbd_enum.o(.data)
</UL>
<P><STRONG><a name="[22]"></a>_usb_str_desc_get</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, usbd_enum.o(i._usb_str_desc_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _usb_str_desc_get
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_enum.o(.data)
</UL>
<P><STRONG><a name="[20e]"></a>hw_delay</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, gd32f4xx_hw.o(i.hw_delay))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = hw_delay &rArr; hw_time_set
</UL>
<BR>[Calls]<UL><LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_disable
<LI><a href="#[20f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hw_time_set
</UL>
<BR>[Called By]<UL><LI><a href="#[234]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_udelay
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_mdelay
</UL>

<P><STRONG><a name="[20f]"></a>hw_time_set</STRONG> (Thumb, 104 bytes, Stack size 24 bytes, gd32f4xx_hw.o(i.hw_time_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = hw_time_set
</UL>
<BR>[Calls]<UL><LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_flag_clear
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_enable
<LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_disable
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_init
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_enable
<LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_disable
<LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_auto_reload_shadow_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;hw_delay
</UL>

<P><STRONG><a name="[24]"></a>cdc_acm_deinit</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, cdc_acm_core.o(i.cdc_acm_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = cdc_acm_deinit &rArr; usbd_ep_clear &rArr; usb_transc_deactivate
</UL>
<BR>[Calls]<UL><LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_clear
</UL>
<BR>[Address Reference Count : 1]<UL><LI> cdc_acm_core.o(.data)
</UL>
<P><STRONG><a name="[27]"></a>cdc_acm_in</STRONG> (Thumb, 70 bytes, Stack size 24 bytes, cdc_acm_core.o(i.cdc_acm_in))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = cdc_acm_in &rArr; usbd_ep_send &rArr; usb_transc_inxfer &rArr; usb_txfifo_write
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_send
</UL>
<BR>[Address Reference Count : 1]<UL><LI> cdc_acm_core.o(.data)
</UL>
<P><STRONG><a name="[23]"></a>cdc_acm_init</STRONG> (Thumb, 84 bytes, Stack size 24 bytes, cdc_acm_core.o(i.cdc_acm_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = cdc_acm_init &rArr; usbd_ep_setup &rArr; usb_transc_active
</UL>
<BR>[Calls]<UL><LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_setup
</UL>
<BR>[Address Reference Count : 1]<UL><LI> cdc_acm_core.o(.data)
</UL>
<P><STRONG><a name="[28]"></a>cdc_acm_out</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, cdc_acm_core.o(i.cdc_acm_out))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = cdc_acm_out
</UL>
<BR>[Address Reference Count : 1]<UL><LI> cdc_acm_core.o(.data)
</UL>
<P><STRONG><a name="[25]"></a>cdc_acm_req</STRONG> (Thumb, 176 bytes, Stack size 12 bytes, cdc_acm_core.o(i.cdc_acm_req))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = cdc_acm_req
</UL>
<BR>[Address Reference Count : 1]<UL><LI> cdc_acm_core.o(.data)
</UL>
<P><STRONG><a name="[26]"></a>cdc_ctlx_out</STRONG> (Thumb, 82 bytes, Stack size 0 bytes, cdc_acm_core.o(i.cdc_ctlx_out))
<BR>[Address Reference Count : 1]<UL><LI> cdc_acm_core.o(.data)
</UL>
<P><STRONG><a name="[c3]"></a>arm_rfft_1024_fast_init_f32</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, arm_rfft_fast_init_f32.o(.text.arm_rfft_1024_fast_init_f32), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cfft_init_f32
</UL>

<P><STRONG><a name="[c5]"></a>arm_rfft_128_fast_init_f32</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, arm_rfft_fast_init_f32.o(.text.arm_rfft_128_fast_init_f32), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cfft_init_f32
</UL>

<P><STRONG><a name="[c6]"></a>arm_rfft_2048_fast_init_f32</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, arm_rfft_fast_init_f32.o(.text.arm_rfft_2048_fast_init_f32), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cfft_init_f32
</UL>

<P><STRONG><a name="[c7]"></a>arm_rfft_256_fast_init_f32</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, arm_rfft_fast_init_f32.o(.text.arm_rfft_256_fast_init_f32), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cfft_init_f32
</UL>

<P><STRONG><a name="[c8]"></a>arm_rfft_32_fast_init_f32</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, arm_rfft_fast_init_f32.o(.text.arm_rfft_32_fast_init_f32), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cfft_init_f32
</UL>

<P><STRONG><a name="[c9]"></a>arm_rfft_4096_fast_init_f32</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, arm_rfft_fast_init_f32.o(.text.arm_rfft_4096_fast_init_f32), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cfft_init_f32
</UL>

<P><STRONG><a name="[ca]"></a>arm_rfft_512_fast_init_f32</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, arm_rfft_fast_init_f32.o(.text.arm_rfft_512_fast_init_f32), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cfft_init_f32
</UL>

<P><STRONG><a name="[cb]"></a>arm_rfft_64_fast_init_f32</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, arm_rfft_fast_init_f32.o(.text.arm_rfft_64_fast_init_f32), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_cfft_init_f32
</UL>

<P><STRONG><a name="[bf]"></a>arm_heapify</STRONG> (Thumb, 124 bytes, Stack size 16 bytes, arm_heap_sort_f32.o(.text.arm_heapify))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = arm_heapify
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_heap_sort_f32
</UL>

<P><STRONG><a name="[c1]"></a>arm_quick_sort_core_f32</STRONG> (Thumb, 202 bytes, Stack size 32 bytes, arm_quick_sort_f32.o(.text.arm_quick_sort_core_f32))
<BR><BR>[Stack]<UL><LI>Max Depth = 32 + In Cycle
<LI>Call Chain = arm_quick_sort_core_f32 &rArr;  arm_quick_sort_core_f32 (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_quick_sort_core_f32
</UL>
<BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_quick_sort_core_f32
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;arm_quick_sort_f32
</UL>

<P><STRONG><a name="[1eb]"></a>_fp_digits</STRONG> (Thumb, 366 bytes, Stack size 64 bytes, printfa.o(i._fp_digits), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>
<BR>[Called By]<UL><LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[1e7]"></a>_printf_core</STRONG> (Thumb, 1744 bytes, Stack size 136 bytes, printfa.o(i._printf_core), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>
<BR>[Called By]<UL><LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf
<LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0snprintf
<LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0printf
</UL>

<P><STRONG><a name="[1ed]"></a>_printf_post_padding</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, printfa.o(i._printf_post_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[1ec]"></a>_printf_pre_padding</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, printfa.o(i._printf_pre_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[95]"></a>_snputc</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, printfa.o(i._snputc))
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0snprintf)
</UL>
<P><STRONG><a name="[96]"></a>_sputc</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, printfa.o(i._sputc))
<BR><BR>[Called By]<UL><LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0sprintf)
</UL><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
