Dependencies for Project 'boot', Target 'Target 1': (DO NOT MODIFY !)
CompilerVersion: 5060960::V5.06 update 7 (build 960)::.\ARMCC
F (.\Firmware\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s)(0x68947F4E)(--cpu Cortex-M4.fp.sp -g --apcs=interwork --pd "__MICROLIB SETA 1"

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

--pd "__UVISION_VERSION SETA 533" --pd "_RTE_ SETA 1" --pd "GD32F470 SETA 1" --pd "_RTE_ SETA 1"

--list .\listings\startup_gd32f450_470.lst --xref -o .\objects\startup_gd32f450_470.o --depend .\objects\startup_gd32f450_470.d)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_adc.c)(0x68947F4E)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_adc.o --omf_browse .\objects\gd32f4xx_adc.crf --depend .\objects\gd32f4xx_adc.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_can.c)(0x68947F4E)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_can.o --omf_browse .\objects\gd32f4xx_can.crf --depend .\objects\gd32f4xx_can.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_crc.c)(0x68947F4E)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_crc.o --omf_browse .\objects\gd32f4xx_crc.crf --depend .\objects\gd32f4xx_crc.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_ctc.c)(0x68947F4E)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_ctc.o --omf_browse .\objects\gd32f4xx_ctc.crf --depend .\objects\gd32f4xx_ctc.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_dac.c)(0x68947F4E)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_dac.o --omf_browse .\objects\gd32f4xx_dac.crf --depend .\objects\gd32f4xx_dac.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_dbg.c)(0x68947F4E)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_dbg.o --omf_browse .\objects\gd32f4xx_dbg.crf --depend .\objects\gd32f4xx_dbg.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_dci.c)(0x68947F4E)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_dci.o --omf_browse .\objects\gd32f4xx_dci.crf --depend .\objects\gd32f4xx_dci.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_dma.c)(0x68947F4E)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_dma.o --omf_browse .\objects\gd32f4xx_dma.crf --depend .\objects\gd32f4xx_dma.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_enet.c)(0x68947F4E)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_enet.o --omf_browse .\objects\gd32f4xx_enet.crf --depend .\objects\gd32f4xx_enet.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_exmc.c)(0x68947F4E)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_exmc.o --omf_browse .\objects\gd32f4xx_exmc.crf --depend .\objects\gd32f4xx_exmc.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_exti.c)(0x68947F4E)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_exti.o --omf_browse .\objects\gd32f4xx_exti.crf --depend .\objects\gd32f4xx_exti.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_fmc.c)(0x68947F4E)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_fmc.o --omf_browse .\objects\gd32f4xx_fmc.crf --depend .\objects\gd32f4xx_fmc.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_fwdgt.c)(0x68947F4E)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_fwdgt.o --omf_browse .\objects\gd32f4xx_fwdgt.crf --depend .\objects\gd32f4xx_fwdgt.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_gpio.c)(0x68947F4E)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_gpio.o --omf_browse .\objects\gd32f4xx_gpio.crf --depend .\objects\gd32f4xx_gpio.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_i2c.c)(0x68947F4E)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_i2c.o --omf_browse .\objects\gd32f4xx_i2c.crf --depend .\objects\gd32f4xx_i2c.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_ipa.c)(0x68947F4E)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_ipa.o --omf_browse .\objects\gd32f4xx_ipa.crf --depend .\objects\gd32f4xx_ipa.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_iref.c)(0x68947F4E)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_iref.o --omf_browse .\objects\gd32f4xx_iref.crf --depend .\objects\gd32f4xx_iref.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_misc.c)(0x68947F4E)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_misc.o --omf_browse .\objects\gd32f4xx_misc.crf --depend .\objects\gd32f4xx_misc.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_pmu.c)(0x68947F4E)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_pmu.o --omf_browse .\objects\gd32f4xx_pmu.crf --depend .\objects\gd32f4xx_pmu.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_rcu.c)(0x68947F4E)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_rcu.o --omf_browse .\objects\gd32f4xx_rcu.crf --depend .\objects\gd32f4xx_rcu.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_rtc.c)(0x68947F4E)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_rtc.o --omf_browse .\objects\gd32f4xx_rtc.crf --depend .\objects\gd32f4xx_rtc.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_sdio.c)(0x68947F4E)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_sdio.o --omf_browse .\objects\gd32f4xx_sdio.crf --depend .\objects\gd32f4xx_sdio.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_spi.c)(0x68947F4E)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_spi.o --omf_browse .\objects\gd32f4xx_spi.crf --depend .\objects\gd32f4xx_spi.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_syscfg.c)(0x68947F4E)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_syscfg.o --omf_browse .\objects\gd32f4xx_syscfg.crf --depend .\objects\gd32f4xx_syscfg.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_timer.c)(0x68947F4E)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_timer.o --omf_browse .\objects\gd32f4xx_timer.crf --depend .\objects\gd32f4xx_timer.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_tli.c)(0x68947F4E)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_tli.o --omf_browse .\objects\gd32f4xx_tli.crf --depend .\objects\gd32f4xx_tli.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_trng.c)(0x68947F4E)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_trng.o --omf_browse .\objects\gd32f4xx_trng.crf --depend .\objects\gd32f4xx_trng.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_usart.c)(0x68947F4E)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_usart.o --omf_browse .\objects\gd32f4xx_usart.crf --depend .\objects\gd32f4xx_usart.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
F (.\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_wwdgt.c)(0x68947F4E)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_wwdgt.o --omf_browse .\objects\gd32f4xx_wwdgt.crf --depend .\objects\gd32f4xx_wwdgt.d)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
F (.\Firmware\CMSIS\GD\GD32F4xx\Source\system_gd32f4xx.c)(0x68947F4E)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\system_gd32f4xx.o --omf_browse .\objects\system_gd32f4xx.crf --depend .\objects\system_gd32f4xx.d)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
F (.\USER\gd32f4xx_it.c)(0x68947F51)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f4xx_it.o --omf_browse .\objects\gd32f4xx_it.crf --depend .\objects\gd32f4xx_it.d)
I (USER\main.h)(0x68947F51)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
I (USER\gd32f4xx_it.h)(0x68947F51)
I (.\Utilities\gd32f470v_start.h)(0x68947F52)
I (USER\systick.h)(0x68947F51)
I (.\Utilities\usart.h)(0x68947F52)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (.\Utilities\time.h)(0x68947F52)
I (.\Utilities\esp32_wifi.h)(0x68947F52)
I (.\Utilities\gpio.h)(0x68947F52)
I (.\Utilities\user_step.h)(0x68947F52)
I (.\Utilities\25LC080A.h)(0x68947F51)
I (.\Utilities\My_CRC.h)(0x68947F52)
I (.\Utilities\eeprom_spi.h)(0x68947F52)
I (.\Utilities\spi.h)(0x68947F52)
I (.\Utilities\dac.h)(0x68947F52)
I (.\Utilities\adc.h)(0x68947F51)
I (.\Utilities\mymath.h)(0x68947F52)
I (E:\MDK533\ARM\ARMCC\include\math.h)(0x5E8E2EB2)
I (.\Utilities\whut_math.h)(0x68947F52)
I (E:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include\arm_math.h)(0x5E8ED122)
I (E:\MDK533\ARM\ARMCC\include\float.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\limits.h)(0x5E8E2EB2)
I (.\Utilities\flash.h)(0x68947F52)
I (.\Utilities\rtc.h)(0x68947F52)
I (.\USBFS-object\drv_usbd_int.h)(0x68947F51)
I (.\USBFS-object\drv_usb_core.h)(0x68947F51)
I (.\USBFS-object\drv_usb_regs.h)(0x68947F51)
I (.\USBFS-object\usb_conf.h)(0x68947F51)
I (.\USBFS-object\usb_ch9_std.h)(0x68947F51)
I (.\USBFS-object\usbd_conf.h)(0x68947F51)
I (.\USBFS-object\drv_usb_dev.h)(0x68947F51)
F (.\USER\main.c)(0x689488F4)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\main.o --omf_browse .\objects\main.crf --depend .\objects\main.d)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
I (.\Utilities\gd32f470v_start.h)(0x68947F52)
I (USER\systick.h)(0x68947F51)
I (.\Utilities\usart.h)(0x68947F52)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (.\Utilities\time.h)(0x68947F52)
I (.\Utilities\esp32_wifi.h)(0x68947F52)
I (.\Utilities\gpio.h)(0x68947F52)
I (.\Utilities\user_step.h)(0x68947F52)
I (.\Utilities\25LC080A.h)(0x68947F51)
I (.\Utilities\My_CRC.h)(0x68947F52)
I (.\Utilities\eeprom_spi.h)(0x68947F52)
I (.\Utilities\spi.h)(0x68947F52)
I (.\Utilities\dac.h)(0x68947F52)
I (.\Utilities\adc.h)(0x68947F51)
I (.\Utilities\mymath.h)(0x68947F52)
I (E:\MDK533\ARM\ARMCC\include\math.h)(0x5E8E2EB2)
I (.\Utilities\whut_math.h)(0x68947F52)
I (E:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include\arm_math.h)(0x5E8ED122)
I (E:\MDK533\ARM\ARMCC\include\float.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\limits.h)(0x5E8E2EB2)
I (.\Utilities\flash.h)(0x68947F52)
I (.\Utilities\rtc.h)(0x68947F52)
I (.\Utilities\dma.h)(0x68947F52)
I (.\Utilities\API_TNRG.h)(0x68947F51)
I (.\Utilities\API_W5500.h)(0x68947F52)
I (..\Ethernet\W5500\w5500.h)(0x68947F52)
I (..\Ethernet\W5500\Types.h)(0x68947F52)
I (.\Utilities\API_LAN_DATA_Process.h)(0x68947F51)
I (..\Ethernet\W5500\socket.h)(0x68947F52)
I (..\Ethernet\W5500\w5500_conf.h)(0x68947F52)
I (..\Ethernet\W5500\utility.h)(0x68947F52)
I (..\Ethernet\APP\dhcp.h)(0x68947F52)
I (..\Ethernet\APP\tcp_client.h)(0x68947F52)
F (.\USER\systick.c)(0x68947F51)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\systick.o --omf_browse .\objects\systick.crf --depend .\objects\systick.d)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
I (USER\systick.h)(0x68947F51)
F (.\Utilities\25LC080A.c)(0x68947F51)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\25lc080a.o --omf_browse .\objects\25lc080a.crf --depend .\objects\25lc080a.d)
I (Utilities\25LC080A.h)(0x68947F51)
I (Utilities\My_CRC.h)(0x68947F52)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
I (Utilities\eeprom_spi.h)(0x68947F52)
I (.\USER\systick.h)(0x68947F51)
I (Utilities\usart.h)(0x68947F52)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
F (.\Utilities\eeprom_spi.c)(0x68947F52)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\eeprom_spi.o --omf_browse .\objects\eeprom_spi.crf --depend .\objects\eeprom_spi.d)
I (Utilities\eeprom_spi.h)(0x68947F52)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
I (.\USER\systick.h)(0x68947F51)
I (Utilities\spi.h)(0x68947F52)
F (.\Utilities\flash.c)(0x68947F52)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\flash.o --omf_browse .\objects\flash.crf --depend .\objects\flash.d)
I (Utilities\flash.h)(0x68947F52)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
F (.\Utilities\gpio.c)(0x68947F52)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gpio.o --omf_browse .\objects\gpio.crf --depend .\objects\gpio.d)
I (Utilities\gpio.h)(0x68947F52)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
F (.\Utilities\My_CRC.c)(0x68947F52)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\my_crc.o --omf_browse .\objects\my_crc.crf --depend .\objects\my_crc.d)
I (Utilities\My_CRC.h)(0x68947F52)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
F (.\Utilities\rtc.c)(0x68947F52)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\rtc.o --omf_browse .\objects\rtc.crf --depend .\objects\rtc.d)
I (Utilities\rtc.h)(0x68947F52)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
F (.\Utilities\spi.c)(0x68947F52)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\spi.o --omf_browse .\objects\spi.crf --depend .\objects\spi.d)
I (Utilities\spi.h)(0x68947F52)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
F (.\Utilities\time.c)(0x68947F52)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\time.o --omf_browse .\objects\time.crf --depend .\objects\time.d)
I (Utilities\time.h)(0x68947F52)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
I (Utilities\gd32f470v_start.h)(0x68947F52)
I (Utilities\esp32_wifi.h)(0x68947F52)
I (Utilities\usart.h)(0x68947F52)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (Utilities\gpio.h)(0x68947F52)
I (Utilities\user_step.h)(0x68947F52)
I (Utilities\25LC080A.h)(0x68947F51)
I (Utilities\My_CRC.h)(0x68947F52)
I (Utilities\eeprom_spi.h)(0x68947F52)
I (.\USER\systick.h)(0x68947F51)
I (Utilities\spi.h)(0x68947F52)
I (Utilities\dac.h)(0x68947F52)
I (Utilities\adc.h)(0x68947F51)
I (Utilities\mymath.h)(0x68947F52)
I (E:\MDK533\ARM\ARMCC\include\math.h)(0x5E8E2EB2)
I (Utilities\whut_math.h)(0x68947F52)
I (E:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include\arm_math.h)(0x5E8ED122)
I (E:\MDK533\ARM\ARMCC\include\float.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\limits.h)(0x5E8E2EB2)
I (Utilities\flash.h)(0x68947F52)
I (Utilities\rtc.h)(0x68947F52)
I (Utilities\API_W5500.h)(0x68947F52)
I (..\Ethernet\W5500\w5500.h)(0x68947F52)
I (..\Ethernet\W5500\Types.h)(0x68947F52)
F (.\Utilities\usart.c)(0x68948926)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\usart.o --omf_browse .\objects\usart.crf --depend .\objects\usart.d)
I (Utilities\usart.h)(0x68947F52)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (..\Ethernet\APP\tcp_client.h)(0x68947F52)
I (..\Ethernet\W5500\socket.h)(0x68947F52)
I (..\Ethernet\W5500\Types.h)(0x68947F52)
I (..\Ethernet\W5500\w5500.h)(0x68947F52)
I (..\Ethernet\W5500\w5500_conf.h)(0x68947F52)
I (Utilities\API_W5500.h)(0x68947F52)
I (Utilities\spi.h)(0x68947F52)
I (.\USER\systick.h)(0x68947F51)
I (Utilities\25LC080A.h)(0x68947F51)
I (Utilities\My_CRC.h)(0x68947F52)
I (Utilities\eeprom_spi.h)(0x68947F52)
I (Utilities\API_LAN_DATA_Process.h)(0x68947F51)
I (Utilities\gpio.h)(0x68947F52)
F (.\Utilities\user_step.c)(0x68947F52)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\user_step.o --omf_browse .\objects\user_step.crf --depend .\objects\user_step.d)
I (Utilities\user_step.h)(0x68947F52)
I (Utilities\25LC080A.h)(0x68947F51)
I (Utilities\My_CRC.h)(0x68947F52)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
I (Utilities\eeprom_spi.h)(0x68947F52)
I (.\USER\systick.h)(0x68947F51)
I (Utilities\usart.h)(0x68947F52)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (Utilities\spi.h)(0x68947F52)
I (Utilities\esp32_wifi.h)(0x68947F52)
I (Utilities\gpio.h)(0x68947F52)
I (Utilities\dac.h)(0x68947F52)
I (Utilities\gd32f470v_start.h)(0x68947F52)
I (Utilities\adc.h)(0x68947F51)
I (Utilities\mymath.h)(0x68947F52)
I (E:\MDK533\ARM\ARMCC\include\math.h)(0x5E8E2EB2)
I (Utilities\whut_math.h)(0x68947F52)
I (E:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include\arm_math.h)(0x5E8ED122)
I (E:\MDK533\ARM\ARMCC\include\float.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\limits.h)(0x5E8E2EB2)
I (Utilities\flash.h)(0x68947F52)
I (Utilities\rtc.h)(0x68947F52)
I (Utilities\API_LAN_DATA_Process.h)(0x68947F51)
I (..\Ethernet\W5500\socket.h)(0x68947F52)
I (..\Ethernet\W5500\Types.h)(0x68947F52)
F (.\Utilities\gd32f470v_start.c)(0x68947F52)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\gd32f470v_start.o --omf_browse .\objects\gd32f470v_start.crf --depend .\objects\gd32f470v_start.d)
I (Utilities\gd32f470v_start.h)(0x68947F52)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
F (.\USER\readme.txt)(0x68947F51)()
F (.\Utilities\API_LAN_DATA_Process .c)(0x689488C8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o ".\objects\api_lan_data_process .o" --omf_browse ".\objects\api_lan_data_process .crf" --depend ".\objects\api_lan_data_process .d")
I (Utilities\API_LAN_DATA_Process.h)(0x68947F51)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
I (..\Ethernet\W5500\socket.h)(0x68947F52)
I (..\Ethernet\W5500\Types.h)(0x68947F52)
I (Utilities\API_W5500.h)(0x68947F52)
I (Utilities\spi.h)(0x68947F52)
I (.\USER\systick.h)(0x68947F51)
I (Utilities\25LC080A.h)(0x68947F51)
I (Utilities\My_CRC.h)(0x68947F52)
I (Utilities\eeprom_spi.h)(0x68947F52)
I (Utilities\usart.h)(0x68947F52)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (..\Ethernet\W5500\w5500.h)(0x68947F52)
I (Utilities\gd32f470v_start.h)(0x68947F52)
I (Utilities\mymath.h)(0x68947F52)
I (E:\MDK533\ARM\ARMCC\include\math.h)(0x5E8E2EB2)
I (Utilities\whut_math.h)(0x68947F52)
I (E:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include\arm_math.h)(0x5E8ED122)
I (E:\MDK533\ARM\ARMCC\include\float.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\limits.h)(0x5E8E2EB2)
I (Utilities\adc.h)(0x68947F51)
I (Utilities\user_step.h)(0x68947F52)
I (Utilities\esp32_wifi.h)(0x68947F52)
I (Utilities\gpio.h)(0x68947F52)
I (Utilities\dac.h)(0x68947F52)
I (Utilities\flash.h)(0x68947F52)
I (Utilities\rtc.h)(0x68947F52)
I (..\Ethernet\W5500\w5500_conf.h)(0x68947F52)
I (..\Ethernet\W5500\utility.h)(0x68947F52)
I (..\Ethernet\APP\dhcp.h)(0x68947F52)
I (..\Ethernet\APP\tcp_client.h)(0x68947F52)
F (.\Utilities\API_TNRG.c)(0x68947F51)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\api_tnrg.o --omf_browse .\objects\api_tnrg.crf --depend .\objects\api_tnrg.d)
I (Utilities\API_TNRG.h)(0x68947F51)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
I (Utilities\usart.h)(0x68947F52)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (Utilities\25LC080A.h)(0x68947F51)
I (Utilities\My_CRC.h)(0x68947F52)
I (Utilities\eeprom_spi.h)(0x68947F52)
I (.\USER\systick.h)(0x68947F51)
I (Utilities\spi.h)(0x68947F52)
I (Utilities\API_W5500.h)(0x68947F52)
I (..\Ethernet\W5500\w5500.h)(0x68947F52)
I (..\Ethernet\W5500\Types.h)(0x68947F52)
F (.\Utilities\API_W5500.c)(0x68948883)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\api_w5500.o --omf_browse .\objects\api_w5500.crf --depend .\objects\api_w5500.d)
I (Utilities\API_W5500.h)(0x68947F52)
I (Utilities\spi.h)(0x68947F52)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
I (.\USER\systick.h)(0x68947F51)
I (Utilities\25LC080A.h)(0x68947F51)
I (Utilities\My_CRC.h)(0x68947F52)
I (Utilities\eeprom_spi.h)(0x68947F52)
I (Utilities\usart.h)(0x68947F52)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (..\Ethernet\W5500\w5500.h)(0x68947F52)
I (..\Ethernet\W5500\Types.h)(0x68947F52)
I (..\Ethernet\W5500\w5500_conf.h)(0x68947F52)
I (Utilities\gpio.h)(0x68947F52)
I (..\Ethernet\W5500\utility.h)(0x68947F52)
I (..\Ethernet\APP\dhcp.h)(0x68947F52)
I (..\Ethernet\APP\tcp_client.h)(0x68947F52)
I (..\Ethernet\W5500\socket.h)(0x68947F52)
I (Utilities\user_step.h)(0x68947F52)
I (Utilities\esp32_wifi.h)(0x68947F52)
I (Utilities\dac.h)(0x68947F52)
I (Utilities\gd32f470v_start.h)(0x68947F52)
I (Utilities\adc.h)(0x68947F51)
I (Utilities\mymath.h)(0x68947F52)
I (E:\MDK533\ARM\ARMCC\include\math.h)(0x5E8E2EB2)
I (Utilities\whut_math.h)(0x68947F52)
I (E:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include\arm_math.h)(0x5E8ED122)
I (E:\MDK533\ARM\ARMCC\include\float.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\limits.h)(0x5E8E2EB2)
I (Utilities\flash.h)(0x68947F52)
I (Utilities\rtc.h)(0x68947F52)
F (..\Ethernet\W5500\socket.c)(0x68947F52)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\socket.o --omf_browse .\objects\socket.crf --depend .\objects\socket.d)
I (..\Ethernet\W5500\socket.h)(0x68947F52)
I (..\Ethernet\W5500\Types.h)(0x68947F52)
I (..\Ethernet\W5500\w5500_conf.h)(0x68947F52)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (..\Ethernet\W5500\w5500.h)(0x68947F52)
I (.\Utilities\API_W5500.h)(0x68947F52)
I (.\Utilities\spi.h)(0x68947F52)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
I (.\USER\systick.h)(0x68947F51)
I (.\Utilities\25LC080A.h)(0x68947F51)
I (.\Utilities\My_CRC.h)(0x68947F52)
I (.\Utilities\eeprom_spi.h)(0x68947F52)
I (.\Utilities\usart.h)(0x68947F52)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (.\Utilities\gpio.h)(0x68947F52)
F (..\Ethernet\W5500\socket.h)(0x68947F52)()
F (..\Ethernet\W5500\types.h)(0x68947F52)()
F (..\Ethernet\W5500\utility.c)(0x68947F52)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\utility.o --omf_browse .\objects\utility.crf --depend .\objects\utility.d)
I (..\Ethernet\W5500\w5500.h)(0x68947F52)
I (..\Ethernet\W5500\Types.h)(0x68947F52)
I (..\Ethernet\W5500\w5500_conf.h)(0x68947F52)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (..\Ethernet\W5500\utility.h)(0x68947F52)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdarg.h)(0x5E8E2EB2)
F (..\Ethernet\W5500\utility.h)(0x68947F52)()
F (..\Ethernet\W5500\w5500.c)(0x68947F52)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\w5500.o --omf_browse .\objects\w5500.crf --depend .\objects\w5500.d)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (..\Ethernet\W5500\w5500_conf.h)(0x68947F52)
I (..\Ethernet\W5500\types.h)(0x68947F52)
I (..\Ethernet\W5500\w5500.h)(0x68947F52)
I (..\Ethernet\W5500\socket.h)(0x68947F52)
I (..\Ethernet\W5500\utility.h)(0x68947F52)
I (.\Utilities\API_W5500.h)(0x68947F52)
I (.\Utilities\spi.h)(0x68947F52)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
I (.\USER\systick.h)(0x68947F51)
I (.\Utilities\25LC080A.h)(0x68947F51)
I (.\Utilities\My_CRC.h)(0x68947F52)
I (.\Utilities\eeprom_spi.h)(0x68947F52)
I (.\Utilities\usart.h)(0x68947F52)
F (..\Ethernet\W5500\w5500.h)(0x68947F52)()
F (..\Ethernet\W5500\w5500_conf.c)(0x68947F52)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\w5500_conf.o --omf_browse .\objects\w5500_conf.crf --depend .\objects\w5500_conf.d)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (..\Ethernet\W5500\w5500_conf.h)(0x68947F52)
I (..\Ethernet\W5500\types.h)(0x68947F52)
I (..\Ethernet\W5500\utility.h)(0x68947F52)
I (..\Ethernet\W5500\w5500.h)(0x68947F52)
I (..\Ethernet\W5500\socket.h)(0x68947F52)
I (..\Ethernet\APP\dhcp.h)(0x68947F52)
I (.\Utilities\API_W5500.h)(0x68947F52)
I (.\Utilities\spi.h)(0x68947F52)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
I (.\USER\systick.h)(0x68947F51)
I (.\Utilities\25LC080A.h)(0x68947F51)
I (.\Utilities\My_CRC.h)(0x68947F52)
I (.\Utilities\eeprom_spi.h)(0x68947F52)
I (.\Utilities\usart.h)(0x68947F52)
F (..\Ethernet\W5500\w5500_conf.h)(0x68947F52)()
F (..\Ethernet\APP\dhcp.c)(0x68947F52)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\dhcp.o --omf_browse .\objects\dhcp.crf --depend .\objects\dhcp.d)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (..\Ethernet\W5500\socket.h)(0x68947F52)
I (..\Ethernet\W5500\Types.h)(0x68947F52)
I (..\Ethernet\W5500\w5500.h)(0x68947F52)
I (..\Ethernet\W5500\w5500_conf.h)(0x68947F52)
I (..\Ethernet\APP\dhcp.h)(0x68947F52)
I (.\Utilities\API_W5500.h)(0x68947F52)
I (.\Utilities\spi.h)(0x68947F52)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
I (.\USER\systick.h)(0x68947F51)
I (.\Utilities\25LC080A.h)(0x68947F51)
I (.\Utilities\My_CRC.h)(0x68947F52)
I (.\Utilities\eeprom_spi.h)(0x68947F52)
I (.\Utilities\usart.h)(0x68947F52)
F (..\Ethernet\APP\dhcp.h)(0x68947F52)()
F (..\Ethernet\APP\tcp_client.c)(0x68947F52)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I .\Firmware\CMSIS\GD\GD32F4xx\Include -I .\USER -I .\Firmware\GD32F4xx_standard_peripheral\Include -I .\Firmware\CMSIS -I .\Utilities -I .\USBFS-object -I ..\Ethernet\APP -I ..\Ethernet\W5500

-I.\RTE\_Target_1

-IE:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Include

-IE:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="533" -D_RTE_ -DGD32F470 -D_RTE_ -DGD32F470 -DUSE_USB_FS -DARM_MATH_CM4

-o .\objects\tcp_client.o --omf_browse .\objects\tcp_client.crf --depend .\objects\tcp_client.d)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (..\Ethernet\APP\tcp_client.h)(0x68947F52)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\Ethernet\W5500\socket.h)(0x68947F52)
I (..\Ethernet\W5500\Types.h)(0x68947F52)
I (..\Ethernet\W5500\w5500.h)(0x68947F52)
I (..\Ethernet\W5500\w5500_conf.h)(0x68947F52)
I (.\Utilities\API_W5500.h)(0x68947F52)
I (.\Utilities\spi.h)(0x68947F52)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x68947F4E)
I (.\Firmware\CMSIS\core_cm4.h)(0x68947F4D)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h)(0x60D3E8E2)
I (E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h)(0x60D3E8E2)
I (.\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x68947F4E)
I (.\USER\gd32f4xx_libopt.h)(0x68947F51)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x68947F4E)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x68947F4E)
I (.\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x68947F4E)
I (.\USER\systick.h)(0x68947F51)
I (.\Utilities\25LC080A.h)(0x68947F51)
I (.\Utilities\My_CRC.h)(0x68947F52)
I (.\Utilities\eeprom_spi.h)(0x68947F52)
I (.\Utilities\usart.h)(0x68947F52)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
F (..\Ethernet\APP\tcp_client.h)(0x68947F52)()
F (E:\MDKPacks\ARM\CMSIS\5.7.0\CMSIS\DSP\Lib\ARM\arm_cortexM4lf_math.lib)(0x5E8ED124)()
