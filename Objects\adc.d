.\objects\adc.o: Utilities\adc.c
.\objects\adc.o: Utilities\adc.h
.\objects\adc.o: .\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\objects\adc.o: .\Firmware\CMSIS\core_cm4.h
.\objects\adc.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\adc.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h
.\objects\adc.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h
.\objects\adc.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h
.\objects\adc.o: C:\Users\<USER>\AppData\Local\Arm\Packs\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h
.\objects\adc.o: .\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h
.\objects\adc.o: .\USER\gd32f4xx_libopt.h
.\objects\adc.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h
.\objects\adc.o: .\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\objects\adc.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h
.\objects\adc.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h
.\objects\adc.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h
.\objects\adc.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h
.\objects\adc.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h
.\objects\adc.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h
.\objects\adc.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h
.\objects\adc.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h
.\objects\adc.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h
.\objects\adc.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h
.\objects\adc.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h
.\objects\adc.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h
.\objects\adc.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h
.\objects\adc.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h
.\objects\adc.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h
.\objects\adc.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h
.\objects\adc.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h
.\objects\adc.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h
.\objects\adc.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h
.\objects\adc.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h
.\objects\adc.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h
.\objects\adc.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h
.\objects\adc.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h
.\objects\adc.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h
.\objects\adc.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h
.\objects\adc.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdlib.h
.\objects\adc.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h
.\objects\adc.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h
.\objects\adc.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h
.\objects\adc.o: Utilities\gd32f470v_start.h
.\objects\adc.o: Utilities\usart.h
.\objects\adc.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
.\objects\adc.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdio.h
.\objects\adc.o: .\USER\systick.h
