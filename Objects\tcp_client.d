.\objects\tcp_client.o: ..\Ethernet\APP\tcp_client.c
.\objects\tcp_client.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdio.h
.\objects\tcp_client.o: ..\Ethernet\APP\tcp_client.h
.\objects\tcp_client.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\tcp_client.o: ..\Ethernet\W5500\socket.h
.\objects\tcp_client.o: ..\Ethernet\W5500\Types.h
.\objects\tcp_client.o: ..\Ethernet\W5500\w5500.h
.\objects\tcp_client.o: ..\Ethernet\W5500\w5500_conf.h
.\objects\tcp_client.o: .\Utilities\API_W5500.h
.\objects\tcp_client.o: .\Utilities\spi.h
.\objects\tcp_client.o: .\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\objects\tcp_client.o: .\Firmware\CMSIS\core_cm4.h
.\objects\tcp_client.o: E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_version.h
.\objects\tcp_client.o: E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_compiler.h
.\objects\tcp_client.o: E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\cmsis_armcc.h
.\objects\tcp_client.o: E:\MDKPacks\ARM\CMSIS\5.8.0\CMSIS\Core\Include\mpu_armv7.h
.\objects\tcp_client.o: .\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h
.\objects\tcp_client.o: .\USER\gd32f4xx_libopt.h
.\objects\tcp_client.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h
.\objects\tcp_client.o: .\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\objects\tcp_client.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h
.\objects\tcp_client.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h
.\objects\tcp_client.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h
.\objects\tcp_client.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h
.\objects\tcp_client.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h
.\objects\tcp_client.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h
.\objects\tcp_client.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h
.\objects\tcp_client.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h
.\objects\tcp_client.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h
.\objects\tcp_client.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h
.\objects\tcp_client.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h
.\objects\tcp_client.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h
.\objects\tcp_client.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h
.\objects\tcp_client.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h
.\objects\tcp_client.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h
.\objects\tcp_client.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h
.\objects\tcp_client.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h
.\objects\tcp_client.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h
.\objects\tcp_client.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h
.\objects\tcp_client.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h
.\objects\tcp_client.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h
.\objects\tcp_client.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h
.\objects\tcp_client.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h
.\objects\tcp_client.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h
.\objects\tcp_client.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h
.\objects\tcp_client.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdlib.h
.\objects\tcp_client.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h
.\objects\tcp_client.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h
.\objects\tcp_client.o: .\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h
.\objects\tcp_client.o: .\USER\systick.h
.\objects\tcp_client.o: .\Utilities\25LC080A.h
.\objects\tcp_client.o: .\Utilities\My_CRC.h
.\objects\tcp_client.o: .\Utilities\eeprom_spi.h
.\objects\tcp_client.o: .\Utilities\usart.h
.\objects\tcp_client.o: E:\MDK533\ARM\ARMCC\Bin\..\include\string.h
