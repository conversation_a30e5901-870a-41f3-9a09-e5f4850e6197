#include "eeprom_spi.h"
#include "spi.h"

#define Dummy_Byte 0xA5

/**
  * 在指定地址开始读取指定长度的数据
  * @brief  Reads a block of data from the EEPROM(READ sequence).
  *
  * @param  pBuffer: pointer to the buffer that receives the data read from the EEPROM.
  * @param  ReadAddr: EEPROM's internal address to read from.
  * @param  NumByteToRead: number of bytes to read from the EEPROM.
  * @retval None
  */
EEPROMStatus EEPROM_SPI_ReadBuffer(uint8_t* pBuffer, unsigned int ReadAddr, uint16_t NumByteToRead)
{
	  while(SET == spi_i2s_flag_get(SPI1, SPI_FLAG_TRANS))
      {delay_1ms(1);}/*!< transmit buffer empty flag */
    uint8_t header[3];
		uint16_t receive_n=0;

    header[0] = EEPROM_READ;           // Send "Read from Memory" instruction
	  header[1] = (ReadAddr >> 8)&0XFF; // Send high 8-bit address
    header[2] = (ReadAddr   )&0XFF; // Send midlle 8-bit address
    SET_SPI1_NSS_LOW;
    EEPROM_SPI_SendInstruction(header, 3);    /* Send WriteAddr address byte to read from */
		while(receive_n < NumByteToRead)
		{
       pBuffer[receive_n] =DRV_SPI_SwapByte(Dummy_Byte);
			receive_n++;
    }
    SET_SPI1_NSS_HIGH;
    return EEPROM_STATUS_COMPLETE;
}
/**
  * 在指定地址开始写入指定长度的数据
  * @brief  Writes block of data to the EEPROM. In this function, the number of
  *         WRITE cycles are reduced, using Page WRITE sequence.(WRITE sequence)
  *
  * @param  pBuffer: pointer to the buffer  containing the data to be written
  *         to the EEPROM.
  * @param  WriteAddr: EEPROM's internal address to write to.
  * @param  NumByteToWrite: number of bytes to write to the EEPROM.
  * @retval EEPROMOperations value: EEPROM_STATUS_COMPLETE or EEPROM_STATUS_ERROR
  */
EEPROMStatus EEPROM_SPI_WriteBuffer(uint8_t* pBuffer, unsigned int WriteAddr, uint16_t NumByteToWrite)
{
    uint16_t NumOfPage = 0, NumOfSingle = 0, Addr = 0, count = 0;
    uint16_t sEE_DataNum = 0;
    EEPROMStatus pageWriteStatus = EEPROM_STATUS_PENDING;
    Addr = WriteAddr % EEPROM_PAGESIZE;//用于判断起始地址是否对齐页地址
    count = EEPROM_PAGESIZE - Addr;//数据页上偏移个数
    NumOfPage =  NumByteToWrite / EEPROM_PAGESIZE;//数据页数量
    NumOfSingle = NumByteToWrite % EEPROM_PAGESIZE;//附加数据页上的个数

    if (Addr == 0) { /* WriteAddr is EEPROM_PAGESIZE aligned 地址对齐 （256的倍数）*/
        if (NumOfPage == 0) { /* NumByteToWrite <= EEPROM_PAGESIZE 数据小于256个*/
            sEE_DataNum = NumByteToWrite;
            pageWriteStatus = EEPROM_SPI_WritePage(pBuffer, WriteAddr, sEE_DataNum);//不超过1页数据，调用页写入函数

            if (pageWriteStatus != EEPROM_STATUS_COMPLETE) {
                return pageWriteStatus;
            }

        } else { /* NumByteToWrite > EEPROM_PAGESIZE 数据量超过页数据量（256个）*/
            while (NumOfPage--) {//1、先把整页数据写完
                sEE_DataNum = EEPROM_PAGESIZE;
                pageWriteStatus = EEPROM_SPI_WritePage(pBuffer, WriteAddr, sEE_DataNum);//调用页写入函数

                if (pageWriteStatus != EEPROM_STATUS_COMPLETE) {
                    return pageWriteStatus;
                }

                WriteAddr +=  EEPROM_PAGESIZE;//地址加页大小256
                pBuffer += EEPROM_PAGESIZE;//数据缓存地址加页大小256
            }
            if (NumOfSingle != 0){//判断是否还有剩余数据
            sEE_DataNum = NumOfSingle;//2、把追加数据写完
            pageWriteStatus = EEPROM_SPI_WritePage(pBuffer, WriteAddr, sEE_DataNum);
            }
            if (pageWriteStatus != EEPROM_STATUS_COMPLETE) {
                return pageWriteStatus;
            }
        }
    } else { /* WriteAddr is not EEPROM_PAGESIZE aligned 地址不对齐 （不是256的倍数 ）*/
        if (NumOfPage == 0) { /* NumByteToWrite < EEPROM_PAGESIZE 数据量小于256*/
            if (NumOfSingle > count) { /* (NumByteToWrite + WriteAddr) > EEPROM_PAGESIZE 剩余数量大于偏移地址*/
                sEE_DataNum = count;
				 /* 在当前地址页剩余地址中写入数据，当前地址页写满*/
                pageWriteStatus = EEPROM_SPI_WritePage(pBuffer, WriteAddr, sEE_DataNum);

                if (pageWriteStatus != EEPROM_STATUS_COMPLETE) {
                    return pageWriteStatus;
                }

                WriteAddr +=  count;
                pBuffer += count;
				
                sEE_DataNum = NumOfSingle - count;
				/* 在当前地址下一页写入剩余数据*/
                pageWriteStatus = EEPROM_SPI_WritePage(pBuffer, WriteAddr, sEE_DataNum);
            } else {//地址当前页剩余空间可以写入数据
                sEE_DataNum = NumByteToWrite;
                pageWriteStatus = EEPROM_SPI_WritePage(pBuffer, WriteAddr, sEE_DataNum);
            }

            if (pageWriteStatus != EEPROM_STATUS_COMPLETE) {
                return pageWriteStatus;
            }
        } else { /* NumByteToWrite > EEPROM_PAGESIZE 写入数量大于256 */
            NumByteToWrite -= count;
            NumOfPage =  NumByteToWrite / EEPROM_PAGESIZE;
            NumOfSingle = NumByteToWrite % EEPROM_PAGESIZE;

            sEE_DataNum = count;
            /* 在当前地址页剩余地址中写入数据，当前地址页写满*/
            pageWriteStatus = EEPROM_SPI_WritePage(pBuffer, WriteAddr, sEE_DataNum);

            if (pageWriteStatus != EEPROM_STATUS_COMPLETE) {
                return pageWriteStatus;
            }

            WriteAddr +=  count;
            pBuffer += count;

            while (NumOfPage--) {//1、剩余数量大于256时，先把整页数据写完
                sEE_DataNum = EEPROM_PAGESIZE;

                pageWriteStatus = EEPROM_SPI_WritePage(pBuffer, WriteAddr, sEE_DataNum);

                if (pageWriteStatus != EEPROM_STATUS_COMPLETE) {
                    return pageWriteStatus;
                }

                WriteAddr +=  EEPROM_PAGESIZE;
                pBuffer += EEPROM_PAGESIZE;
            }

            if (NumOfSingle != 0) {//2、不足1页数据部分，继续写入
                sEE_DataNum = NumOfSingle;

                pageWriteStatus = EEPROM_SPI_WritePage(pBuffer, WriteAddr, sEE_DataNum);

                if (pageWriteStatus != EEPROM_STATUS_COMPLETE) {
                    return pageWriteStatus;
                }
            }
        }
    }

    return EEPROM_STATUS_COMPLETE;
}

/**         写入整页(256Byte）数据函数
  * @brief  Writes more than one byte to the EEPROM with a single WRITE cycle
  *         (Page WRITE sequence).
  *
  * @note   The number of byte can't exceed the EEPROM page size.
  * @param  pBuffer: pointer to the buffer  containing the data to be written
  *         to the EEPROM.
  * @param  WriteAddr: EEPROM's internal address to write to.
  * @param  NumByteToWrite: number of bytes to write to the EEPROM, must be equal
  *         or less than "EEPROM_PAGESIZE" value.
  * @retval EEPROMOperations value: EEPROM_STATUS_COMPLETE or EEPROM_STATUS_ERROR
  */

EEPROMStatus EEPROM_SPI_WritePage(uint8_t* pBuffer, unsigned int WriteAddr, uint16_t NumByteToWrite)
{
		while(SET == spi_i2s_flag_get(SPI1, SPI_FLAG_TRANS))
			{delay_1ms(1);}
    EEPROM_WriteEnable();//写使能
    uint8_t header[3];
  	uint8_t send_n=0;

    header[0] = EEPROM_WRITE;    // Send "Write to Memory" instruction
    header[1] = (WriteAddr >> 8 )&0XFF; 
    header[2] =  WriteAddr       &0XFF;
    SET_SPI1_NSS_LOW;
    EEPROM_SPI_SendInstruction((uint8_t*)header, 3);
    for (uint8_t i = 0; i < 5; i++) 
		{		
				while(send_n < NumByteToWrite)
				{
					DRV_SPI_SwapByte(pBuffer[send_n]);
					send_n++;
				}

        if (spi_i2s_flag_get(SPI1, SPI_FLAG_TRANS) == SET)
				{
            delay_1ms (5);
        } 
				else 
				{
            break;
        }
    }
    SET_SPI1_NSS_HIGH;
    EEPROM_SPI_WaitStandbyState();//等待写入完成
    EEPROM_WriteDisable();
    if (SET == spi_i2s_flag_get(SPI1, SPI_FLAG_TRANS)) {
        return EEPROM_STATUS_ERROR;
    } else {
        return EEPROM_STATUS_COMPLETE;
    }
}

/**
  * @brief  Sends a byte through the SPI interface and return the byte received
*         from the SPI bus.发送一个字节，接收一个字节
  *
  * @param  byte: byte to send.
  * @retval The value of the received byte.
  */
uint8_t EEPROM_SendByte(uint8_t byte) 
{
    uint8_t answerByte;
    while(RESET == spi_i2s_flag_get(SPI1, SPI_STAT_TBE)) {
        delay_1ms  (1);
    }
		spi_i2s_data_transmit(SPI1, byte);

     while(RESET == spi_i2s_flag_get(SPI1, SPI_FLAG_RBNE)) {
       delay_1ms  (1);
    }
		answerByte = spi_i2s_data_receive(SPI1);
    return (uint8_t)answerByte;
}
/**
  * @brief  Enables the write access to the EEPROM.
  *         写使能
  * @param  None
  * @retval None
  */
void EEPROM_WriteEnable(void) 
{
    SET_SPI1_NSS_LOW;
    uint8_t command[1] = { EEPROM_WREN };//00000110
    EEPROM_SPI_SendInstruction((uint8_t*)command, 1);
    SET_SPI1_NSS_HIGH;
}

/**
  * @brief  Disables the write access to the EEPROM.
  *         写失能
  * @param  None
  * @retval None
  */
void EEPROM_WriteDisable(void)
{
    SET_SPI1_NSS_LOW;
    uint8_t command[1] = { EEPROM_WRDI };//00000100
    EEPROM_SPI_SendInstruction((uint8_t*)command, 1);
    SET_SPI1_NSS_HIGH;
}

/**
  * @brief  Write new value in EEPROM Status Register.
  *         
  * @param  regval : new value of register
  * @retval None
  */
void EEPROM_WriteStatusRegister(uint8_t regval) 
{
    uint8_t command[2];
    command[0] = EEPROM_WRSR;
    command[1] = regval;
    EEPROM_WriteEnable();
    SET_SPI1_NSS_LOW;
    EEPROM_SPI_SendInstruction((uint8_t*)command, 2);
    SET_SPI1_NSS_HIGH;
    EEPROM_WriteDisable();	
  	EEPROM_SPI_WaitStandbyState();//等待操作完成
}

/**
  * @brief  Read EEPROM Status Register Instruction.
  *         
  * @param  none
* @retval answerByte:SR值
  */

uint8_t EEPROM_ReadStatusRegister(void)
{
   uint8_t answerByte;
    SET_SPI1_NSS_LOW;	
    answerByte = EEPROM_SendByte(EEPROM_RDSR);
    SET_SPI1_NSS_HIGH;	
    return answerByte;	   
}	

/**
  * @brief  Polls the status of the Write In Progress (WIP) flag in the EEPROM's
  *         status register and loop until write operation has completed.
  *         等待操作完成，进入备用状态
  *
  * @param  None
  * @retval None
  */
uint8_t EEPROM_SPI_WaitStandbyState(void)
{
    uint8_t EEPROMstatus[1] = { 0x00 };
    uint8_t command[1] = { EEPROM_RDSR };//读状态寄存器
    SET_SPI1_NSS_LOW;
    EEPROM_SPI_SendInstruction((uint8_t*)command, 1);//发送读状态寄存器命令
    do {
			
			    EEPROMstatus[0]=DRV_SPI_SwapByte(Dummy_Byte);

      } while ((EEPROMstatus[0] & EEPROM_WIP_BUSY) == SET); // Write in progress
    SET_SPI1_NSS_HIGH;
    return 0;
}

/**       发送指令
 * @brief Low level function to send header data to EEPROM
 *
 * @param instruction array of bytes to send
 * @param size        data size in bytes
 */
void EEPROM_SPI_SendInstruction(uint8_t *instruction, uint8_t size)
{
	uint8_t send_n=0;
    while(SET == spi_i2s_flag_get(SPI1, SPI_FLAG_TRANS))
     {delay_1ms(1);}/*!< transmit buffer empty flag */ //等待SPI初始化完成		
		while(send_n < size)
		{
			DRV_SPI_SwapByte(instruction[send_n]);
			send_n++;
		}
}
